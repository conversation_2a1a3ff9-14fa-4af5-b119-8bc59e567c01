import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/home/<USER>/controller/notification_controller.dart';

import '../features/onekitty/screens/pages/home/<USER>/model/notification_model.dart';

class PushNotificationService {
  final FirebaseMessaging fcm;
  final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  PushNotificationService(this.fcm);

  Future<void> setupInteractedMessage() async {
    if (kIsWeb) return; // Skip on web
    
    try {
      await _setupNotifications();
      
      FirebaseMessaging.onMessage.listen((RemoteMessage message) {
        _showNotification(
          message.notification?.title ?? 'New Message',
          message.notification?.body ?? '',
        ).whenComplete((){
          Get.put(NotificationController()).addNotification(AppNotification(
            id: message.messageId ?? '',
            created: DateTime.now(),
            received: DateTime.now(),
            title: message.notification?.title ?? 'New Message',
            subtitle: message.notification?.body ?? '',
            actions: [],
            read: false,
            leadingIcon: Icons.message,
            importance: NotificationImportance(
              level: ImportanceLevel.medium,
              priority: 2,
            ),
          ));
        });
      });
    } catch (e) {
      debugPrint('Notification setup failed: $e');
    }
  }

  Future<void> _setupNotifications() async {
    if (kIsWeb) return; // Skip on web
    
    try {
      const AndroidInitializationSettings androidSettings =
          AndroidInitializationSettings('@mipmap/ic_launcher');
      const DarwinInitializationSettings iosSettings =
          DarwinInitializationSettings(
        requestSoundPermission: true,
        defaultPresentSound: true,
      );

      await flutterLocalNotificationsPlugin.initialize(
        const InitializationSettings(
          android: androidSettings,
          iOS: iosSettings,
        ),
      );

      const AndroidNotificationChannel channel = AndroidNotificationChannel(
        'Onekitty',
        'High Importance Notifications',
        description: 'This channel is used for important notifications.',
        importance: Importance.high,
        playSound: true,
        sound: RawResourceAndroidNotificationSound('onekittynotification'),
        enableVibration: true,
      );

      await flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>()
          ?.createNotificationChannel(channel);

      try {
        await flutterLocalNotificationsPlugin
            .resolvePlatformSpecificImplementation<
                AndroidFlutterLocalNotificationsPlugin>()
            ?.requestNotificationsPermission();
      } catch (e) {
        debugPrint('Permission request failed: $e');
      }

      try {
        await fcm.requestPermission(
          alert: true,
          badge: true,
          sound: true,
        );
      } catch (e) {
        debugPrint('FCM permission request failed: $e');
      }
    } catch (e) {
      debugPrint('Notification setup failed: $e');
    }
  }

  Future<void> _showNotification(String title, String body) async {
      AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
      'Onekitty',
      'High Importance Notifications',
      channelDescription: 'This channel is used for important notifications.',
      importance: Importance.high,
      priority: Priority.high,
      playSound: true,
      sound: const RawResourceAndroidNotificationSound('onekittynotification'),
      enableVibration: true,
      vibrationPattern: Int64List.fromList([0, 1000, 500, 1000]),
      icon: '@mipmap/ic_launcher',
    );

    const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
      presentSound: true,
    );

    await flutterLocalNotificationsPlugin.show(
      DateTime.now().millisecondsSinceEpoch % 100000,
      title,
      body,
        NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      ),
    );
  }
}

// Keep this for backward compatibility but don't use it
// ignore: unused_element
void _setupNotificationsInIsolate(_) async {
  // This function is kept for backward compatibility
  // but its content has been moved to the main thread
  debugPrint('Notification setup should be done on the main thread, not in isolate');
}

class FirebaseServices {
  final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();
  final messaging = FirebaseMessaging.instance;

  Future<void> showNotification({
    required String title,
    required String body,
  }) async {
      AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
      'Onekitty',
      'High Importance Notifications',
      channelDescription: 'This channel is used for important notifications.',
      importance: Importance.high,
      priority: Priority.high,
      playSound: true,
      sound: const RawResourceAndroidNotificationSound('onekittynotification'),
      enableVibration: true,
      vibrationPattern: Int64List.fromList([0, 1000, 500, 1000]),
      icon: '@mipmap/ic_launcher',
    );

    const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
      presentSound: true,
    );

    await flutterLocalNotificationsPlugin.show(
      DateTime.now().millisecondsSinceEpoch,
      title,
      body,
        NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      ),
    );
  }
}