import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onekitty_admin/features/admin/api_urls.dart';
import 'package:onekitty_admin/models/chama/chama_model.dart';
import '../../../services/http_service.dart';

class ChamasAdminService {
  final HttpService _apiProvider = Get.find<HttpService>();
  final Logger _logger = Get.find<Logger>();

  /// Fetch chamas with pagination and filtering
  Future<Map<String, dynamic>> fetchChamas({
    int page = 0,
    int size = 15,
    String? search,
    String? chamaId,
    String? frequency,
    String? kittyId,
    String? startDate,
    String? endDate,
  }) async {
    try {
      // Build query parameters
      final Map<String, dynamic> queryParams = {
        'page': page,
        'size': size,
      };

      if (search != null && search.isNotEmpty) {
        queryParams['search'] = search;
      }
      if (chamaId != null && chamaId.isNotEmpty) {
        queryParams['chama_id'] = chamaId;
      }
      if (frequency != null && frequency.isNotEmpty) {
        queryParams['frequency'] = frequency;
      }
      if (kittyId != null && kittyId.isNotEmpty) {
        queryParams['kitty_id'] = kittyId;
      }
      if (startDate != null && startDate.isNotEmpty) {
        queryParams['start_date'] = startDate;
      }
      if (endDate != null && endDate.isNotEmpty) {
        queryParams['end_date'] = endDate;
      }

      // Build URL with query parameters
      final queryString = queryParams.entries
          .map((e) => '${e.key}=${Uri.encodeComponent(e.value.toString())}')
          .join('&');

      final url = '${AdminApiUrls.getAllChamasAdmin}?$queryString';

      final response = await _apiProvider.request(
        method: Method.GET,
        url: url,
      );

      if (response.data['status'] ?? false) {
        final data = response.data['data'];
        final items = data['items'] as List;

        final chamas = items
            .map((item) => Chama.fromJson(item))
            .toList();

        return {
          'success': true,
          'chamas': chamas,
          'pagination': {
            'page': data['page'] ?? page,
            'size': data['size'] ?? size,
            'total_pages': data['total_pages'] ?? 1,
            'total_items': data['total_items'] ?? 0,
            'has_next': !(data['last'] ?? true),
            'has_previous': !(data['first'] ?? true),
            'is_last': data['last'] ?? true,
            'is_first': data['first'] ?? true,
          },
          'message': response.data['message'] ?? 'Chamas fetched successfully',
        };
      } else {
        return {
          'success': false,
          'chamas': <Chama>[],
          'message': response.data['message'] ?? 'Failed to fetch chamas',
        };
      }
    } catch (e) {
      _logger.e('Error fetching chamas: $e');
      return {
        'success': false,
        'chamas': <Chama>[],
        'message': 'An error occurred while fetching chamas: ${e.toString()}',
      };
    }
  }

  /// Fetch single chama details
  Future<Map<String, dynamic>> fetchChamaById(int chamaId) async {
    try {
      final response = await _apiProvider.request(
        method: Method.GET,
        url: '${AdminApiUrls.getChamaDetails}?chama_id=$chamaId',
      );

      if (response.data['status'] ?? false) {
        return {
          'success': true,
          'chama': response.data['data'],
          'message': response.data['message'] ?? 'Chama details fetched successfully',
        };
      } else {
        return {
          'success': false,
          'chama': null,
          'message': response.data['message'] ?? 'Failed to fetch chama details',
        };
      }
    } catch (e) {
      _logger.e('Error fetching chama details: $e');
      return {
        'success': false,
        'chama': null,
        'message': 'An error occurred while fetching chama details: ${e.toString()}',
      };
    }
  }

  /// Send funds to beneficiaries
  Future<Map<String, dynamic>> sendFundsToBeneficiaries({
    required int chamaId,
  }) async {
    try {
      final response = await _apiProvider.request(
        method: Method.POST,
        url: AdminApiUrls.sendFundsToBeneficiaries,
        params: {'chama_id': chamaId},
      );

      return {
        'success': response.data['status'] ?? false,
        'message': response.data['message'] ?? 'Send funds failed',
        'data': response.data['data'],
      };
    } catch (e) {
      _logger.e('Error sending funds to beneficiaries: $e');
      return {
        'success': false,
        'message': 'An error occurred while sending funds: ${e.toString()}',
        'data': null,
      };
    }
  }

  /// Add general penalty to chama
  Future<Map<String, dynamic>> addGeneralPenalty({
    required int chamaId,
    required double amount,
    required String reason,
    String? description,
  }) async {
    try {
      final params = {
        'chama_id': chamaId,
        'amount': amount,
        'reason': reason,
      };

      if (description != null && description.isNotEmpty) {
        params['description'] = description;
      }

      final response = await _apiProvider.request(
        method: Method.POST,
        url: AdminApiUrls.addGeneralPenalty,
        params: params,
      );

      return {
        'success': response.data['status'] ?? false,
        'message': response.data['message'] ?? 'Add penalty failed',
        'data': response.data['data'],
      };
    } catch (e) {
      _logger.e('Error adding general penalty: $e');
      return {
        'success': false,
        'message': 'An error occurred while adding penalty: ${e.toString()}',
        'data': null,
      };
    }
  }

  /// Block chama
  Future<Map<String, dynamic>> blockChama({
    required int chamaId,
    required String reason,
  }) async {
    try {
      final response = await _apiProvider.request(
        method: Method.POST,
        url: AdminApiUrls.blockChama,
        params: {
          'chama_id': chamaId,
          'reason': reason,
        },
      );

      return {
        'success': response.data['status'] ?? false,
        'message': response.data['message'] ?? 'Block chama failed',
        'data': response.data['data'],
      };
    } catch (e) {
      _logger.e('Error blocking chama: $e');
      return {
        'success': false,
        'message': 'An error occurred while blocking chama: ${e.toString()}',
        'data': null,
      };
    }
  }

  /// Unblock chama
  Future<Map<String, dynamic>> unblockChama(int chamaId) async {
    try {
      final response = await _apiProvider.request(
        method: Method.POST,
        url: AdminApiUrls.unblockChama,
        params: {'chama_id': chamaId},
      );

      return {
        'success': response.data['status'] ?? false,
        'message': response.data['message'] ?? 'Unblock chama failed',
        'data': response.data['data'],
      };
    } catch (e) {
      _logger.e('Error unblocking chama: $e');
      return {
        'success': false,
        'message': 'An error occurred while unblocking chama: ${e.toString()}',
        'data': null,
      };
    }
  }

  /// Fetch chama transactions
  Future<Map<String, dynamic>> fetchChamaTransactions({
    required int chamaId,
    int page = 0,
    int size = 15,
    String? filters,
  }) async {
    try {
      final url = '${AdminApiUrls.getChamaTransactions}?chama_id=$chamaId&page=$page&size=$size${filters != null ? '&$filters' : ''}';

      final response = await _apiProvider.request(
        method: Method.GET,
        url: url,
      );

      if (response.data['status'] ?? false) {
        final data = response.data['data'];

        return {
          'success': true,
          'transactions': data['transactions'] ?? [],
          'pagination': {
            'page': data['page'] ?? page,
            'size': data['size'] ?? size,
            'total_pages': data['total_pages'] ?? 1,
            'total_items': data['total_items'] ?? 0,
          },
          'message': response.data['message'] ?? 'Transactions fetched successfully',
        };
      } else {
        return {
          'success': false,
          'transactions': [],
          'message': response.data['message'] ?? 'Failed to fetch transactions',
        };
      }
    } catch (e) {
      _logger.e('Error fetching chama transactions: $e');
      return {
        'success': false,
        'transactions': [],
        'message': 'An error occurred while fetching transactions: ${e.toString()}',
      };
    }
  }

  /// Export chamas data
  Future<Map<String, dynamic>> exportChamasData({
    required String format,
    Map<String, dynamic>? filters,
  }) async {
    try {
      final response = await _apiProvider.request(
        method: Method.POST,
        url: AdminApiUrls.exportData,
        params: {
          'type': 'chamas',
          'format': format,
          'filters': filters ?? {},
        },
      );

      return {
        'success': response.data['status'] ?? false,
        'download_url': response.data['data']?['download_url'],
        'message': response.data['message'] ?? 'Export failed',
      };
    } catch (e) {
      _logger.e('Error exporting chamas data: $e');
      return {
        'success': false,
        'message': 'An error occurred while exporting data: ${e.toString()}',
      };
    }
  }
}
