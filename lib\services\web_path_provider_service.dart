import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart' as path_provider;

/// Web-compatible path provider service that handles platform differences
class WebPathProviderService {
  static const String _webTempPath = '/tmp';
  static const String _webDocumentsPath = '/documents';
  static const String _webDownloadsPath = '/downloads';
  static const String _webApplicationSupportPath = '/app_support';

  /// Get application documents directory with web fallback
  static Future<Directory> getApplicationDocumentsDirectory() async {
    if (kIsWeb) {
      // For web, return a mock directory that won't be used for actual file operations
      return Directory(_webDocumentsPath);
    } else {
      return await path_provider.getApplicationDocumentsDirectory();
    }
  }

  /// Get temporary directory with web fallback
  static Future<Directory> getTemporaryDirectory() async {
    if (kIsWeb) {
      // For web, return a mock directory
      return Directory(_webTempPath);
    } else {
      return await path_provider.getTemporaryDirectory();
    }
  }

  /// Get downloads directory with web fallback
  static Future<Directory?> getDownloadsDirectory() async {
    if (kIsWeb) {
      // For web, return null as downloads are handled differently
      return null;
    } else {
      return await path_provider.getDownloadsDirectory();
    }
  }

  /// Get application support directory with web fallback
  static Future<Directory> getApplicationSupportDirectory() async {
    if (kIsWeb) {
      // For web, return a mock directory
      return Directory(_webApplicationSupportPath);
    } else {
      return await path_provider.getApplicationSupportDirectory();
    }
  }

  /// Check if we're running on web platform
  static bool get isWeb => kIsWeb;

  /// Get a safe file path for web or mobile
  static String getSafeFilePath(String directory, String filename) {
    if (kIsWeb) {
      // For web, just return the filename as we'll handle downloads differently
      return filename;
    } else {
      return '$directory/$filename';
    }
  }
}
