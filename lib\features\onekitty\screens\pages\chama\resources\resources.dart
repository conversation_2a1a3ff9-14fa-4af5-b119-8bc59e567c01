import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:onekitty_admin/helpers/colors.dart';
import 'package:onekitty_admin/helpers/show_toast.dart';
import 'package:onekitty_admin/models/chama/resource_model.dart';
import 'package:onekitty_admin/features/onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/chama/resources/viewMedia.dart';
import 'package:onekitty_admin/nav_routes/nav_routes.dart';
import 'package:onekitty_admin/utils/size_config.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../../../../utils/utils_exports.dart';

class ResourcesWidget extends StatefulWidget {
  const ResourcesWidget({super.key});

  @override
  State<ResourcesWidget> createState() => _ResourcesWidgetState();
}

class _ResourcesWidgetState extends State<ResourcesWidget> {
  final ChamaDataController dataController = Get.put(ChamaDataController());
  final ChamaController chamaController = Get.find<ChamaController>();
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: Padding(
          padding: EdgeInsets.only(left: 15.0.w, right: 15.w),
          child: Column(
            children: [
              const RowAppBar(),
              SizedBox(
                height: 20.h,
              ),
              Text(
                'documents'.tr,
                style: Theme.of(context)
                    .textTheme
                    .titleLarge
                    ?.copyWith(fontWeight: FontWeight.bold, fontSize: 22),
              ),
              SizedBox(
                height: 5.h,
              ),
              Container(
                width: 350.w,
                margin: EdgeInsets.symmetric(horizontal: 10.w),
                child: Text(
                  'share_documents_description'.tr,
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                  style: CustomTextStyles.bodyLargeOnPrimaryContainer,
                ),
              ),
              SizedBox(
                height: 20.h,
              ),
              GetX(
                init: ChamaController(),
                initState: (state) {
                  Future.delayed(Duration.zero, () async {
                    try {
                      await chamaController.getResources(
                          chamaId: dataController.singleChamaDts.value.id);
                    } catch (e) {}
                  });
                },
                builder: (ChamaController chamaController) {
                  if (chamaController.isgetRes.isTrue) {
                    return SizedBox(
                      height: SizeConfig.screenHeight * .33,
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SpinKitDualRing(
                              color: ColorUtil.blueColor,
                              lineWidth: 4.sp,
                              size: 40.0.sp,
                            ),
                            Text(
                              'loading'.tr,
                              style: const TextStyle(
                                color: Colors.white,
                                fontStyle: FontStyle.italic,
                              ),
                            )
                          ],
                        ),
                      ),
                    );
                  }
                  if (chamaController.resources.isEmpty) {
                    return Text('no_documents_added'.tr);
                  }
                  if (chamaController.resources.isNotEmpty) {
                    return SizedBox(
                      height: SizeConfig.screenHeight * .60,
                      child: ListView.separated(
                        separatorBuilder: (context, index) {
                          return SizedBox(
                            height: 8.h,
                          );
                        },
                        itemCount: chamaController.resources.length,
                        itemBuilder: (context, index) {
                          final resource = chamaController.resources[index];
                          return Resource(
                            resourceDts: resource,
                          );
                        },
                      ),
                    );
                  }
                  return   Center(
                    child: Text(
                      'error_getting_documents'.tr,
                      style: const TextStyle(
                        color: Colors.red,
                        fontSize: 16.0,
                      ),
                    ),
                  );
                },
              ),
              SizedBox(
                height: 20.h,
              ),
              //if (dataController.chama.value.member?.role == "CHAIRPERSON" || dataController.chama.value.member?.role == "SECRETARY")
              if (dataController.chama.value.member?.role != "MEMBER")
                _buildButtons(context),
            ],
          ),
        ),
      ),
    );
  }
}

class Resource extends StatelessWidget {
  final ResourceDts resourceDts;
  final ChamaController chamaController = Get.put(ChamaController());
  final ChamaDataController dataController = Get.put(ChamaDataController());
  Resource({
    super.key,
    required this.resourceDts,
  });
  Future<void> openMedia(String url) async {
    try {
      await launchUrl(Uri.parse(url));
    } catch (e) {}
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomImageView(
              imagePath: (resourceDts.type == "PDF")
                  ? AssetUrl.pdf
                  : AssetUrl.resources,
              height: 40,
              width: 40,
              radius: BorderRadius.circular(20),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    resourceDts.title ?? "",
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    resourceDts.description ?? "",
                    style: const TextStyle(
                      fontSize: 14,
                    ),
                  ),
                  SizedBox(height: 14.h),
                  InkWell(
                    onTap: () {
                      (resourceDts.type == "PDF")
                          ? Get.to(
                              () => PDFScreen(
                                path: resourceDts.mediaUrl ?? "",
                              ),
                            )
                          : openMedia(resourceDts.mediaUrl ?? "");
                    },
                    child: Row(
                      children: [
                        Text(
                          resourceDts.type ?? "",
                          style: TextStyle(
                              fontSize: 14.sp,
                              color: AppColors.midBlue,
                              decoration: TextDecoration.underline),
                        ),
                        SizedBox(width: 4.w),
                        CustomImageView(
                          imagePath: AssetUrl.extlink,
                          height: 15.h,
                          width: 15.w,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            //if (dataController.chama.value.member?.role == "CHAIRPERSON" || dataController.chama.value.member?.role == "SECRETARY")
            if (dataController.chama.value.member?.role != "MEMBER")
              InkWell(
                onTap: () {
                  final controller = Get.put(ChamaDataController());
                  controller.singleRs.value = resourceDts;
                  Get.toNamed(NavRoutes.updateRs);
                },
                child: const Icon(Icons.edit),
              ),
            SizedBox(
              width: 15.w,
            ),
            //if (dataController.chama.value.member?.role == "CHAIRPERSON" || dataController.chama.value.member?.role == "SECRETARY")
            if (dataController.chama.value.member?.role != "MEMBER")
              InkWell(
                onTap: () async {
                  bool confirm = await showConfirmationDialog(context);
                  if (confirm) {
                    try {
                      final res = await chamaController.RmResource(
                        chId: resourceDts.chamaId ?? 0,
                        rsId: resourceDts.id ?? 0,
                      );

                      if (res) {
                        ToastUtils.showSuccessToast(context,
                            chamaController.apiMessage.toString(), 'success'.tr);
                        chamaController.getResources(
                            chamaId: resourceDts.chamaId ?? 0);
                      } else {
                        ToastUtils.showErrorToast(context,
                            chamaController.apiMessage.toString(), 'error'.tr);
                      }
                    } catch (e) {
                      ToastUtils.showErrorToast(context,
                          chamaController.apiMessage.toString(), 'error'.tr);
                    }
                  }
                },
                child: CustomImageView(
                  imagePath: AssetUrl.imgIconoirCancel,
                  height: 20.h,
                  width: 20.w,
                  radius: BorderRadius.circular(20),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Future<bool> showConfirmationDialog(BuildContext context) async {
    return await showDialog<bool>(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: Text('confirm_deletion'.tr),
              content:
                  Text('delete_document_confirmation'.tr),
              actions: <Widget>[
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop(false);
                  },
                  child: Text('cancel'.tr),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop(true);
                  },
                  child: Text('delete'.tr),
                ),
              ],
            );
          },
        ) ??
        false;
  }
}

Widget _buildButtons(BuildContext context) {
  return Row(
    mainAxisAlignment: MainAxisAlignment.spaceBetween,
    children: [
      OutlinedButton(
        onPressed: () {
          Get.back();
        },
        child: Text('back'.tr),
      ),
      CustomKtButton(
        onPress: () {
          Get.toNamed(NavRoutes.uploadResource);
        },
        width: 130,
        height: 40,
        btnText: 'add_document'.tr,
      )
    ],
  );
}
