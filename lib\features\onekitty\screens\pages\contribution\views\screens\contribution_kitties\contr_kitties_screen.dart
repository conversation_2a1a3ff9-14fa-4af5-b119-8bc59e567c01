import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/controllers/kitty_controller.dart';
import 'package:onekitty_admin/helpers/colors.dart';
import 'package:onekitty_admin/models/kitty/kitty_categories_model.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/views/screens/create_or_contri_kitty_screen.dart';
import 'package:onekitty_admin/nav_routes/nav_routes.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import '../../../controllers/user_ktty_controller.dart';
import '../../../../../../../../../utils/utils_exports.dart';
import 'widgets/single_kitty_card.dart';

class MyKittiesScreen extends StatefulWidget {
  const MyKittiesScreen({super.key});

  @override
  State<MyKittiesScreen> createState() => _MyKittiesScreenState();
}

class _MyKittiesScreenState extends State<MyKittiesScreen> {
  DateTime? startDate, endDate;
  TextEditingController searchController = TextEditingController();

  final UserKittyController userController = Get.find<UserKittyController>();
  final KittyController kittyController = Get.put(KittyController());

  Timer? _debounce;

  final RefreshController _refreshController = RefreshController(initialRefresh: false);

  @override
  void initState() {
    super.initState();
    
    // Load initial data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (userController.kitties.isEmpty) {
        userController.loadKitties();
      }
      if (kittyController.kittyCategories.isEmpty) {
        kittyController.getKittyCategories();
      }
    });
  }

  @override
  void dispose() {
    searchController.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  // Refresh handler
  void _onRefresh() async {
    searchController.clear();
    await userController.refreshKitties();
    _refreshController.refreshCompleted();
  }

  // Load more handler
  void _onLoading() async {
    await userController.loadMoreKitties();
    _refreshController.loadComplete();
  }

  // Search handler with debounce
  void _handleSearch(String query) {
    if (_debounce?.isActive ?? false) _debounce?.cancel();

    _debounce = Timer(const Duration(milliseconds: 500), () {
      userController.searchKitties(query);
    });
  }

  // Show filter dialog
  void _showFilterDialog() {
    final searchCtrl = TextEditingController(
      text: userController.currentFilters['search'] ?? ''
    );
    final startDateCtrl = TextEditingController(
      text: userController.currentFilters['start_date'] ?? ''
    );
    final endDateCtrl = TextEditingController(
      text: userController.currentFilters['end_date'] ?? ''
    );

    String selectedStatus = _getStatusDisplayValue(
      userController.currentFilters['status'] ?? ''
    );

    String selectedCategoryId = userController.currentFilters['category'] ?? '';
    KittyCategoriesModel? selectedCategory = _getCategoryById(selectedCategoryId);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => StatefulBuilder(
        builder: (context, setModalState) => Container(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.8,
          ),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildFilterHeader(context),
              Divider(
                color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
                thickness: 1,
                height: 1,
              ),
              Flexible(
                child: SingleChildScrollView(
                  padding: EdgeInsets.all(24.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildEnhancedStatusDropdown(
                        selectedStatus: selectedStatus,
                        onChanged: (value) {
                          setModalState(() {
                            selectedStatus = value ?? '';
                          });
                        },
                        context: context,
                      ),
                      SizedBox(height: 20.h),
                      _buildEnhancedCategoryDropdown(
                        selectedCategory: selectedCategory,
                        onChanged: (category) {
                          setModalState(() {
                            selectedCategory = category;
                          });
                        },
                        context: context,
                      ),
                      SizedBox(height: 20.h),
                      Text(
                        'date_range_text'.tr,
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      SizedBox(height: 12.h),
                      _buildEnhancedDateField(
                        controller: startDateCtrl,
                        label: 'start_date_text'.tr,
                        icon: Icons.calendar_today,
                        context: context,
                        onDateSelected: (date) {
                          startDate = date;
                        },
                      ),
                      SizedBox(height: 16.h),
                      _buildEnhancedDateField(
                        controller: endDateCtrl,
                        label: 'end_date_text'.tr,
                        icon: Icons.event,
                        context: context,
                        onDateSelected: (date) {
                          endDate = date;
                        },
                      ),
                    ],
                  ),
                ),
              ),
              _buildFilterActions(
                context: context,
                onClear: () {
                  Navigator.pop(context);
                  userController.applyFilters({});
                },
                onApply: () {
                  final filters = _buildFiltersMap(
                    searchCtrl.text,
                    selectedStatus,
                    selectedCategory,
                  );
                  Navigator.pop(context);
                  userController.applyFilters(filters);
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Show sort dialog
  void _showSortDialog() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildSortHeader(context),
            Divider(
              color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
              thickness: 1,
              height: 1,
            ),
            Padding(
              padding: EdgeInsets.all(24.w),
              child: Obx(() => Column(
                children: [
                  _buildSortOption(
                    context: context,
                    icon: Icons.arrow_downward,
                    title: 'Newest First',
                    value: '-id',
                    onTap: () {
                      userController.applySort('-id');
                      Navigator.pop(context);
                    },
                  ),
                  _buildSortOption(
                    context: context,
                    icon: Icons.arrow_upward,
                    title: 'Oldest First',
                    value: 'id',
                    onTap: () {
                      userController.applySort('id');
                      Navigator.pop(context);
                    },
                  ),
                ],
              )),
            ),
          ],
        ),
      ),
    );
  }

  // Build filters map
  Map<String, String> _buildFiltersMap(
    String search,
    String status,
    KittyCategoriesModel? category,
  ) {
    final filters = <String, String>{};

    if (search.isNotEmpty) {
      final searchText = search.trim();
      if (int.tryParse(searchText) != null) {
        filters['id'] = searchText;
      } else {
        filters['search'] = searchText;
      }
    }

    if (startDate != null) {
      filters['start_date'] = startDate!.toUtc().toIso8601String();
    }

    if (endDate != null) {
      filters['end_date'] = endDate!.toUtc().toIso8601String();
    }

    if (status.isNotEmpty) {
      filters['status'] = _getStatusFilterValue(status);
    }

    if (category != null && category.id != null) {
      filters['category'] = category.id!.toString();
    }

    return filters;
  }

  // Get status display value
  String _getStatusDisplayValue(String statusCode) {
    switch (statusCode) {
      case '0':
        return 'Active';
      case '5':
        return 'Complete';
      case '1':
        return 'Draft';
      default:
        return '';
    }
  }

  // Get status filter value
  String _getStatusFilterValue(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return '0';
      case 'complete':
        return '5';
      case 'draft':
        return '1';
      default:
        return '';
    }
  }

  // Get category by ID
  KittyCategoriesModel? _getCategoryById(String categoryId) {
    if (categoryId.isEmpty || kittyController.kittyCategories.isEmpty) {
      return null;
    }
    try {
      return kittyController.kittyCategories.firstWhere(
        (cat) => cat.id.toString() == categoryId,
      );
    } catch (e) {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: buildAppBarWithImage(context),
      body: SmartRefresher(
        controller: _refreshController,
        enablePullDown: true,
        enablePullUp: true,
        onRefresh: _onRefresh,
        onLoading: _onLoading,
        child: CustomScrollView(
          controller: userController.scrollController,
          slivers: [
            SliverToBoxAdapter(child: SizedBox(height: 10.h)),
            
            // Header with title and create button
            SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 20.w),
                child: _buildHeader(context),
              ),
            ),
            
            SliverToBoxAdapter(child: SizedBox(height: 24.h)),
            
            // Search bar and filter buttons
            SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 20.w),
                child: _buildSearchBar(context),
              ),
            ),
            
            SliverToBoxAdapter(child: SizedBox(height: 20.h)),
            
            // Main content
            Obx(() => _buildContent(context)),
          ],
        ),
      ),
    );
  }

  // Build header
  Widget _buildHeader(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          "my_kitties".tr,
          style: theme.textTheme.titleLarge,
          textScaleFactor: 0.8,
        ),
        _buildCreateButton(context),
      ],
    );
  }

  // Build create button
  Widget _buildCreateButton(BuildContext context) {
    return CustomElevatedButton(
      isDisabled: false,
      onPressed: () => Get.toNamed(NavRoutes.createkittyScreen),
      width: 160.w,
      height: 40.h,
      text: "create_a_kitty".tr,
      buttonTextStyle: TextStyle(
        fontSize: 12.h,
        color: Colors.white,
        fontWeight: FontWeight.bold,
      ),
      leftIcon: Container(
        margin: EdgeInsets.only(right: 8.w),
        child: CustomImageView(
          imagePath: AssetUrl.imgPlus,
          height: 18.h,
          width: 18.w,
        ),
      ),
    );
  }

  // Build search bar
  Widget _buildSearchBar(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: TextFormField(
            controller: searchController,
            decoration: InputDecoration(
              prefixIcon: const Icon(Icons.search),
              suffixIcon: Obx(() {
                if (userController.searchLoading.value) {
                  return const SizedBox(
                    height: 20,
                    width: 20,
                    child: Padding(
                      padding: EdgeInsets.all(12.0),
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                  );
                }
                if (searchController.text.isNotEmpty) {
                  return IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: () {
                      searchController.clear();
                      userController.searchKitties('');
                    },
                  );
                }
                return const SizedBox();
              }),
              hintText: "search_for_kitty".tr,
            ),
            onChanged: _handleSearch,
          ),
        ),
        IconButton(
          onPressed: _showSortDialog,
          icon: Obx(() => Icon(
            Icons.sort,
            color: userController.currentSort.value != '-id'
                ? theme.colorScheme.primary
                : null,
          )),
          tooltip: 'Sort kitties',
        ),
        IconButton(
          onPressed: _showFilterDialog,
          icon: Obx(() => Icon(
            Icons.filter_alt_outlined,
            color: userController.currentFilters.isNotEmpty
                ? theme.colorScheme.primary
                : null,
          )),
          tooltip: 'filter_kitties_tooltip'.tr,
        ),
      ],
    );
  }

  // Build main content
  Widget _buildContent(BuildContext context) {
    // Search loading
    if (userController.searchLoading.value) {
      return SliverFillRemaining(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SpinKitDualRing(
                color: ColorUtil.blueColor,
                lineWidth: 4.sp,
                size: 40.0.sp,
              ),
              SizedBox(height: 16.h),
              Text("searching_kitties".tr),
            ],
          ),
        ),
      );
    }

    // Initial loading
    if (userController.kittiesLoading.value && userController.kitties.isEmpty) {
      return SliverFillRemaining(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SpinKitDualRing(
                color: ColorUtil.blueColor,
                lineWidth: 4.sp,
                size: 40.0.sp,
              ),
              SizedBox(height: 16.h),
              Text("loading_kitties".tr),
            ],
          ),
        ),
      );
    }

    // Empty state
    if (userController.kitties.isEmpty) {
      if (userController.searchQuery.isNotEmpty) {
        return SliverFillRemaining(
          child: Center(
            child: Text(
              "${"no_kitties_found_for".tr} '${userController.searchQuery.value}'",
              style: const TextStyle(fontStyle: FontStyle.italic),
            ),
          ),
        );
      }
      return const SliverFillRemaining(
        child: CrtContributionKittyPage(),
      );
    }

    // Kitties list
    return SliverPadding(
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h),
      sliver: SliverList(
        delegate: SliverChildBuilderDelegate(
          (context, index) {
            // Show loading indicator at the end
            if (index == userController.kitties.length) {
              if (userController.loadingMore.value) {
                return Padding(
                  padding: EdgeInsets.symmetric(vertical: 16.h),
                  child: Column(
                    children: [
                      const SpinKitDualRing(
                        color: ColorUtil.blueColor,
                        lineWidth: 4,
                        size: 40.0,
                      ),
                      SizedBox(height: 8.h),
                      Text(
                        "loading_more_kitties".tr,
                        style: const TextStyle(fontStyle: FontStyle.italic),
                      ),
                    ],
                  ),
                );
              }

              // End of list message
              if (!userController.hasMoreData.value &&
                  userController.searchQuery.isEmpty) {
                return Padding(
                  padding: EdgeInsets.symmetric(vertical: 16.h),
                  child: Text(
                    "no_more_kitties_to_load".tr,
                    textAlign: TextAlign.center,
                    style: const TextStyle(fontStyle: FontStyle.italic),
                  ),
                );
              }

              return const SizedBox();
            }

            // Regular kitty item
            final kitty = userController.kitties[index];
            return Padding(
              padding: EdgeInsets.only(bottom: 24.h),
              child: ContributionKittyWidget(kitty: kitty),
            );
          },
          childCount: userController.kitties.length +
              (userController.loadingMore.value ||
                      (!userController.hasMoreData.value &&
                          userController.searchQuery.isEmpty)
                  ? 1
                  : 0),
        ),
      ),
    );
  }

  // Build filter header
  Widget _buildFilterHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(24.w, 24.h, 16.w, 16.h),
      child: Row(
        children: [
          Container(
            width: 40.w,
            height: 40.w,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Icon(
              Icons.filter_list,
              size: 20.w,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'filter_kitties_text'.tr,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                ),
                SizedBox(height: 2.h),
                Text(
                  'refine_search_results_text'.tr,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context)
                            .colorScheme
                            .onSurface
                            .withOpacity(0.6),
                      ),
                ),
              ],
            ),
          ),
          InkWell(
            onTap: () => Navigator.pop(context),
            borderRadius: BorderRadius.circular(20.r),
            child: Container(
              padding: EdgeInsets.all(8.w),
              child: Icon(
                Icons.close,
                size: 20.w,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Build sort header
  Widget _buildSortHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(24.w, 24.h, 16.w, 16.h),
      child: Row(
        children: [
          Container(
            width: 40.w,
            height: 40.w,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Icon(
              Icons.sort,
              size: 20.w,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Text(
              'Sort Kitties',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
            ),
          ),
          InkWell(
            onTap: () => Navigator.pop(context),
            borderRadius: BorderRadius.circular(20.r),
            child: Container(
              padding: EdgeInsets.all(8.w),
              child: Icon(
                Icons.close,
                size: 20.w,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Build sort option
  Widget _buildSortOption({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String value,
    required VoidCallback onTap,
  }) {
    final isSelected = userController.currentSort.value == value;
    return ListTile(
      leading: Icon(
        icon,
        color: Theme.of(context).colorScheme.primary,
      ),
      title: Text(title),
      trailing: isSelected
          ? Icon(
              Icons.check,
              color: Theme.of(context).colorScheme.primary,
            )
          : null,
      onTap: onTap,
    );
  }

  // Build filter actions
  Widget _buildFilterActions({
    required BuildContext context,
    required VoidCallback onClear,
    required VoidCallback onApply,
  }) {
    return Container(
      padding: EdgeInsets.fromLTRB(24.w, 16.h, 24.w, 24.h),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton.icon(
              onPressed: onClear,
              icon: Icon(Icons.clear_all, size: 18.w),
              label: Text('clear_all_text'.tr),
              style: OutlinedButton.styleFrom(
                padding: EdgeInsets.symmetric(vertical: 14.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
                side: BorderSide(
                  color: Theme.of(context).colorScheme.outline.withOpacity(0.5),
                ),
              ),
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            flex: 2,
            child: ElevatedButton.icon(
              onPressed: onApply,
              icon: Icon(Icons.check, size: 18.w),
              label: Text(
                'apply_filters_text'.tr,
                style: const TextStyle(color: Colors.white),
              ),
              style: ElevatedButton.styleFrom(
                padding: EdgeInsets.symmetric(vertical: 14.h),
                elevation: 0,
                backgroundColor: AppColors.primary,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Build date field
  Widget _buildEnhancedDateField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    required BuildContext context,
    required Function(DateTime) onDateSelected,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        SizedBox(height: 8.h),
        TextFormField(
          controller: controller,
          readOnly: true,
          onTap: () async {
            final date = await showDatePicker(
              context: context,
              initialDate: DateTime.now(),
              firstDate: DateTime(2020),
              lastDate: DateTime.now().add(const Duration(days: 365)),
            );
            if (date != null) {
              onDateSelected(date);
              controller.text =
                  '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
            }
          },
          decoration: InputDecoration(
            hintText: 'Select date',
            prefixIcon: Icon(icon, size: 20.w),
            suffixIcon: controller.text.isNotEmpty
                ? InkWell(
                    onTap: () => controller.clear(),
                    child: Icon(Icons.clear, size: 18.w),
                  )
                : null,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
            filled: true,
            fillColor: Theme.of(context).colorScheme.surface,
            contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
          ),
        ),
      ],
    );
  }

  // Build status dropdown
  Widget _buildEnhancedStatusDropdown({
    required String selectedStatus,
    required ValueChanged<String?> onChanged,
    required BuildContext context,
  }) {
    final statuses = ['Active', 'Complete', 'Draft'];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'status_text'.tr,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        SizedBox(height: 8.h),
        DropdownButtonFormField<String>(
          value: selectedStatus.isEmpty ? null : selectedStatus,
          onChanged: onChanged,
          decoration: InputDecoration(
            hintText: 'select_status'.tr,
            prefixIcon: Icon(Icons.info_outline, size: 20.w),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
            filled: true,
            fillColor: Theme.of(context).colorScheme.surface,
            contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
          ),
          items: [
            DropdownMenuItem<String>(
              value: '',
              child: Text('all_statuses'.tr),
            ),
            ...statuses.map(
              (status) => DropdownMenuItem<String>(
                value: status,
                child: Row(
                  children: [
                    Container(
                      width: 8.w,
                      height: 8.w,
                      decoration: BoxDecoration(
                        color: _getStatusColor(status, context),
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                    ),
                    SizedBox(width: 8.w),
                    Text(status),
                  ],
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Get status color
  Color _getStatusColor(String status, BuildContext context) {
    switch (status.toLowerCase()) {
      case 'active':
        return Theme.of(context).colorScheme.primary;
      case 'complete':
        return Colors.green;
      case 'draft':
        return Colors.orange;
      default:
        return Theme.of(context).colorScheme.outline;
    }
  }

  // Build category dropdown
  Widget _buildEnhancedCategoryDropdown({
    required KittyCategoriesModel? selectedCategory,
    required ValueChanged<KittyCategoriesModel?> onChanged,
    required BuildContext context,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'category_text'.tr,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        SizedBox(height: 8.h),
        GetX<KittyController>(
          init: kittyController,
          builder: (controller) {
            if (controller.isLoadingKittyCategories.isTrue) {
              return Container(
                height: 55.h,
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
                  ),
                  borderRadius: BorderRadius.circular(12.r),
                  color: Theme.of(context).colorScheme.surface,
                ),
                child: Row(
                  children: [
                    Icon(Icons.category_outlined, size: 20.w),
                    SizedBox(width: 12.w),
                    const Text('Loading categories...'),
                    const Spacer(),
                    SizedBox(
                      width: 16.w,
                      height: 16.w,
                      child: const CircularProgressIndicator(strokeWidth: 2),
                    ),
                  ],
                ),
              );
            }

            return DropdownButtonFormField<KittyCategoriesModel>(
              value: selectedCategory,
              onChanged: onChanged,
              decoration: InputDecoration(
                hintText: 'Select category',
                prefixIcon: Icon(Icons.category_outlined, size: 20.w),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
                filled: true,
                fillColor: Theme.of(context).colorScheme.surface,
                contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
              ),
              items: [
                DropdownMenuItem<KittyCategoriesModel>(
                  value: null,
                  child: Text('All Categories'),
                ),
                ...controller.kittyCategories.map(
                  (category) => DropdownMenuItem<KittyCategoriesModel>(
                    value: category,
                    child: Text(category.name ?? 'unknown_category_text'.tr),
                  ),
                ),
              ],
            );
          },
        ),
      ],
    );
  }
}