import 'dart:convert';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_quill_delta_from_html/flutter_quill_delta_from_html.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mime/mime.dart';
import 'package:onekitty_admin/models/events/categories_model.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/controllers/controllers.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/controllers/edit_event_controller.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/controllers/events_controller.dart';
import 'package:onekitty_admin/helpers/colors.dart';
import 'package:onekitty_admin/helpers/show_toast.dart';
import 'package:onekitty_admin/models/events/events_model.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/views/screens/map_page.dart';
import 'package:onekitty_admin/utils/date_formatter.dart';
import 'package:onekitty_admin/utils/iswysiwyg.dart';
import 'package:onekitty_admin/utils/my_button.dart';
import 'package:flutter_quill/flutter_quill.dart' as q;
import 'package:onekitty_admin/utils/show_cached_network_image.dart';
import 'package:onekitty_admin/utils/show_snackbar.dart';
import 'package:onekitty_admin/utils/themes_colors.dart';
import '/models/events/media_models.dart';
import '/utils/my_text_field.dart';
import '/utils/glassmorphic.dart';
import 'package:onekitty_admin/main.dart' show isLight;

import 'view_single_event_organizer.dart';

class EditEventPage extends StatefulWidget {
  final int id;
  final MyEventsModel myevent;
  const EditEventPage({super.key, required this.id, required this.myevent});

  @override
  State<EditEventPage> createState() => _EditEventPageState();
}

class _EditEventPageState extends State<EditEventPage> {
  final GlobalKey<FormState> _formKey2 = GlobalKey<FormState>();
  final ScrollController scrollController = ScrollController();
  final ValueNotifier<double> scrollPosition = ValueNotifier(0);
  final EditEventController controller = Get.find<EditEventController>();

  @override
  void initState() {
    super.initState();
    scrollController.addListener(() {
      scrollPosition.value = scrollController.offset;
    });
    // Load event data into controller
    Future.delayed(Duration.zero, () {
      controller.loadEventData(widget.myevent);
      // Show media limit warning if needed
      controller.showMediaLimitWarningIfNeeded(context);
    });
  }

  @override
  void dispose() {
    // Clean up resources to prevent memory leaks
    scrollController.removeListener(() {
      scrollPosition.value = scrollController.offset;
    });
    scrollController.dispose();
    // Clear controller variables
    controller.clearVariables();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      // if (controller.isloading.value) {
      //   return  Scaffold(
      //     body: Center(child: CircularProgressIndicator()),
      //   ); // Loading widget
      // }
      final event = controller.event.value;

      final TextEditingController eventTitle =
              TextEditingController(text: event.title),
          // eventDescription = TextEditingController(text: event.description),
          emailAddress = TextEditingController(text: event.email);
      // phoneNumber = TextEditingController(text:event);
      final GlobalKey<FormState> _formKey1 = GlobalKey<FormState>();
      final ValueNotifier<String?> countryCode = ValueNotifier(null);

      //controllers for page 2
      final TextEditingController eventStartDate = TextEditingController(
              text: event.startDate != null
                  ? DateFormat('dd/MM/yyyy hh:mm a')
                      .format(event.startDate?.toLocal() ?? DateTime.now())
                  : ''),
          eventEndDate = TextEditingController(
              text: event.endDate != null
                  ? DateFormat("dd/MM/yyyy hh:mm a")
                      .format(event.endDate?.toLocal() ?? DateTime.now())
                  : ''),
          venue = TextEditingController(text: event.venue),
          location = TextEditingController(text: event.locationTip);

      dynamic document;
      try {
        document = q.Document.fromJson(isWysiwygFormat(event.description)
            ? HtmlToDelta().convert(event.description).toJson()
            : jsonDecode(event.description));
      } catch (_) {
        document = q.Document();
      }
      var selection = const TextSelection.collapsed(offset: 0);
      final eventDescription =
          q.QuillController(document: document, selection: selection);

      return Scaffold(
          persistentFooterButtons: [
            Obx(
              () => MyButton(
                showLoading:
                    controller.isEditing.value || controller.isUploading.value,
                onClick: controller.isUploading.value
                    ? null
                    : () {
                        if (controller.isEditing.value ||
                            controller.isUploading.value) {
                          return;
                        }

                        // Validate media uploads are complete
                        if (controller.eventMedia.any((media) =>
                            media.url == null || media.url!.isEmpty)) {
                          showSnackbar(
                              context: context,
                              label: 'wait_for_media_uploads_complete'.tr);
                          return;
                        }

                        // Validate both forms before submission
                        final isForm1Valid =
                            _formKey1.currentState?.validate() ?? false;
                        final isForm2Valid =
                            _formKey2.currentState?.validate() ?? false;

                        if (!isForm1Valid || !isForm2Valid) {
                          showSnackbar(
                              context: context,
                              label: 'please_fill_all_required_fields'.tr);
                          return;
                        }

                        // Validate that end date is after start date
                        try {
                          final startDate =
                              parseCustomDateTime(eventStartDate.text);
                          final endDate =
                              parseCustomDateTime(eventEndDate.text);

                          if (endDate.isBefore(startDate)) {
                            showSnackbar(
                                context: context,
                                label: 'end_date_must_be_after_start_date'.tr);
                            return;
                          }

                          controller.editEvent(context: context, data: {
                            "ID": event.id,
                            "email": emailAddress.text,
                            "title": eventTitle.text,
                            "description": quilltoHtml(eventDescription),
                            "location_tip": location.text,
                            "venue": venue.text,
                            "latitude": controller.lat.value,
                            "longitude": controller.long.value,
                            "CreatedAt":
                                event.createdAt!.toUtc().toIso8601String(),
                            "UpdatedAt":
                                DateTime.now().toUtc().toIso8601String(),
                            "DeletedAt": null,
                            "phone_number": Get.find<Eventcontroller>()
                                .getLocalUser()
                                ?.phoneNumber,
                            "username":
                                "${event.username} ${DateTime.now().millisecondsSinceEpoch}",
                            "status": "ACTIVE",
                            "category_id": controller.category.value,
                            "kitty_id": widget.myevent.event.kittyId,
                            "start_date": convertToIso8601(eventStartDate.text),
                            "end_date": convertToIso8601(eventEndDate.text),
                          }).whenComplete(() {
                            ViewSingleEventOrganizer.onRefresh(
                                eventId: event.id ?? 0);
                            Navigator.of(context).pop();
                          });
                        } catch (e) {
                          showSnackbar(
                              context: context,
                              label: 'invalid_date_format'.tr);
                        }
                      },
                label: 'edit_event'.tr,
              ),
            ),
          ],
          body: CustomScrollView(controller: scrollController, slivers: [
            SliverAppBar(
                leadingWidth: 88,
                pinned: true,
                backgroundColor: isLight.value
                    ? scaffoldBackgroundColor
                    : Colors.transparent,
                leading: GlassmorphicContainer(
                  onTap: () => Navigator.pop(context),
                  color: Colors.black,
                  blurRadius: 20,
                  cornerRadius: 24.r,
                  child: Row(
                    children: [
                      const Icon(
                        Icons.arrow_back,
                        color: Colors.white,
                        size: 18,
                      ),
                      const SizedBox(width: 8),
                      Text('back'.tr,
                          style: const TextStyle(color: Colors.white))
                    ],
                  ),
                ),
                expandedHeight: 250.h,
                flexibleSpace: FlexibleSpaceBar(
                    background: Stack(alignment: Alignment.center, children: [
                  Positioned.fill(
                    top: 0,
                    child: Obx(() {
                      if (controller.eventMedia.isEmpty) {
                        return Container(
                          height: 250.h,
                          color: Colors.grey.shade200,
                          child: const Center(
                            child: Icon(Icons.image_not_supported,
                                size: 50, color: Colors.grey),
                          ),
                        );
                      }
                      return CarouselView(
                          itemExtent: 390.w,
                          itemSnapping: true,
                          children: controller.eventMedia
                              .where((e) =>
                                  e.url != null &&
                                  e.url!.isNotEmpty &&
                                  Uri.tryParse(e.url!) != null)
                              .map(
                                (e) => ShowCachedNetworkImage(
                                    fit: BoxFit.cover,
                                    height: 250.h,
                                    width: MediaQuery.sizeOf(context).width,
                                    errorWidget: Container(
                                      color: Colors.grey.shade300,
                                      child: const Icon(Icons.broken_image,
                                          size: 50),
                                    ),
                                    imageurl: e.url!),
                              )
                              .toList());
                    }),
                  ),
                ]))),
            SliverPadding(
                padding: const EdgeInsets.all(18),
                sliver: SliverToBoxAdapter(
                  child: Column(children: [
                    Page1(
                        media: event.eventMedia ?? [],
                        event: event,
                        eventTitle: eventTitle,
                        eventDescription: eventDescription,
                        emailAddress: emailAddress,
                        countryCode: countryCode,
                        formKey: _formKey1),
                    Page2(
                        eventStartDate: eventStartDate,
                        eventEndDate: eventEndDate,
                        venue: venue,
                        location: location,
                        formKey: _formKey2),
                    SizedBox(height: 10.h),
                  ]),
                ))
          ]));
    });
  }
}

class Page1 extends StatelessWidget {
  final TextEditingController eventTitle, emailAddress;
  final Event event;
  final GlobalKey<FormState> formKey;
  final ValueNotifier<String?> countryCode;
  final q.QuillController eventDescription;
  final List<EventMedia> media;
  const Page1(
      {super.key,
      required this.eventTitle,
      required this.eventDescription,
      required this.emailAddress,
      required this.countryCode,
      required this.formKey,
      required this.event,
      required this.media});

  @override
  Widget build(BuildContext context) {
    final EditEventController controller = Get.find<EditEventController>();
    return SingleChildScrollView(
      child: Form(
        key: formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
                height: 80.h,
                child: Obx(
                  () => ListView.builder(
                      itemCount: controller.eventMedia.length + 1,
                      scrollDirection: Axis.horizontal,
                      shrinkWrap: true,
                      itemBuilder: (context, index) {
                        if (index == controller.eventMedia.length) {
                          return 
                              InkWell(
                                  onTap: controller.isUploading.value
                                      ? null
                                      : () async {
                                          if (controller.isUploading.value)
                                            return;

                                          // Check media limit before allowing upload
                                          if (controller.isMediaLimitReached) {
                                            ToastUtils.showToast(
                                              'maximum_media_items_allowed'
                                                  .tr
                                                  .replaceAll(
                                                      '{limit}',
                                                      EditEventController
                                                          .maxMediaLimit
                                                          .toString()),
                                              toastType: ToastType.error,
                                            );
                                            return;
                                          }

                                          try {
                                            FilePickerResult? results =
                                                await FilePicker.platform
                                                    .pickFiles(
                                                        allowMultiple: false);
                                            if (results != null) {
                                              // Check file size (limit to 5MB)
                                              final fileSize =
                                                  results.files.first.size;
                                              if (fileSize > 5 * 1024 * 1024) {
                                                ToastUtils.showToast(
                                                    'file_size_exceeds_5mb'.tr);
                                                return;
                                              }

                                              // Check file type
                                              final String mimeType =
                                                  lookupMimeType(results.files
                                                              .first.path ??
                                                          '') ??
                                                      '';
                                              if (!mimeType
                                                  .startsWith('image/')) {
                                                ToastUtils.showToast(
                                                    'please_select_image_file'
                                                        .tr);
                                                return;
                                              }

                                              // Set uploading state
                                              controller.isUploading(true);

                                              try {
                                                // Upload file
                                                final filedetail =
                                                    await controller.uploadFile(
                                                        results
                                                            .files.first.path!);

                                                if (filedetail == null) {
                                                  ToastUtils.showToast(
                                                      'failed_to_upload_image'
                                                          .tr);
                                                  return;
                                                }

                                                // Validate uploaded file URL
                                                final filePath =
                                                    filedetail['file_path']
                                                        ?.toString();
                                                if (filePath == null ||
                                                    filePath.isEmpty) {
                                                  ToastUtils.showToast(
                                                      'upload_completed_invalid_url'
                                                          .tr);
                                                  return;
                                                }

                                                // Validate URL format
                                                if (Uri.tryParse(filePath) ==
                                                    null) {
                                                  ToastUtils.showToast(
                                                      'backend_invalid_url_format'
                                                          .tr);
                                                  return;
                                                }

                                                // Check if media already exists to prevent duplicates
                                                final existingMedia = controller
                                                    .eventMedia
                                                    .where((media) =>
                                                        media.url == filePath)
                                                    .toList();

                                                if (existingMedia.isNotEmpty) {
                                                  ToastUtils.showToast(
                                                      'media_already_exists'
                                                          .tr);
                                                  return;
                                                }

                                                // Update event media via API (adds to existing list)
                                                final result = await controller
                                                    .updateEventMedia(
                                                        event.id ?? 0,
                                                        addMediaUrl: filePath, 
                                                        mediaID: filedetail['ID'] ?? 0);

                                                if (result == false) {
                                                  ToastUtils.showToast(
                                                      'failed_update_event_media'
                                                          .tr);
                                                  return;
                                                }
                                                Get.find<Eventcontroller>().userEvents.firstWhere((e)=> e.event.id == event.id).event.eventMedia = controller.eventMedia.value;
                                                controller.fetchEventDetail(event.id??0, isOrganizer: true);
                                                ToastUtils.showToast(
                                                    'media_uploaded_successfully'
                                                        .tr);
                                              } catch (e) {
                                                ToastUtils.showToast(
                                                    '${'upload_failed'.tr}: ${e.toString()}');
                                              } finally {
                                                controller.isUploading(false);
                                              }
                                            }
                                          } catch (e) {
                                            controller.isUploading(false);
                                            ToastUtils.showToast(
                                                '${'error_uploading_image'.tr}: ${e.toString()}');
                                          }
                                        },
                                  child: Obx(
                                    () => Container(
                                        padding: const EdgeInsets.all(8.0),
                                        height: 70.h,
                                        width: 70.w,
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(4.5),
                                          border:
                                              Border.all(color: Colors.grey),
                                        ),
                                        child: controller.isUploading.value
                                            ? const CircularProgressIndicator
                                                .adaptive()
                                            : const Icon(
                                                Icons.add_a_photo_outlined)),
                                  ),
                                );
                        }
                        if(controller.eventMedia[index].category?.toLowerCase() == 'placeholder'){
                          return const SizedBox();
                        }
                        return Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Stack(children: [
                            ClipRRect(
                              borderRadius: BorderRadius.circular(4.5),
                              child: ShowCachedNetworkImage(
                                  height: 70.h,
                                  width: 70.w,
                                  fit: BoxFit.cover,
                                  errorWidget: Container(
                                    color: Colors.grey.shade300,
                                    child: const Icon(Icons.broken_image,
                                        color: Colors.grey),
                                  ),
                                  imageurl: (controller.eventMedia[index].url !=
                                              null &&
                                          controller.eventMedia[index].url!
                                              .isNotEmpty &&
                                          Uri.tryParse(controller
                                                  .eventMedia[index].url!) !=
                                              null)
                                      ? controller.eventMedia[index].url!
                                      : ''),
                            ),
                            IconButton(
                                onPressed: () {
                                  final mediaToDelete =
                                      controller.eventMedia[index];
                                  showDialog(
                                    context: context,
                                    builder: (BuildContext context) {
                                      return AlertDialog(
                                        title: Text('confirm_deletion'.tr),
                                        content:
                                            Text('confirm_delete_photo'.tr),
                                        actions: <Widget>[
                                          TextButton(
                                            onPressed: () {
                                              Navigator.of(context)
                                                  .pop(); // Close the dialog
                                            },
                                            child: Text('cancel'.tr),
                                          ),
                                          TextButton(
                                            onPressed: () async {
                                              // Show loading indicator
                                              showDialog(
                                                context: context,
                                                barrierDismissible: false,
                                                builder:
                                                    (BuildContext context) {
                                                  return AlertDialog(
                                                    content: Row(
                                                      children: [
                                                        const CircularProgressIndicator(),
                                                        const SizedBox(
                                                            width: 20),
                                                        Text(
                                                            'deleting_media'.tr)
                                                      ],
                                                    ),
                                                  );
                                                },
                                              );

                                              try {
                                                // Remove media from list and update API
                                                final success = await controller
                                                    .deleteEventMedia(
                                                        mediaID: controller
                                                            .eventMedia[index]
                                                            .id!,
                                                        mediaPosition: index,
                                                        context: context);

                                                // Note: Local list is updated automatically in the method

                                                // Close loading dialog
                                                // Navigator.of(context).pop();
                                                // Close confirmation dialog
                                                Navigator.of(context).pop();
                                                // Show success message
                                                ToastUtils.showToast(success
                                                    ? 'media_deleted_successfully'
                                                        .tr
                                                    : 'failed_delete_media'.tr);
                                              } catch (e) {
                                                // Close loading dialog
                                                // Navigator.of(context).pop();
                                                // Close confirmation dialog
                                                Navigator.of(context).pop();
                                                // Show error message
                                                ToastUtils.showToast(
                                                    '${'failed_delete_media'.tr}: ${e.toString()}');
                                              }
                                            },
                                            child: Text('delete'.tr,
                                                style: const TextStyle(
                                                    color: Colors.red)),
                                          ),
                                        ],
                                      );
                                    },
                                  );
                                },
                                icon: Icon(Icons.delete_outlined,
                                    color: Colors.grey.shade400))
                          ]),
                        );
                      }),
                )),
            MyTextFieldwValidator(
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'event_title_required'.tr;
                  }
                  return null;
                },
                controller: eventTitle,
                title: 'event_title'.tr,
                hint: 'kenya_awards_night_example'.tr),
            SizedBox(height: 8.h),
            Text(
              'event_description'.tr,
              style: TextStyle(fontSize: 14.spMin, fontWeight: FontWeight.w600),
            ),
            Padding(
              padding: const EdgeInsets.all(4.0),
              child: Container(
                decoration: ThemeHelper.inputBoxDecoration(),
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 8),
                    q.QuillSimpleToolbar(
                      config: const q.QuillSimpleToolbarConfig(
                        multiRowsDisplay: false,
                      ),
                      controller: eventDescription,
                    ),
                    const SizedBox(height: 15),
                    q.QuillEditor.basic(
                      controller: eventDescription,
                      config: q.QuillEditorConfig(
                        placeholder: 'event_description_placeholder'.tr,

                        // readOnly: false,
                        autoFocus: false,
                        enableInteractiveSelection:
                            true, // Enable interactive selection to allow text editing
                      ),
                    ),
                    const SizedBox(height: 8),
                  ],
                ),
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'category'.tr,
              style: TextStyle(fontSize: 14.spMin, fontWeight: FontWeight.w600),
            ),
            SizedBox(height: 8.h),
            GetX<EditEventController>(initState: (state) async {
              await state.controller?.getCategories();
            }, builder: (controller) {
              if (controller.isLoadingCategories.isTrue) {
                return Container(
                  height: 55.h,
                  padding: const EdgeInsets.all(8),
                  width: double.infinity,
                  decoration: ThemeHelper.inputBoxDecoration(),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('select_category'.tr),
                      const CupertinoActivityIndicator()
                    ],
                  ),
                );
              }

              return DropdownButtonFormField<CategoriesModel>(
                decoration: InputDecoration(
                  hintText: 'select_category'.tr,
                  filled: true,
                  fillColor: AppColors.slate,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(14.r),
                    borderSide: const BorderSide(
                      width: 0.5,
                      color: AppColors.primary,
                    ),
                  ),
                ),
                value: controller.categories.firstWhere(
                  (category) =>
                      category.id == controller.eventcategory.value?.id,
                  orElse: () =>
                      controller.categories.first, // Provide a default value
                ), // Prefill with controller.eventCategory
                items: controller.categories.map((category) {
                  return DropdownMenuItem<CategoriesModel>(
                    value: category,
                    child: Text(category.title!),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    controller.category.value = value.id!; // Update category
                    controller.eventcategory.value = CategoriesModel.fromJson(
                        value.toJson()); // Update event category
                  }
                },
                validator: (value) {
                  if (value == null) {
                    return 'category_is_required'.tr;
                  }
                  return null;
                },
              );
            }),
            SizedBox(height: 16.h),
            MyTextFieldwValidator(
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'email_address_required'.tr;
                  }
                  if (!RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(value)) {
                    return 'enter_valid_email_address'.tr;
                  }

                  return null;
                },
                keyboardType: TextInputType.emailAddress,
                controller: emailAddress,
                title: 'email_address'.tr,
                hint: 'info_awards_example'.tr),
            SizedBox(height: 16.h),
          ],
        ),
      ),
    );
  }
}

class Page2 extends StatelessWidget {
  final TextEditingController eventStartDate, eventEndDate, venue, location;
  final GlobalKey<FormState> formKey;
  const Page2(
      {super.key,
      required this.eventStartDate,
      required this.eventEndDate,
      required this.venue,
      required this.location,
      required this.formKey});

  @override
  Widget build(BuildContext context) {
    final editController =
        Get.find<EditEventController>(); // use the same instance
    return Form(
      key: formKey,
      child: SingleChildScrollView(
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          MyTextFieldwValidator(
              readOnly: true,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'event_start_date_time_required'.tr;
                }
                return null;
              },
              controller: eventStartDate,
              titleStyle:
                  TextStyle(fontSize: 14.spMin, fontWeight: FontWeight.w500),
              iconSuffix: IconButton(
                icon: const Icon(Icons.calendar_month),
                onPressed: () async {
                  DateTime? pickedDateTime = await showDatePicker(
                    context: context,
                    initialDate: DateTime.now(),
                    firstDate: DateTime.now(),
                    lastDate: DateTime.now().add(const Duration(days: 365)),
                  );

                  if (pickedDateTime != null) {
                    TimeOfDay? pickedTime = await showTimePicker(
                      context: context,
                      initialTime: TimeOfDay.now(),
                      builder: (BuildContext context, Widget? child) {
                        return MediaQuery(
                          data: MediaQuery.of(context).copyWith(
                            alwaysUse24HourFormat: false,
                          ),
                          child: child!,
                        );
                      },
                    );

                    if (pickedTime != null) {
                      DateTime finalDateTime = DateTime(
                        pickedDateTime.year,
                        pickedDateTime.month,
                        pickedDateTime.day,
                        pickedTime.hour,
                        pickedTime.minute,
                      );

                      String formattedDateTime =
                          DateFormat('dd/MM/yyyy hh:mm a')
                              .format(finalDateTime);
                      eventStartDate.text = formattedDateTime;
                    }
                  }
                },
              ),
              title: 'event_start_date_time_label'.tr,
              hint: 'date_time_pm_example'.tr),
          SizedBox(height: 8.h),
          MyTextFieldwValidator(
              readOnly: true,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'event_end_date_time_required'.tr;
                }
                return null;
              },
              controller: eventEndDate,
              titleStyle:
                  TextStyle(fontSize: 14.spMin, fontWeight: FontWeight.w500),
              iconSuffix: IconButton(
                icon: const Icon(Icons.calendar_month),
                onPressed: () async {
                  DateTime? pickedDateTime = await showDatePicker(
                    context: context,
                    initialDate: DateTime.now(),
                    firstDate: DateTime.now(),
                    lastDate: DateTime.now().add(const Duration(days: 365)),
                  );

                  if (pickedDateTime != null) {
                    TimeOfDay? pickedTime = await showTimePicker(
                      context: context,
                      initialTime: TimeOfDay.now(),
                      builder: (BuildContext context, Widget? child) {
                        return MediaQuery(
                          data: MediaQuery.of(context).copyWith(
                            alwaysUse24HourFormat: false,
                          ),
                          child: child!,
                        );
                      },
                    );

                    if (pickedTime != null) {
                      DateTime finalDateTime = DateTime(
                        pickedDateTime.year,
                        pickedDateTime.month,
                        pickedDateTime.day,
                        pickedTime.hour,
                        pickedTime.minute,
                      );

                      String formattedDateTime =
                          DateFormat('dd/MM/yyyy hh:mm a')
                              .format(finalDateTime);
                      eventEndDate.text = formattedDateTime;
                    }
                  }
                },
              ),
              title: 'event_end_date_time_label'.tr,
              hint: 'date_time_pm_example'.tr),
          SizedBox(height: 8.h),
          MyTextFieldwValidator(
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'event_venue_is_required'.tr;
                }
                return null;
              },
              controller: venue,
              titleStyle:
                  const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
              title: 'venue_label'.tr,
              hint: 'kicc_example'.tr),
          TextButton(
            onPressed: () async {
              final results = await Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => MapScreen(
                    longitude: editController
                        .long.value, // use existing controller values
                    latitude: editController.lat.value,
                  ),
                ),
              );
              if (results != null) {
                editController.long.value = results['longitude'];
                editController.lat.value = results['latitude'];
              }
            },
            child: Text(
              'choose_on_map'.tr,
              style: TextStyle(
                fontSize: 14.spMin,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          MyTextFieldwValidator(
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'location_is_required'.tr;
                }
                return null;
              },
              controller: location,
              titleStyle:
                  const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
              title: 'location_tip_label'.tr,
              hint: 'search_location'.tr),
        ]),
      ),
    );
  }
}

String getTextFormat(String text) {
  try {
    // Try to parse as Delta JSON
    final decoded = jsonDecode(text);
    if (decoded is Map && decoded.containsKey('ops')) {
      return 'delta';
    }
    return 'wysiwyg';
  } catch (e) {
    // If JSON parsing fails, assume WYSIWYG
    return 'wysiwyg';
  }
}
