import 'dart:async';
import 'dart:io';
import 'dart:ui';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:onekitty_admin/configs/country_specifics.dart';
import 'package:onekitty_admin/features/admin/widgets/events_dashboard.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/controllers/user_ktty_controller.dart';
import 'package:onekitty_admin/helpers/colors.dart';
import 'package:onekitty_admin/helpers/show_toast.dart';
import 'package:onekitty_admin/main.dart' show isLight;
import 'package:animations/animations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:logger/logger.dart';
import 'package:onekitty_admin/models/events/tickets_model.dart' show Ticket;
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/views/screens/edit_kitty/whatsapp_link.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/controllers/controllers.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/controllers/edit_event_controller.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/controllers/view_single_event.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/controllers/vieweventcontroller.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/controllers/kitty_controller.dart';
import 'package:onekitty_admin/features/onekitty/controllers/contribute_controller.dart';
import 'package:onekitty_admin/models/events/events_model.dart';
import 'package:onekitty_admin/models/events/media_models.dart';
import 'package:onekitty_admin/models/contr_kitty_model.dart';
import 'package:onekitty_admin/models/transaction_model.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/views/screens/edit_event.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/views/screens/edit_ticket_page.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/views/screens/event_statistics.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/views/screens/invite_users.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/views/screens/manage_delegates.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/views/widgets/attendees_widget.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/views/widgets/compact_event_statistics.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/transactions/models/transaction_type.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/transactions/views/screens/transaction_page.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/transactions/views/widgets/transaction_export_widget.dart';
import 'package:onekitty_admin/utils/asset_urls.dart';
import 'package:onekitty_admin/utils/date_formatter.dart';
import 'package:onekitty_admin/utils/formatted_currency.dart';
import 'package:onekitty_admin/utils/image_popup.dart';
import 'package:onekitty_admin/utils/my_button.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/views/widgets/my_quill_editor.dart';
import 'package:onekitty_admin/utils/show_cached_network_image.dart';
import 'package:onekitty_admin/utils/timeSince.dart';
import 'package:onekitty_admin/utils/whatsapp_validator.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:share_plus/share_plus.dart';
import '/utils/glassmorphic.dart';
import 'map_page.dart';
import 'signatory_transactions.dart';
import 'package:carousel_slider/carousel_slider.dart' as q;
import 'package:onekitty_admin/features/onekitty/screens/pages/transfers/transfers_exports.dart';
import 'verify_ticket.dart';
import 'view_single_event_viewer.dart' as ve;
import 'package:qr_flutter/qr_flutter.dart';
import 'package:flutter/rendering.dart';
import 'package:path_provider/path_provider.dart';

class ViewSingleEventOrganizer extends StatefulWidget {
  final MyEventsModel eventmodel;
  const ViewSingleEventOrganizer({super.key, required this.eventmodel});

  @override
  State<ViewSingleEventOrganizer> createState() =>
      _ViewSingleEventOrganizerState();
  static onRefresh({required int eventId}) {
    final controller = Get.find<ViewSingleEventController>();
    final getEventController = Get.find<EditEventController>();

    try {
      controller.fetchTransactions(eventId);
      getEventController.fetchEventDetail(eventId, isOrganizer: true);
    } catch (e) {
      Logger().e(e);
    }
  }
}

class _ViewSingleEventOrganizerState extends State<ViewSingleEventOrganizer> {
  final ViewSingleEventController controller = Get.put(
    ViewSingleEventController(),
  );
  final getEventController = Get.put(EditEventController());
  final RefreshController _refreshController = RefreshController(
    initialRefresh: true,
  );
  final ContributeController whatsappController = Get.put(
    ContributeController(),
  );
  final RxList<Notifications> eventWhatsappGroups = <Notifications>[].obs;
  final RxBool isLoadingWhatsapp = false.obs;

  // Store the original card's hero tag suffix when passed
  late String heroTagSuffix;

  @override
  void initState() {
    super.initState();
    // If no tag suffix is provided, generate a new one
    heroTagSuffix =
        widget.eventmodel.heroTagSuffix ??
        DateTime.now().microsecondsSinceEpoch.toString();
    _onRefresh();
  }

  void _onRefresh() async {
    try {
      await controller.fetchTransactions(widget.eventmodel.event.id ?? 0);
      getEventController.fetchEventDetail(
        widget.eventmodel.event.id ?? 0,
        isOrganizer: true,
      );
      _fetchWhatsappGroups();
      _refreshController.refreshCompleted();
    } catch (e) {
      _refreshController.refreshCompleted();
      Logger().e(e);
    }
  }

  Future<void> _fetchWhatsappGroups() async {
    try {
      isLoadingWhatsapp(true);
      // For now, we'll use the kitty ID for fetching WhatsApp groups
      // In a real implementation, you would modify the backend to support event-specific WhatsApp groups
      if (widget.eventmodel.event.kittyId != null) {
        await whatsappController.getWhatsapp(
          id: widget.eventmodel.event.kittyId!,
        );
        eventWhatsappGroups.assignAll(whatsappController.whatsappList);
      }
    } catch (e) {
      Logger().e(e);
    } finally {
      isLoadingWhatsapp(false);
    }
  }

  final carouselController = q.CarouselSliderController();
  final activeIndex = 0.obs;

  void _showWhatsappGroupsDialog(BuildContext context) {
    // Show loading overlay first
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return Center(
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
              boxShadow: [
                const BoxShadow(
                  color: Colors.black26,
                  blurRadius: 10,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Stack(
                  alignment: Alignment.center,
                  children: [
                    Image.asset(
                      'assets/images/whatsapp.png',
                      height: 40,
                      width: 40,
                    ),
                    SizedBox(
                      height: 60,
                      width: 60,
                      child: CircularProgressIndicator(
                        backgroundColor: Colors.grey.shade200,
                        color: primaryColor,
                        strokeWidth: 2,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Text(
                  'loading_whatsapp_groups'.tr,
                  style: TextStyle(
                    fontSize: 16.spMin,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );

    // Start loading
    isLoadingWhatsapp(true);

    // Fetch WhatsApp groups first, then show dialog
    _fetchWhatsappGroups().then((_) {
      // Remove loading dialog and show the actual WhatsApp groups dialog
      Navigator.of(context).pop();

      showDialog(
        context: context,
        builder: (context) => Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Container(
            width: double.infinity,
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.7,
              minHeight: 300,
            ),
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        'whatsapp_groups'.tr,
                        style: TextStyle(
                          fontSize: 18.spMin,
                          fontWeight: FontWeight.bold,
                          color: primaryColor,
                        ),
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
                const Divider(),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        'connected_groups'.tr,
                        style: TextStyle(
                          fontSize: 16.spMin,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    TextButton.icon(
                      onPressed: () {
                        final _dataController = Get.put(DataController());
                        _dataController.kitty.value.kitty?.title =
                            widget.eventmodel.event.title;
                        _dataController.kitty.value.kitty?.iD =
                            widget.eventmodel.event.kittyId;

                        Get.to(
                          () => WhatsAppEditLink(
                            kittyId: widget.eventmodel.event.kittyId,
                          ),
                        );
                      },
                      // onPressed: () => _showAddWhatsappGroupDialog(context),
                      icon: const Icon(Icons.add_circle, color: primaryColor),
                      label: Text(
                        'add_group'.tr,
                        style: TextStyle(
                          fontSize: 14.spMin,
                          color: primaryColor,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Obx(() {
                  if (eventWhatsappGroups.isEmpty) {
                    return Padding(
                      padding: const EdgeInsets.all(24.0),
                      child: Center(
                        child: Column(
                          children: [
                            Image.asset(
                              'assets/images/whatsapp.png',
                              height: 48,
                              width: 48,
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'no_whatsapp_groups_connected'.tr,
                              style: TextStyle(
                                fontSize: 16.spMin,
                                color: Colors.grey.shade600,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'connect_whatsapp_group_message'.tr,
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontSize: 14.spMin,
                                color: Colors.grey.shade500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  }

                  return Expanded(
                    child: ListView.separated(
                      shrinkWrap: true,
                      itemCount: eventWhatsappGroups.length,
                      separatorBuilder: (_, __) => const Divider(),
                      itemBuilder: (context, index) {
                        final group = eventWhatsappGroups[index];
                        return WhatsAppGroupTile(
                          group: group,
                          onToggle: (value) async {
                            if (widget.eventmodel.event.kittyId != null) {
                              await whatsappController.toggleWhatsapp(
                                id: group.id ?? 0,
                                kittyid: widget.eventmodel.event.kittyId!,
                                status: value ? "ACTIVE" : "INACTIVE",
                              );
                              _fetchWhatsappGroups();
                            }
                          },
                          onDelete: () async {
                            final confirmed = await _showConfirmDeleteDialog(
                              context,
                            );
                            if (confirmed &&
                                widget.eventmodel.event.kittyId != null) {
                              await whatsappController.RmWhatsapp(
                                notificationId: group.id ?? 0,
                                kittyId: widget.eventmodel.event.kittyId!,
                              );
                              _fetchWhatsappGroups();
                            }
                          },
                        );
                      },
                    ),
                  );
                }),
              ],
            ),
          ),
        ),
      );
    });
  }

  Future<bool> _showConfirmDeleteDialog(BuildContext context) async {
    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: Text('confirm_removal'.tr),
            content: Text('confirm_remove_whatsapp_group'.tr),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: Text('cancel'.tr),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                child: Text(
                  'remove'.tr,
                  style: TextStyle(color: Colors.red.shade700),
                ),
              ),
            ],
          ),
        ) ??
        false;
  }

  // ignore: unused_element
  void _showAddWhatsappGroupDialog(BuildContext context) {
    final formKey = GlobalKey<FormState>();
    final linkController = TextEditingController();
    final isSubmitting = false.obs;

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          child: Form(
            key: formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'connect_whatsapp_group'.tr,
                  style: TextStyle(
                    fontSize: 18.spMin,
                    fontWeight: FontWeight.bold,
                    color: primaryColor,
                  ),
                ),
                Text(
                  controller.event.value.title,
                  style: TextStyle(
                    fontSize: 14.spMin,
                    color: Colors.grey.shade600,
                  ),
                ),
                const SizedBox(height: 24),
                Text(
                  'whatsapp_group_link'.tr,
                  style: TextStyle(
                    fontSize: 16.spMin,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: linkController,
                  decoration: InputDecoration(
                    hintText: 'enter_whatsapp_link'.tr,
                    hintStyle: TextStyle(fontSize: 14.spMin),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 16,
                    ),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'please_enter_whatsapp_link'.tr;
                    } else if (!WhatsAppValidator.isValidWhatsAppLink(value)) {
                      return WhatsAppValidator.getValidationErrorMessage();
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    OutlinedButton(
                      onPressed: () => Navigator.pop(context),
                      style: OutlinedButton.styleFrom(
                        side: const BorderSide(color: primaryColor),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                      ),
                      child: Text(
                        'cancel'.tr,
                        style: const TextStyle(color: primaryColor),
                      ),
                    ),
                    Obx(
                      () => ElevatedButton(
                        onPressed: isSubmitting.value
                            ? null
                            : () async {
                                if (formKey.currentState?.validate() ?? false) {
                                  if (widget.eventmodel.event.kittyId == null) {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text(
                                          'cannot_connect_whatsapp_group'.tr,
                                        ),
                                      ),
                                    );
                                    Navigator.pop(context);
                                    return;
                                  }

                                  isSubmitting(true);
                                  try {
                                    final kittyController = Get.put(
                                      KittyController(),
                                    );
                                    final result = await kittyController
                                        .joinGroup(
                                          context: context,
                                          id: widget.eventmodel.event.kittyId!,
                                          link: linkController.text.trim(),
                                        );

                                    if (result) {
                                      _fetchWhatsappGroups();
                                      Navigator.pop(context);
                                    }
                                  } finally {
                                    isSubmitting(false);
                                  }
                                }
                              },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: primaryColor,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 12,
                          ),
                        ),
                        child: isSubmitting.value
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  color: Colors.white,
                                ),
                              )
                            : Text(
                                'connect'.tr,
                                style: const TextStyle(color: Colors.white),
                              ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showQRCodeDialog(BuildContext context, String eventUrl) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'event_qr_code'.tr,
                style: TextStyle(
                  fontSize: 18.spMin,
                  fontWeight: FontWeight.bold,
                  color: primaryColor,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'scan_to_open_event_page'.tr,
                style: TextStyle(
                  fontSize: 14.spMin,
                  color: Colors.grey.shade600,
                ),
              ),
              const SizedBox(height: 20),
              Stack(
                alignment: Alignment.center,
                children: [
                  QrImageView(
                    data: eventUrl,
                    version: QrVersions.auto,
                    size: 200.0,
                    backgroundColor: Colors.white,
                    foregroundColor: Colors.black,
                    embeddedImage: const AssetImage(AssetUrl.logo4),
                    embeddedImageStyle: const QrEmbeddedImageStyle(
                      size: Size(40, 40),
                    ),
                    gapless: false,
                  ),
                ],
              ),
              const SizedBox(height: 20),
              Text(
                '${'url'.tr}: $eventUrl',
                style: TextStyle(
                  fontSize: 12.spMin,
                  color: Colors.grey.shade600,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  TextButton.icon(
                    onPressed: () async {
                      await _shareEventPoster(context);
                    },
                    icon: const Icon(Icons.share, color: primaryColor),
                    label: Text(
                      'share_qr_code'.tr,
                      style: const TextStyle(color: primaryColor),
                    ),
                  ),
                  // ElevatedButton(
                  //   onPressed: () => Navigator.pop(context),
                  //   style: ElevatedButton.styleFrom(
                  //     backgroundColor: primaryColor,
                  //     foregroundColor: Colors.white,
                  //     shape: RoundedRectangleBorder(
                  //       borderRadius: BorderRadius.circular(8),
                  //     ),
                  //   ),
                  //   child:  Text('Close'),
                  // ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _shareEventPoster(BuildContext context) async {
    final overlayState = Overlay.of(context);

    // Poster widget
    final posterWidget = RepaintBoundary(
      key: GlobalKey(),
      child: Container(
        width: 1080.w,
        height: 1920.h,
        color: Colors.white,
        child: Padding(
          padding: EdgeInsets.all(40.0.spMin),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              SizedBox(height: 15.h),
              Column(
                children: [
                  Image.asset(
                    'assets/images/icons/onekitty.png',
                    height: 60.h,
                    width: 60.w,
                  ),
                  SizedBox(height: 15.h),
                  Text(
                    controller.event.value.title.toUpperCase(),
                    style: TextStyle(
                      fontSize: 28.spMin, // Reduced from 36
                      fontWeight: FontWeight.w800,
                      color: primaryColor,
                      letterSpacing: 1.1.w,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                  ),
                ],
              ),
              SizedBox(height: 10.h),

              // Centered QR Code
              Container(
                padding: EdgeInsets.all(20.spMin),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(30.r),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 15,
                      spreadRadius: 3,
                    ),
                  ],
                ),
                child: QrImageView(
                  data:
                      'https://www.onekitty.co.ke/events/${controller.event.value.username}',
                  version: QrVersions.auto,
                  size: 300.spMin, // Reduced from 400
                  backgroundColor: Colors.white,
                  foregroundColor: Colors.black,
                  embeddedImage: const AssetImage(AssetUrl.logo4),
                  embeddedImageStyle: QrEmbeddedImageStyle(
                    size: Size(45.w, 45.h), // Reduced from 60
                  ),
                ),
              ),
              SizedBox(height: 25.h),

              // Event Details
              Column(
                children: [
                  _buildDetailRow(
                    icon: Icons.location_on_outlined,
                    text: controller.event.value.venue,
                  ),
                  SizedBox(height: 20.h),
                  _buildDetailRow(
                    icon: Icons.calendar_month_outlined,
                    text: formatDate(
                      "${controller.event.value.startDate?.toLocal()}",
                    ),
                  ),
                ],
              ),
              SizedBox(height: 25.h),
              // Bottom CTA
              Center(
                child: Container(
                  alignment: Alignment.center,
                  padding: EdgeInsets.symmetric(
                    vertical: 15.h,
                    horizontal: 30.w,
                  ),
                  decoration: BoxDecoration(
                    color: primaryColor,
                    borderRadius: BorderRadius.circular(15.r),
                  ),
                  child: Text(
                    'scan_to_join_participate'.tr,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 20.spMin,
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );

    final overlayEntry = OverlayEntry(builder: (context) => posterWidget);
    overlayState.insert(overlayEntry);

    await Future.delayed(const Duration(milliseconds: 200));

    try {
      final RenderRepaintBoundary boundary =
          (posterWidget.key as GlobalKey).currentContext!.findRenderObject()
              as RenderRepaintBoundary;

      final image = await boundary.toImage(pixelRatio: 3.0);
      final byteData = await image.toByteData(format: ImageByteFormat.png);
      final buffer = byteData!.buffer;

      final tempDir = await getTemporaryDirectory();
      final file = await File('${tempDir.path}/event_poster.png').writeAsBytes(
        buffer.asUint8List(byteData.offsetInBytes, byteData.lengthInBytes),
      );

      await Share.shareXFiles(
        [XFile(file.path)],
        text:
            'Join ${controller.event.value.title}!\nScan or visit: '
            'https://www.onekitty.co.ke/events/${controller.event.value.username}',
      );
    } catch (e) {
      Logger().e('Error sharing poster: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('error_generating_poster'.tr),
          duration: const Duration(seconds: 2),
        ),
      );
    } finally {
      overlayEntry.remove();
      if (mounted) ScaffoldMessenger.of(context).hideCurrentSnackBar();
    }
  }

  Widget _buildDetailRow({required IconData icon, required String text}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(icon, color: primaryColor, size: 22), // Reduced from 28
        const SizedBox(width: 10),
        Text(
          text,
          style: const TextStyle(
            fontSize: 18, // Reduced from 22
            color: Colors.black87,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(
        () => SmartRefresher(
          onRefresh: _onRefresh,
          controller: _refreshController,
          child: CustomScrollView(
            slivers: [
              SliverAppBar(
                // floating: true,
                leadingWidth: 88,
                pinned: true,
                actions: [
                  GlassmorphicContainer(
                    onTap: () async {
                      await _shareEventPoster(context);
                    },
                    color: Colors.black,
                    blurRadius: 20,
                    cornerRadius: 360,
                    child: IconButton(
                      icon: Icon(Icons.admin_panel_settings),
                      tooltip: 'Admin Actions',
                      onPressed: () => EventsDashboardState()
                          .showAdminActionsDialog(context),
                    ),
                  ),
                  SizedBox(width: 8.w),
                  // QR Code button
                  GlassmorphicContainer(
                    onTap: () {
                      _showQRCodeDialog(
                        context,
                        'https://www.onekitty.co.ke/events/${controller.event.value.username}',
                      );
                    },
                    color: Colors.black,
                    blurRadius: 20,
                    cornerRadius: 360,
                    child: const Icon(Icons.qr_code),
                  ),
                  SizedBox(width: 8.w), // Add space between buttons
                  GlassmorphicContainer(
                    onTap: () {
                      Share.share(
                        'https://onekitty.co.ke/events/${controller.event.value.username}',
                      );
                    },
                    color: Colors.black,
                    blurRadius: 20,
                    cornerRadius: 360,
                    child: const Icon(Icons.share),
                  ),
                ],
                backgroundColor: isLight.value
                    ? scaffoldBackgroundColor
                    : Colors.transparent,
                bottom: PreferredSize(
                  preferredSize: const Size(0, 0),
                  child: Container(
                    // height: 53.h,
                    alignment: Alignment.center,
                    width: 280.w,
                    padding: const EdgeInsets.symmetric(
                      vertical: 1,
                      horizontal: 2,
                    ),
                    decoration: BoxDecoration(
                      color: isLight.value ? Colors.white : Colors.grey[900],
                      boxShadow: [
                        BoxShadow(
                          color: isLight.value
                              ? Colors.grey.shade500
                              : Colors.black,
                          offset: const Offset(0, 2),
                          blurRadius: 12,
                          spreadRadius: -4,
                        ),
                      ],
                      borderRadius: BorderRadius.circular(30.r),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        AttendeesWidget(
                          count: widget.eventmodel.count,
                          size: 18.spMin,
                          textSize: 15.spMin,
                        ),
                        Material(
                          borderRadius: BorderRadius.circular(25),
                          color: primaryColor,
                          child: SizedBox(
                            height: 30.h,
                            width: 70.w,
                            child: MaterialButton(
                              onPressed: () {
                                Get.dialog(
                                  InviteUser(
                                    ticket:
                                        widget.eventmodel.event.tickets ?? [],
                                    eventId: controller.event.value.id ?? 0,
                                  ),
                                );
                              },
                              child: AutoSizeText(
                                'invite'.tr,
                                minFontSize: 4,
                                maxLines: 1,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                leading: GlassmorphicContainer(
                  onTap: () => Navigator.pop(context),
                  color: Colors.black,
                  blurRadius: 20,
                  cornerRadius: 24,
                  child: Row(
                    children: [
                      const Icon(
                        Icons.arrow_back,
                        color: Colors.white,
                        size: 18,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'back'.tr,
                        style: const TextStyle(color: Colors.white),
                      ),
                    ],
                  ),
                ),
                expandedHeight: 255.h,
                flexibleSpace: FlexibleSpaceBar(
                  background: Stack(
                    alignment: Alignment.topCenter,
                    children: [
                      SizedBox(
                        height: 260.h,
                        child: Stack(
                          children: [
                            q.CarouselSlider.builder(
                              itemCount:
                                  controller.event.value.eventMedia?.length ??
                                  0,
                              itemBuilder: (context, index, realIndex) {
                                final mediaUrl = controller
                                    .event
                                    .value
                                    .eventMedia?[index]
                                    .url;
                                final isValidUrl =
                                    mediaUrl != null &&
                                    mediaUrl.isNotEmpty &&
                                    Uri.tryParse(mediaUrl) != null;

                                return Hero(
                                  tag: realIndex == 0
                                      ? 'image:${controller.event.value.id}o:card:$heroTagSuffix'
                                      : 'image:${controller.event.value.id}o$index:$heroTagSuffix',
                                  child: InkWell(
                                    onTap: () {
                                      final validUrls =
                                          controller.event.value.eventMedia
                                              ?.where(
                                                (e) =>
                                                    e.url != null &&
                                                    e.url!.isNotEmpty &&
                                                    Uri.tryParse(e.url!) !=
                                                        null,
                                              )
                                              .map((e) => e.url!)
                                              .toList() ??
                                          <String>[];
                                      if (validUrls.isNotEmpty) {
                                        Get.to(
                                          () => ImagePopup(
                                            pos: realIndex,
                                            title: controller.event.value.title,
                                            imageUrl: validUrls,
                                          ),
                                        );
                                      }
                                    },
                                    child: ShowCachedNetworkImage(
                                      fit: BoxFit.cover,
                                      width: MediaQuery.sizeOf(context).width,
                                      errorWidget: Container(
                                        color: Colors.grey.shade300,
                                        child: const Icon(
                                          Icons.broken_image,
                                          size: 50,
                                        ),
                                      ),
                                      imageurl: isValidUrl
                                          ? mediaUrl
                                          : AssetUrl.onekittyBannnerUrl,
                                    ),
                                  ),
                                );
                              },
                              options: q.CarouselOptions(
                                height: 260.h,
                                viewportFraction: 1.0,
                                enableInfiniteScroll: false,
                                onPageChanged: (index, reason) {
                                  activeIndex(index);
                                  // carouselController.jumpToPage(index);
                                },
                              ),
                            ),
                            (controller.event.value.eventMedia ??
                                        <EventMedia>[])
                                    .isEmpty
                                ? const SizedBox()
                                : Positioned(
                                    bottom: 38.h,
                                    left: 0,
                                    right: 0,
                                    child: Obx(
                                      () => Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: List.generate(
                                          controller
                                                  .event
                                                  .value
                                                  .eventMedia
                                                  ?.length ??
                                              0,
                                          (index) => Container(
                                            margin: const EdgeInsets.symmetric(
                                              horizontal: 4,
                                            ),
                                            width: 8,
                                            height: 8,
                                            decoration: BoxDecoration(
                                              shape: BoxShape.circle,
                                              color: activeIndex.value == index
                                                  ? Colors.white70
                                                  : Colors.white24,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              SliverToBoxAdapter(
                child: Padding(
                  padding: controller.event.value.balance.toString().length <= 4
                      ? EdgeInsets.symmetric(horizontal: 20.0, vertical: 8.h)
                      : EdgeInsets.only(left: 20.0.w, right: 20.w, top: 8.h),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // if (kDebugMode) Text('${controller.event.value.id}'),
                            Hero(
                              tag:
                                  'text:${controller.event.value.id}o:$heroTagSuffix',
                              child: Material(
                                color: Colors.transparent,
                                child: Text(
                                  controller.event.value.title,
                                  style: TextStyle(
                                    color: isLight.value
                                        ? Colors.black
                                        : Colors.white,
                                    fontWeight: FontWeight.w700,
                                    fontSize: 22,
                                  ),
                                ),
                              ),
                            ),
                            if (controller.event.value.balance
                                    .toString()
                                    .length <=
                                4)
                              Container(
                                margin: const EdgeInsets.only(left: 2, top: 4),
                                padding: const EdgeInsets.symmetric(
                                  vertical: 4,
                                  horizontal: 8,
                                ),
                                decoration: BoxDecoration(
                                  border: Border.all(color: Colors.grey),
                                  borderRadius: BorderRadius.circular(24),
                                ),
                                child: Text(
                                  controller.event.value.status?.name ?? '',
                                  style: TextStyle(
                                    color: getEventStatusColor(
                                      controller.event.value.status?.name ?? "",
                                    ),
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                      if (controller.event.value.balance.toString().length <= 4)
                        Hero(
                          tag:
                              'collected${controller.event.value.id}o:$heroTagSuffix',
                          child: Material(
                            color: Colors.transparent,
                            child: Text.rich(
                              textAlign: TextAlign.end,
                              TextSpan(
                                children: [
                                  TextSpan(
                                    text:
                                        '${FormattedCurrency.getFormattedCurrency(widget.eventmodel.event.balance)}\n',
                                    style: const TextStyle(
                                      color: Color(0xff4355b6),
                                      fontSize: 30,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  TextSpan(text: 'collected'.tr),
                                ],
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
              SliverToBoxAdapter(
                child: Column(
                  children: [
                    if (controller.event.value.balance.toString().length >= 4)
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Container(
                              margin: const EdgeInsets.only(left: 2, top: 4),
                              padding: const EdgeInsets.symmetric(
                                vertical: 4,
                                horizontal: 8,
                              ),
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey),
                                borderRadius: BorderRadius.circular(24),
                              ),
                              child: Text(
                                controller.event.value.status?.name ?? '',
                                style: TextStyle(
                                  color: getEventStatusColor(
                                    controller.event.value.status?.name ?? "",
                                  ),
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            Hero(
                              tag:
                                  'collected${controller.event.value.id}o:$heroTagSuffix',
                              child: Material(
                                color: Colors.transparent,
                                child: Text.rich(
                                  textAlign: TextAlign.end,
                                  TextSpan(
                                    children: [
                                      TextSpan(
                                        text:
                                            '${FormattedCurrency.getFormattedCurrency(widget.eventmodel.event.balance)}\n',
                                        style: const TextStyle(
                                          color: Color(0xff4355b6),
                                          fontSize: 30,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                      TextSpan(text: 'collected'.tr),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ListTile(
                      onTap: () => Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => MapScreen(
                            viewOnly: true,
                            longitude: controller.event.value.longitude,
                            latitude: controller.event.value.latitude,
                          ),
                        ),
                      ),
                      leading: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          color: primaryColor.withOpacity(0.15),
                        ),
                        child: Hero(
                          tag:
                              "ilocation${controller.event.value.id}o:$heroTagSuffix",
                          child: Image.asset(
                            'assets/images/icons/location.png',
                            height: 30,
                            width: 30,
                            color: primaryColor,
                          ),

                          //   Icon(Icons.location_on,
                          //     color: primaryColor)
                        ),
                      ),
                      title: Hero(
                        tag:
                            "tlocation${controller.event.value.id}o:$heroTagSuffix",
                        child: Material(
                          color: Colors.transparent,
                          child: Text(
                            controller.event.value.venue,
                            style: const TextStyle(fontWeight: FontWeight.w600),
                          ),
                        ),
                      ),
                      subtitle: Text(
                        controller.event.value.locationTip,
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                    ListTile(
                      onTap: () {
                        showDialog(
                          context: context,
                          builder: (context) => Dialog(
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  CalendarDatePicker(
                                    initialDate:
                                        controller.event.value.startDate!,
                                    firstDate: DateTime(2000),
                                    lastDate: DateTime(2100),
                                    onDateChanged: (_) {},
                                    selectableDayPredicate: (DateTime date) {
                                      return date.year ==
                                              controller
                                                  .event
                                                  .value
                                                  .startDate!
                                                  .year &&
                                          date.month ==
                                              controller
                                                  .event
                                                  .value
                                                  .startDate!
                                                  .month &&
                                          date.day ==
                                              controller
                                                  .event
                                                  .value
                                                  .startDate!
                                                  .day;
                                    },
                                  ),
                                  Align(
                                    alignment: Alignment.centerRight,
                                    child: TextButton(
                                      onPressed: () => Navigator.pop(context),
                                      child: Text('close'.tr),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                      leading: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          color: primaryColor.withOpacity(0.15),
                        ),
                        child: Hero(
                          tag:
                              "idate${controller.event.value.id}o:$heroTagSuffix",
                          child: Image.asset(
                            'assets/images/icons/calendar.png',
                            height: 30,
                            width: 30,
                            color: primaryColor,
                          ),
                          //   Icon(Icons.calendar_month_rounded,
                          //     color: primaryColor)
                        ),
                      ),
                      title: Hero(
                        tag:
                            "tdate${controller.event.value.id}o:$heroTagSuffix",
                        child: Material(
                          color: Colors.transparent,
                          child: Text(
                            formatDate(
                              "${controller.event.value.startDate?.toLocal()}",
                            ),
                            style: const TextStyle(fontWeight: FontWeight.w600),
                          ),
                        ),
                      ),
                      subtitle: Text(
                        controller.event.value.startDate == null ||
                                controller.event.value.endDate == null
                            ? ''
                            : '${DateFormat('EEEE').format(controller.event.value.startDate?.toLocal() ?? DateTime.now())}, ${formatTime("${controller.event.value.startDate?.toLocal()}")} - ${formatDate("${controller.event.value.startDate?.toLocal()}") == formatDate("${controller.event.value.endDate?.toLocal()}") ? formatTime("${controller.event.value.endDate?.toLocal()}") : "${DateFormat('EEEE').format(controller.event.value.endDate?.toLocal() ?? DateTime.now())}, ${formatTime("${controller.event.value.endDate?.toLocal()}")}"}\n'
                                  '${highPrecisiontimeSince(controller.event.value.endDate?.toLocal() ?? DateTime.now(), preffixFutureDate: '', suffixFutureDate: 'left'.tr, preffixPastDate: 'ended'.tr)}',
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SliverToBoxAdapter(
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: 12.0.w,
                    vertical: 2.h,
                  ),
                  child: Text(
                    'about_event'.tr,
                    style: TextStyle(
                      fontSize: 16.spMin,
                      fontWeight: FontWeight.w500,
                      color: isLight.value ? Colors.black : Colors.white,
                    ),
                  ),
                ),
              ),
              SliverToBoxAdapter(
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: 12.0.w,
                    vertical: 4.h,
                  ),
                  child: Hero(
                    tag: 'desc:${controller.event.value.id}o:$heroTagSuffix',
                    child: QuillEditorWidget(
                      text: controller.event.value.description,
                    ),
                  ),
                ),
              ),
              SliverToBoxAdapter(
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: 12.0.w,
                    vertical: 4.h,
                  ),
                  child:
                      // Compact Event Statistics Widget
                      CompactEventStatistics(
                        eventId: widget.eventmodel.event.id ?? 0,
                      ),
                ),
              ),
              SliverToBoxAdapter(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 2.0.w),
                  child: Builder(
                    builder: (context) {
                      final ValueNotifier<int> selectedIndex = ValueNotifier(0);
                      return ValueListenableBuilder(
                        valueListenable: selectedIndex,
                        builder: (context, index, child) {
                          return Column(
                            children: [
                              Container(
                                padding: EdgeInsets.symmetric(
                                  vertical: 6.h,
                                  horizontal: 8.w,
                                ),
                                margin: EdgeInsets.all(8.spMin),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(15),
                                  color: primaryColor.withOpacity(0.15),
                                ),
                                child: Row(
                                  children: [
                                    Expanded(
                                      child: GestureDetector(
                                        onTap: () => selectedIndex.value = 0,
                                        child: Container(
                                          padding: const EdgeInsets.all(8),
                                          alignment: Alignment.center,
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(
                                              7.5,
                                            ),
                                            color: index == 0
                                                ? Colors.white
                                                : null,
                                          ),
                                          child: Text(
                                            'services'.tr,
                                            style: TextStyle(
                                              fontWeight: FontWeight.w600,
                                              fontSize: 14.spMin,
                                              color: index == 0
                                                  ? primaryColor
                                                  : null,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                    Expanded(
                                      child: GestureDetector(
                                        onTap: () => selectedIndex.value = 1,
                                        child: Container(
                                          alignment: Alignment.center,
                                          padding: const EdgeInsets.all(8),
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(
                                              7.5,
                                            ),
                                            color: index == 1
                                                ? Colors.white
                                                : null,
                                          ),
                                          child: Text(
                                            'my_transactions'.tr,
                                            style: TextStyle(
                                              fontWeight: FontWeight.w600,
                                              fontSize: 14.spMin,
                                              color: index == 1
                                                  ? primaryColor
                                                  : null,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              if (index == 0)
                                Wrap(
                                  children: [
                                    ServicesWidget(
                                      page: EditTickectPage(
                                        eventId: controller.event.value.id ?? 0,
                                      ),
                                      label: 'edit_ticket'.tr,
                                      image:
                                          'assets/images/icons/ticketlogo.png',
                                      icon: Icons.airplane_ticket_outlined,
                                    ),
                                    ServicesWidget(
                                      page: EditEventPage(
                                        myevent: widget.eventmodel,
                                        id: controller.event.value.id ?? 0,
                                      ),
                                      refreshController: _refreshController,
                                      label: 'edit_event'.tr,
                                      icon: Icons.edit_calendar_rounded,
                                    ),
                                    ServicesWidget(
                                      page: InvitePage(
                                        eventname: controller.event.value.title,
                                        kittyId:
                                            widget.eventmodel.event.kittyId!,
                                      ),
                                      label: 'manage_delegates'.tr,
                                      icon: Icons.group_add_outlined,
                                    ),
                                    ServicesWidget(
                                      page: const ve.ViewSingleEventViewer(),
                                      label: 'purchase_ticket'.tr,
                                      icon: Icons.monetization_on_outlined,
                                    ),
                                    Obx(
                                      () => ServicesWidget(
                                        refreshController: _refreshController,
                                        flash: getEventController
                                            .hasSignatoryTransactions
                                            .value,
                                        page: SignatoryTransactions(
                                          kittyId:
                                              controller.event.value.kittyId!,
                                        ),
                                        label: 'signatory_approvals'.tr,
                                        icon: Icons.verified_outlined,
                                      ),
                                    ),
                                    ServicesWidget(
                                      flash: false,
                                      page: VerifyTicket(
                                        eventId: controller.event.value.id ?? 0,
                                      ),
                                      label: 'verify_tickets'.tr,
                                      icon: Icons.document_scanner_outlined,
                                    ),
                                    ServicesWidget(
                                      onTap: () {
                                        Get.toEventTransfer(
                                          eventId:
                                              controller.event.value.id ?? 0,
                                        );
                                      },
                                      label: 'transfer_funds'.tr,
                                      icon: Icons.currency_exchange,
                                      flash: false,
                                    ),
                                    ServicesWidget(
                                      onTap: () {
                                        _showWhatsappGroupsDialog(context);
                                      },
                                      label: 'whatsapp_groups'.tr,
                                      image: 'assets/images/icons/whatsapp.png',
                                      icon: Icons.message,
                                      flash: false,
                                    ),
                                    ServicesWidget(
                                      icon: Icons.import_export,
                                      label: "export_transactions".tr,
                                      onTap: () {
                                        final kittyId =
                                            widget.eventmodel.event.kittyId ??
                                            0;
                                        showDialog(
                                          context: context,
                                          barrierDismissible: true,
                                          builder: (context) => Dialog(
                                            child: SizedBox(
                                              height: 100,
                                              width: 100,
                                              child: Column(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                children: [
                                                  const Center(
                                                    child:
                                                        CircularProgressIndicator(),
                                                  ),
                                                  const SizedBox(height: 16),
                                                  Text(
                                                    'fetching_transactions'.tr,
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        );
                                        Get.put(KittyController())
                                            .getKittyContributions(
                                              kittyId: kittyId,
                                            )
                                            .then((_) {
                                              Navigator.pop(
                                                context,
                                              ); // Dismiss loading dialog
                                              showModalBottomSheet(
                                                isScrollControlled: true,
                                                context: context,
                                                builder: (BuildContext context) {
                                                  return TransactionExportWidget(
                                                    transactions:
                                                        Get.find<
                                                              KittyController
                                                            >()
                                                            .transactionsKitty,
                                                    singleTrans: false,
                                                    entityTitle:
                                                        Get.find<
                                                              DataController
                                                            >()
                                                            .kitty
                                                            .value
                                                            .kitty
                                                            ?.title,
                                                    entityId:
                                                        Get.find<
                                                              DataController
                                                            >()
                                                            .kitty
                                                            .value
                                                            .kitty
                                                            ?.iD,
                                                    kitty:
                                                        Get.find<
                                                              DataController
                                                            >()
                                                            .kitty
                                                            .value
                                                            .kitty,
                                                    transactionType:
                                                        TransactionType.kitty,
                                                    controllerTag: '',
                                                  );
                                                },
                                              );
                                            })
                                            .catchError((_) {
                                              Navigator.pop(
                                                context,
                                              ); // Dismiss loading dialog on error
                                            });
                                      },
                                    ),
                                  ],
                                ),
                              if (index == 1)
                                Obx(() {
                                  return TransactionPage(
                                    config: TransactionPageConfig(
                                      transactionType: TransactionType.event,
                                      entityId: controller.event.value.id ?? 0,
                                      title: 'event_transactions'.tr,
                                      isFullPage: false,
                                      showExportOptions: true,
                                      showEditOptions: false,
                                    ),
                                  ); /*Container(
                              margin:  EdgeInsets.all(15),
                              padding:  EdgeInsets.all(2),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(15),
                                  border: Border.all(
                                      color: isLight.value
                                          ? Colors.grey.shade300
                                          : Colors.grey.shade800),
                                  color: isLight.value
                                      ? Colors.white
                                      : Colors.grey[900]),
                              child: controller.transactions.isEmpty
                                  ? SizedBox(
                                      height: 200.h,
                                      child:  Center(
                                          child: Text('no_transactions_found'.tr)))
                                  : GroupedTransactionsList(
                                      transactions: controller.transactions,
                                    ));*/
                                }),
                            ],
                          );
                        },
                      );
                    },
                  ),
                ),
              ),
              SliverToBoxAdapter(child: SizedBox(height: 300.h)),
            ],
          ),
        ),
      ),
    );
  }
}

class ServicesWidget extends StatelessWidget {
  final String label;
  final IconData icon;
  final Widget? page;
  final String? image;
  final bool? flash;
  final RefreshController? refreshController;
  final VoidCallback? onTap;

  const ServicesWidget({
    super.key,
    required this.label,
    required this.icon,
    this.page,
    this.image,
    this.flash,
    this.refreshController,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final flashColor = primaryColor.obs;
    if (flash ?? false) {
      Timer.periodic(const Duration(milliseconds: 800), (t) {
        flashColor.value = t.tick.isEven ? primaryColor : Colors.red;
      });
    }

    if (onTap != null) {
      return InkWell(
        onTap: () {
          if (Get.find<EditEventController>().isloading.value) {
            ToastUtils.showInfoToast(
              context,
              'please_wait_fetching_data'.tr,
              'please_try_again_later'.tr,
            );
            return;
          }
          onTap!();
          if (refreshController != null) {
            refreshController!.requestRefresh();
          }
        },
        child: _buildServiceContainer(flashColor),
      );
    }

    return OpenContainer(
      closedElevation: 0,
      closedColor: Colors.transparent,
      openBuilder: (context, action) => page!,
      closedBuilder: (context, action) => InkWell(
        onTap: () {
          action.call();
          if (refreshController != null) {
            refreshController!.requestRefresh();
          }
        },
        child: _buildServiceContainer(flashColor),
      ),
    );
  }

  Widget _buildServiceContainer(Rx<Color> flashColor) {
    return Obx(() {
      return Container(
        constraints: const BoxConstraints(
          minHeight: 90,
          maxHeight: 90,
          minWidth: 110,
          maxWidth: 110,
        ),
        padding: const EdgeInsets.all(8),
        margin: const EdgeInsets.all(4),
        decoration: BoxDecoration(
          border: Border.all(color: flashColor.value, width: 0.5),
          borderRadius: BorderRadius.circular(8),
          color: isLight.value ? scaffoldBackgroundColor : Colors.grey[900],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            image != null
                ? image!.endsWith('.svg')
                      ? SvgPicture.asset(
                          image!,
                          height: 24.h,
                          width: 24.w,
                          colorFilter: const ColorFilter.mode(
                            primaryColor,
                            BlendMode.srcIn,
                          ),
                        )
                      : Image.asset(
                          image!,
                          height: 24.h,
                          width: 24.w,
                          color: primaryColor,
                        )
                : Icon(icon, color: flashColor.value),
            Center(
              child: Text(
                label,
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 12, color: flashColor.value),
              ),
            ),
          ],
        ),
      );
    });
  }
}

class GroupedTransactionsList extends StatelessWidget {
  final List<TransactionModel> transactions;

  const GroupedTransactionsList({super.key, required this.transactions});

  @override
  Widget build(BuildContext context) {
    // Sort transactions by date
    final sortedTransactions = List<TransactionModel>.from(transactions)
      ..sort((a, b) => b.createdAt!.compareTo(a.createdAt!));

    // Group transactions by date
    final groupedTransactions = <String, List<TransactionModel>>{};
    for (var transaction in sortedTransactions) {
      final dateStr = DateFormat('dd MMMM yyyy').format(transaction.createdAt!);
      groupedTransactions.putIfAbsent(dateStr, () => []).add(transaction);
    }

    return ListView.builder(
      itemCount: groupedTransactions.length,
      shrinkWrap: true,
      padding: const EdgeInsets.all(0),
      physics: const NeverScrollableScrollPhysics(),
      itemBuilder: (context, index) {
        final date = groupedTransactions.keys.elementAt(index);
        final transactionsForDate = groupedTransactions[date]!;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 16.0, top: 8.0, right: 16.0),
              child: Text(
                date,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            ListView.builder(
              itemCount: transactionsForDate.length,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemBuilder: (context, index) {
                final transaction = transactionsForDate[index];
                return Column(
                  children: [
                    ListTile(
                      onTap: () {
                        showDialog(
                          context: context,
                          builder: (context) => AlertDialog(
                            contentPadding: EdgeInsets.zero,
                            content: SingleChildScrollView(
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(16),
                                    decoration: BoxDecoration(
                                      color: primaryColor.withOpacity(0.1),
                                      borderRadius: const BorderRadius.only(
                                        topLeft: Radius.circular(8),
                                        topRight: Radius.circular(8),
                                      ),
                                    ),
                                    child: Row(
                                      children: [
                                        CircleAvatar(
                                          backgroundColor: primaryColor,
                                          child: Text(
                                            (transaction
                                                        .firstName
                                                        ?.isNotEmpty ==
                                                    true)
                                                ? transaction.firstName![0]
                                                : '',
                                            style: const TextStyle(
                                              color: Colors.white,
                                            ),
                                          ),
                                        ),
                                        const SizedBox(width: 12),
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                '${transaction.firstName ?? ""} ${transaction.secondName ?? ""}',
                                                style: const TextStyle(
                                                  fontSize: 16,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                              Text(
                                                transaction.phoneNumber ?? '',
                                                style: TextStyle(
                                                  color: Colors.grey.shade700,
                                                ),
                                              ),
                                              if (transaction.email != null)
                                                Text(
                                                  transaction.email!,
                                                  style: TextStyle(
                                                    color: Colors.grey.shade700,
                                                    fontSize: 12,
                                                  ),
                                                ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.all(16),
                                    child: Builder(
                                      builder: (context) {
                                        Widget _buildDetailRow(
                                          String label,
                                          String value,
                                        ) {
                                          return Padding(
                                            padding: const EdgeInsets.symmetric(
                                              vertical: 8.0,
                                            ),
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                Text(
                                                  label,
                                                  style: TextStyle(
                                                    color: Colors.grey.shade600,
                                                  ),
                                                ),
                                                Text(
                                                  value,
                                                  style: const TextStyle(
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          );
                                        }

                                        return Column(
                                          children: [
                                            _buildDetailRow(
                                              'transaction_id'.tr,
                                              transaction.transactionCode ?? '',
                                            ),
                                            _buildDetailRow(
                                              'amount'.tr,
                                              FormattedCurrency.getFormattedCurrency(
                                                transaction.amount,
                                              ),
                                            ),
                                            _buildDetailRow(
                                              'date'.tr,
                                              DateFormat(
                                                'dd MMM yyyy, hh:mm a',
                                              ).format(
                                                transaction.createdAt!
                                                    .toLocal(),
                                              ),
                                            ),
                                            _buildDetailRow(
                                              'status'.tr,
                                              transaction.status ?? '',
                                            ),
                                            if (transaction.channelCode != null)
                                              _buildDetailRow(
                                                'payment_method'.tr,
                                                transaction.channelCode != null
                                                    ? Get.find<
                                                            GlobalControllers
                                                          >()
                                                          .paymentChannels
                                                          .where(
                                                            (e) =>
                                                                e.channelCode ==
                                                                int.tryParse(
                                                                  transaction
                                                                      .channelCode
                                                                      .toString(),
                                                                ),
                                                          )
                                                          .first
                                                          .name
                                                    : "",
                                              ),
                                          ],
                                        );
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            actions: [
                              ElevatedButton(
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Theme.of(
                                    context,
                                  ).canvasColor,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(20.sp),
                                    side: BorderSide(
                                      width: 2.sp,
                                      color: Theme.of(context).primaryColor,
                                    ),
                                  ),
                                ),
                                onPressed: () async {
                                  String shareMsg =
                                      "Phone Number: ${transaction.phoneNumber ?? ''}\nEmail: ${transaction.email ?? 'Not provided'}\nAmount: ${CountryConfig.getCurrencyCode} ${transaction.amount}\nTransaction Code: ${transaction.transactionCode ?? ''}\nKitty: https://onekitty.co.ke/kitty/${transaction.kittyId ?? ''}";
                                  await Share.share(
                                    shareMsg,
                                    subject: 'contribution_details'.tr,
                                  );
                                },
                                child: Text(
                                  'share'.tr,
                                  style: const TextStyle(color: Colors.black),
                                ),
                              ),
                              TextButton(
                                onPressed: () => Navigator.pop(context),
                                child: const Text('Close'),
                              ),
                            ],
                          ),
                        );
                      },
                      title: Text(
                        (transaction.firstName ?? "") +
                            (transaction.secondName ?? ''),
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${transaction.transactionCode ?? transaction.phoneNumber}',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade700,
                            ),
                          ),
                          if (transaction.email != null)
                            Text(
                              transaction.email!,
                              style: TextStyle(
                                fontSize: 11,
                                color: Colors.grey.shade600,
                              ),
                            ),
                        ],
                      ),
                      trailing: Text.rich(
                        textAlign: TextAlign.end,
                        TextSpan(
                          children: [
                            TextSpan(
                              text:
                                  '${FormattedCurrency.getFormattedCurrency(transaction.amount)}\n',
                              style: const TextStyle(
                                fontWeight: FontWeight.w600,
                                fontSize: 16,
                                color: Colors.green,
                              ),
                            ),
                            TextSpan(
                              text: DateFormat.jm().format(
                                transaction.createdAt!.toLocal(),
                              ),
                            ),
                          ],
                        ),
                      ),
                      leading: CircleAvatar(
                        backgroundColor: primaryColor,
                        child: Text(
                          (transaction.firstName?.isNotEmpty == true)
                              ? transaction.firstName![0]
                              : '',
                          style: const TextStyle(color: Colors.white),
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 15.0),
                      child: Divider(height: 1, color: Colors.grey.shade300),
                    ),
                  ],
                );
              },
            ),
          ],
        );
      },
    );
  }
}

class InviteUser extends StatelessWidget {
  final List<Ticket> ticket;
  final int eventId;
  const InviteUser({super.key, required this.ticket, required this.eventId});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(ViewEventController());
    final cont = false.obs;

    return Dialog(
      child: Builder(
        builder: (context) {
          return Obx(
            () => AnimatedContainer(
              duration: const Duration(milliseconds: 500),
              height: ticket.isEmpty
                  ? 250.h
                  : cont.value
                  ? 340.h
                  : 440.h,
              child: Column(
                mainAxisSize: MainAxisSize.max,
                children: [
                  Text(
                    cont.value ? 'enter_user_details'.tr : 'pick_a_ticket'.tr,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 20.spMin,
                    ),
                  ),
                  if (ticket.isEmpty)
                    SizedBox(
                      height: 290.h,
                      child: Center(child: Text('no_tickets_available'.tr)),
                    ),
                  if (ticket.isNotEmpty)
                    Expanded(
                      child: ListView.builder(
                        shrinkWrap: true,
                        itemCount: ticket.length,
                        itemBuilder: (context, index) {
                          final Ticket tickets = ticket[index];
                          if (tickets.ticketType != "RESERVE") {
                            return const SizedBox();
                          }
                          return Obx(
                            () => GestureDetector(
                              onTap: () {
                                controller.selectedTicket.value = index;
                              },
                              child: Container(
                                padding: const EdgeInsets.all(8),
                                margin: EdgeInsets.all(8.spMin),
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color:
                                        controller.selectedTicket.value == index
                                        ? AppColors.primary
                                        : Colors.grey,
                                    width: 1,
                                  ),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      tickets.title ?? '',
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    Text(tickets.ticketType ?? ''),
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  const SizedBox(height: 10),
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Obx(
                      () => MyButton(
                        showLoading: controller.isInviting.value,
                        icon: cont.value ? null : Icons.navigate_next,
                        label: cont.value ? 'invite'.tr : 'continue'.tr,
                        onClick: () {
                          Get.to(
                            () => InviteUsersPage(
                              ticketId:
                                  ticket[controller.selectedTicket.value].id!,
                              eventId: eventId,
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

Color getEventStatusColor(String status) {
  switch (status.toLowerCase()) {
    case "active":
      return const Color(0xFF56AF57);
    case "ended":
      return AppColors.greyTextColor;
    case "pending review":
      return Colors.amber;
    case "blocked":
      return const Color(0xFFEE5B60);

    default:
      return const Color(0xFFEE5B60);
  }
}

class WhatsAppGroupTile extends StatefulWidget {
  final Notifications group;
  final Function(bool) onToggle;
  final VoidCallback onDelete;

  const WhatsAppGroupTile({
    super.key,
    required this.group,
    required this.onToggle,
    required this.onDelete,
  });

  @override
  State<WhatsAppGroupTile> createState() => _WhatsAppGroupTileState();
}

class _WhatsAppGroupTileState extends State<WhatsAppGroupTile> {
  late bool isActive;
  bool isLoading = false;
  bool isDeleteLoading = false;

  @override
  void initState() {
    super.initState();
    isActive = widget.group.whatsappStatus == "ACTIVE";
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: isLight.value ? Colors.white : Colors.grey.shade800,
      ),
      child: Row(
        children: [
          CircleAvatar(
            backgroundColor: Colors.green.shade100,
            child: Image.asset(AssetUrl.whatsapp, height: 24, width: 24),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.group.whatsappGroupName ?? "WhatsApp Group",
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14.spMin,
                  ),
                ),
                Row(
                  children: [
                    Container(
                      width: 8,
                      height: 8,
                      margin: const EdgeInsets.only(right: 6),
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: widget.group.whatsappStatus == "ACTIVE"
                            ? Colors.green.shade700
                            : Colors.grey.shade400,
                      ),
                    ),
                    Text(
                      widget.group.whatsappStatus == "ACTIVE"
                          ? 'active'.tr
                          : 'inactive'.tr,
                      style: TextStyle(
                        fontSize: 12.spMin,
                        color: widget.group.whatsappStatus == "ACTIVE"
                            ? Colors.green.shade700
                            : Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          isLoading
              ? const SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: primaryColor,
                  ),
                )
              : Switch(
                  value: isActive,
                  onChanged: (value) async {
                    setState(() {
                      isLoading = true;
                    });
                    try {
                      await widget.onToggle(value);
                      setState(() {
                        isActive = value;
                      });
                    } finally {
                      setState(() {
                        isLoading = false;
                      });
                    }
                  },
                  activeColor: primaryColor,
                ),
          isDeleteLoading
              ? SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: Colors.red.shade400,
                  ),
                )
              : IconButton(
                  icon: Icon(Icons.delete_outline, color: Colors.red.shade400),
                  onPressed: () async {
                    final messenger = ScaffoldMessenger.of(context);
                    setState(() {
                      isDeleteLoading = true;
                    });

                    // Call the onDelete callback
                    widget.onDelete();

                    // Set loading state back to false after a delay
                    Future.delayed(const Duration(milliseconds: 300), () {
                      if (mounted) {
                        setState(() {
                          isDeleteLoading = false;
                        });
                        messenger.hideCurrentSnackBar();
                      }
                    });
                  },
                ),
        ],
      ),
    );
  }
}
