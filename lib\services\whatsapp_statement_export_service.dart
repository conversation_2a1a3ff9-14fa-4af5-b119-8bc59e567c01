import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onekitty_admin/configs/country_specifics.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/transactions/controllers/transaction_controller.dart';
import 'package:onekitty_admin/services/api_urls.dart';
import 'package:onekitty_admin/services/http_service.dart';
import 'package:onekitty_admin/helpers/show_toast.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/controllers/events_controller.dart';
import 'package:onekitty_admin/features/onekitty/widgets/custom_international_phone_input.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:intl/intl.dart';

/// Service for generating transaction statements
class WhatsAppStatementExportService extends GetxService {
  final HttpService apiProvider = Get.find();
  final logger = Get.find<Logger>();

  final RxBool isSendingStatement = false.obs;
  final RxString lastSendStatus = ''.obs;

  /// Generate chama statement
  Future<bool> sendChamaStatementToWhatsApp({
    required BuildContext context,
    required int? kittyId,
    required String? recipient,
    required String type, // "EXCEL" or "PDF"
    String? startDate,
    String? endDate,
    String? email,
    bool showCharges = true,
  }) async {
    try {
      isSendingStatement(true);

      // Validate kittyId
      if (kittyId == null) {
        ToastUtils.showErrorToast(
          context,
          'Unable to generate statement: Missing chama information',
          'Error',
        );
        return false;
      }

      // Get current user's phone number for validation
      final currentUser = Get.find<Eventcontroller>().getLocalUser();
      final currentUserPhone = currentUser?.phoneNumber;

      if (currentUserPhone == null) {
        ToastUtils.showErrorToast(
          context,
          'User session expired. Please login again.',
          'Error',
        );
        return false;
      }

      // Prepare request body for chama statement - use kitty_id instead of chama_id
      final requestBody = {
        'kitty_id': kittyId,
        'recipient': recipient,
        'type': type.toUpperCase(),
        'show_charges': showCharges,
      };

      // Add optional parameters
      if (startDate != null && startDate.isNotEmpty) {
        requestBody['start_date'] = startDate;
      }

      if (endDate != null && endDate.isNotEmpty) {
        requestBody['end_date'] = endDate;
      }

      if (email != null && email.isNotEmpty) {
        requestBody['email'] = email;
      }

      logger.i('Generating chama statement request: $requestBody');

      final response = await apiProvider.request(
        url: ApiUrls.SEND_STATEMENT_WHATSAPP,
        method: Method.POST,
        params: requestBody,
      );

      if (response.data['status'] ?? false) {
        lastSendStatus.value = 'success';
        ToastUtils.showSuccessToast(
          context,
          response.data['message'] ?? 'Statement generated successfully',
          'Success',
        );
        return true;
      } else {
        lastSendStatus.value = 'failed';
        ToastUtils.showErrorToast(
          context,
          response.data['message'] ?? 'Failed to generate statement',
          'Error',
        );
        return false;
      }
    } catch (e) {
      logger.e('Error generating chama statement: $e');
      lastSendStatus.value = 'error';

      String errorMessage = 'An error occurred while generating statement';
      if (e.toString().contains('delegate')) {
        errorMessage = 'You must be a chama member to generate statements';
      } else if (e.toString().contains('whatsapp')) {
        errorMessage = 'Recipient must be on WhatsApp';
      }

      ToastUtils.showErrorToast(
        context,
        errorMessage,
        'Error',
      );
      return false;
    } finally {
      isSendingStatement(false);
    }
  }

  /// Generate kitty statement
  Future<bool> sendKittyStatementToWhatsApp({
    required BuildContext context,
    required int kittyId,
    required String? recipient,
    required String type, // "EXCEL" or "PDF"
    String? startDate,
    String? endDate,
    String? email,
    bool showCharges = true,
  }) async {
    try {
      isSendingStatement(true);

      // Get current user's phone number for validation
      final currentUser = Get.find<Eventcontroller>().getLocalUser();
      final currentUserPhone = currentUser?.phoneNumber;

      if (currentUserPhone == null) {
        ToastUtils.showErrorToast(
          context,
          'User session expired. Please login again.',
          'Error',
        );
        return false;
      }

      // Prepare request body according to API specification
      final requestBody = {
        'kitty_id': kittyId,
        'recipient': recipient,
        'type': type.toUpperCase(),
        'show_charges': showCharges,
      };

      // Add optional parameters
      if (startDate != null && startDate.isNotEmpty) {
        requestBody['start_date'] = startDate;
      }

      if (endDate != null && endDate.isNotEmpty) {
        requestBody['end_date'] = endDate;
      }

      if (email != null && email.isNotEmpty) {
        requestBody['email'] = email;
      }

      logger.i('Generating kitty statement request: $requestBody');

      final response = await apiProvider.request(
        url: ApiUrls.SEND_STATEMENT_WHATSAPP,
        method: Method.POST,
        params: requestBody,
      );

      if (response.data['status'] ?? false) {
        lastSendStatus.value = 'success';
        ToastUtils.showSuccessToast(
          context,
          response.data['message'] ?? 'Statement generated successfully',
          'Success',
        );
        return true;
      } else {
        lastSendStatus.value = 'failed';
        ToastUtils.showErrorToast(
          context,
          response.data['message'] ?? 'Failed to generate statement',
          'Error',
        );
        return false;
      }
    } catch (e) {
      logger.e('Error generating kitty statement: $e');
      lastSendStatus.value = 'error';

      String errorMessage = 'An error occurred while generating statement';
      if (e.toString().contains('delegate')) {
        errorMessage = 'You must be a kitty delegate to generate statements';
      } else if (e.toString().contains('whatsapp')) {
        errorMessage = 'Recipient must be on WhatsApp';
      }

      ToastUtils.showErrorToast(
        context,
        errorMessage,
        'Error',
      );
      return false;
    } finally {
      isSendingStatement(false);
    }
  }

  /// Show statement generation dialog
  Future<void> showWhatsAppStatementDialog({
    required BuildContext context,
    required int kittyId,
    String? kittyTitle,
  }) async {
    final currentUser = Get.find<Eventcontroller>().getLocalUser();
    final defaultPhone = currentUser?.phoneNumber ?? '';

    // Remove country code prefix if present for display
    String displayPhone = defaultPhone;
    if (displayPhone.startsWith(CountryConfig.dialCodeMinus)) {
      displayPhone = '0${displayPhone.substring(3)}';
    }

    final recipientController = TextEditingController(text: displayPhone);
    final emailController = TextEditingController();
    final startDateController = TextEditingController();
    final endDateController = TextEditingController();

    String selectedType = 'PDF';
    bool showCharges = true;
    PhoneNumber phoneNumber = CountryConfig.phoneNumber;

    DateTime? startDate;
    DateTime? endDate;

    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              scrollable: true,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              title: Row(
                children: [
                  const Expanded(child: Text('Generate Statement')),
                  const SizedBox(width: 8),
                  Tooltip(
                    message:
                        'Generate and send statement via WhatsApp or email',
                    child: Icon(
                      Icons.info_outline,
                      size: 16,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (kittyTitle != null) ...[
                      Text(
                        'Kitty: $kittyTitle',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                      const SizedBox(height: 16),
                    ],

                    // Recipient WhatsApp Number
                    CustomInternationalPhoneInput(
                      controller: recipientController,
                      label: 'WhatsApp Number (Optional)',
                      onInputChanged: (PhoneNumber number) {
                        phoneNumber = number;
                      },
                    ),
                    const SizedBox(height: 16),

                    // Email (Optional)
                    TextField(
                      controller: emailController,
                      decoration: const InputDecoration(
                        labelText: 'Email (Optional)',
                        hintText: '<EMAIL>',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.email),
                      ),
                      keyboardType: TextInputType.emailAddress,
                    ),
                    const SizedBox(height: 16),

                    // Document Type
                    DropdownButtonFormField<String>(
                      value: selectedType,
                      decoration: const InputDecoration(
                        labelText: 'Document Type',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.file_present),
                      ),
                      items: [
                        const DropdownMenuItem(
                            value: 'PDF', child: Text('PDF')),
                        const DropdownMenuItem(
                            value: 'EXCEL', child: Text('Excel')),
                      ],
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            selectedType = value;
                          });
                        }
                      },
                    ),
                    const SizedBox(height: 16),

                    // Start Date
                    TextField(
                      controller: startDateController,
                      decoration: const InputDecoration(
                        labelText: 'Start Date (Optional)',
                        border: OutlineInputBorder(),
                        suffixIcon: Icon(Icons.calendar_today),
                      ),
                      readOnly: true,
                      onTap: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate:
                              DateTime.now().subtract(const Duration(days: 30)),
                          firstDate: DateTime(2020),
                          lastDate: DateTime.now(),
                        );
                        if (date != null) {
                          setState(() {
                            startDate = date;
                            startDateController.text =
                                DateFormat('MMM dd, yyyy').format(date);
                          });
                        }
                      },
                    ),
                    const SizedBox(height: 16),

                    // End Date
                    TextField(
                      controller: endDateController,
                      decoration: const InputDecoration(
                        labelText: 'End Date (Optional)',
                        border: OutlineInputBorder(),
                        suffixIcon: Icon(Icons.calendar_today),
                      ),
                      readOnly: true,
                      onTap: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: DateTime.now(),
                          firstDate: DateTime(2020),
                          lastDate: DateTime.now(),
                        );
                        if (date != null) {
                          setState(() {
                            endDate = date;
                            endDateController.text =
                                DateFormat('MMM dd, yyyy').format(date);
                          });
                        }
                      },
                    ),
                    const SizedBox(height: 16),

                    // Options
                    CheckboxListTile(
                      title: const Text('Show Charges'),
                      value: showCharges,
                      onChanged: (value) {
                        setState(() {
                          showCharges = value ?? true;
                        });
                      },
                      controlAffinity: ListTileControlAffinity.leading,
                    ),
                  ],
                ),
              ),
              actions: [
                Row(
                  children: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('Cancel'),
                    ),
                    Expanded(
                      child: Obx(() => ElevatedButton.icon(
                            onPressed: isSendingStatement.value
                                ? null
                                : () async {
                                    // Validate that at least one of email or WhatsApp is provided
                                    final hasWhatsApp = recipientController.text
                                        .trim()
                                        .isNotEmpty;
                                    final hasEmail =
                                        emailController.text.trim().isNotEmpty;

                                    if (!hasWhatsApp && !hasEmail) {
                                      ToastUtils.showErrorToast(
                                        context,
                                        'Please provide either WhatsApp number or email address',
                                        'Validation Error',
                                      );
                                      return;
                                    }

                                    // Extract numeric phone number without country code
                                    String recipient = '';
                                    if (hasWhatsApp) {
                                      try {
                                        recipient = phoneNumber.phoneNumber
                                                ?.replaceAll("+", '') ??
                                            '';
                                      } catch (e) {
                                        ToastUtils.showErrorToast(
                                          context,
                                          'Please enter a valid phone number',
                                          'Validation Error',
                                        );
                                        return;
                                      }
                                    }

                                    final success =
                                        await sendKittyStatementToWhatsApp(
                                      context: context,
                                      kittyId: kittyId,
                                      recipient: recipient,
                                      type: selectedType,
                                      startDate:
                                          startDate?.toUtc().toIso8601String(),
                                      endDate:
                                          endDate?.toUtc().toIso8601String(),
                                      email:
                                          hasEmail ? emailController.text : '',
                                      showCharges: showCharges,
                                    );

                                    if (success) {
                                      Navigator.of(context).pop();
                                    }
                                  },
                            icon: const Icon(Icons.send),
                            label: Row(
                              children: [
                                if (isSendingStatement.value)
                                  const Padding(
                                    padding: EdgeInsets.all(8.0),
                                    child: CircularProgressIndicator(),
                                  ),
                                AutoSizeText(
                                  isSendingStatement.value
                                      ? 'Generating...'
                                      : 'Generate',
                                  maxLines: 1,
                                  minFontSize: 5,
                                ),
                              ],
                            ),
                          )),
                    ),
                  ],
                ),
              ],
            );
          },
        );
      },
    );
  }

  /// Show chama statement generation dialog
  Future<void> showWhatsAppChamaStatementDialog({
    required BuildContext context,
    required int? kittyId,
    String? chamaTitle,
  }) async {
    // Validate kittyId before showing dialog
    if (kittyId == null) {
      ToastUtils.showErrorToast(
        context,
        'Unable to generate statement: Missing chama information',
        'Error',
      );
      return;
    }
    final currentUser = Get.find<Eventcontroller>().getLocalUser();
    final defaultPhone = currentUser?.phoneNumber ?? '';

    // Remove country code prefix if present for display
    String displayPhone = defaultPhone;
    if (displayPhone.startsWith(CountryConfig.dialCodeMinus)) {
      displayPhone = '0${displayPhone.substring(3)}';
    }

    final recipientController = TextEditingController(text: displayPhone);
    final emailController = TextEditingController();
    final startDateController = TextEditingController();
    final endDateController = TextEditingController();

    String selectedType = 'PDF';
    bool showCharges = true;
    PhoneNumber phoneNumber = CountryConfig.phoneNumber;

    DateTime? startDate;
    DateTime? endDate;

    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              title: Row(
                children: [
                  const Expanded(child: Text('Generate Statement')),
                  const SizedBox(width: 8),
                  Tooltip(
                    message:
                        'Generate and send statement via WhatsApp or email',
                    child: Icon(
                      Icons.info_outline,
                      size: 16,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (chamaTitle != null) ...[
                      Text(
                        'Chama: $chamaTitle',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                      const SizedBox(height: 16),
                    ],

                    // Recipient WhatsApp Number
                    CustomInternationalPhoneInput(
                      controller: recipientController,
                      label: 'WhatsApp Number (Optional)',
                      onInputChanged: (PhoneNumber number) {
                        phoneNumber = number;
                      },
                    ),
                    const SizedBox(height: 16),

                    // Email (Optional)
                    TextField(
                      controller: emailController,
                      decoration: const InputDecoration(
                        labelText: 'Email (Optional)',
                        hintText: '<EMAIL>',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.email),
                      ),
                      keyboardType: TextInputType.emailAddress,
                    ),
                    const SizedBox(height: 16),

                    // Document Type
                    DropdownButtonFormField<String>(
                      value: selectedType,
                      decoration: const InputDecoration(
                        labelText: 'Document Type',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.file_present),
                      ),
                      items: [
                        const DropdownMenuItem(
                            value: 'PDF', child: Text('PDF')),
                        const DropdownMenuItem(
                            value: 'EXCEL', child: Text('Excel')),
                      ],
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            selectedType = value;
                          });
                        }
                      },
                    ),
                    const SizedBox(height: 16),

                    // Start Date
                    TextField(
                      controller: startDateController,
                      decoration: const InputDecoration(
                        labelText: 'Start Date (Optional)',
                        border: OutlineInputBorder(),
                        suffixIcon: Icon(Icons.calendar_today),
                      ),
                      readOnly: true,
                      onTap: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate:
                              DateTime.now().subtract(const Duration(days: 30)),
                          firstDate: DateTime(2020),
                          lastDate: DateTime.now(),
                        );
                        if (date != null) {
                          setState(() {
                            startDate = date;
                            startDateController.text =
                                DateFormat('MMM dd, yyyy').format(date);
                          });
                        }
                      },
                    ),
                    const SizedBox(height: 16),

                    // End Date
                    TextField(
                      controller: endDateController,
                      decoration: InputDecoration(
                        labelText: "${'end_date'.tr} (Optional)",
                        border: const OutlineInputBorder(),
                        suffixIcon: const Icon(Icons.calendar_today),
                      ),
                      readOnly: true,
                      onTap: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: DateTime.now(),
                          firstDate: DateTime(2020),
                          lastDate: DateTime.now(),
                        );
                        if (date != null) {
                          setState(() {
                            endDate = date;
                            endDateController.text =
                                DateFormat('MMM dd, yyyy').format(date);
                          });
                        }
                      },
                    ),
                    const SizedBox(height: 16),

                    // Options
                    CheckboxListTile(
                      title: const Text('Show Charges'),
                      value: showCharges,
                      onChanged: (value) {
                        setState(() {
                          showCharges = value ?? true;
                        });
                      },
                      controlAffinity: ListTileControlAffinity.leading,
                    ),
                  ],
                ),
              ),
              actions: [
                Row(
                  children: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('Cancel'),
                    ),
                    Expanded(
                      flex: 2,
                      child: Obx(() => ElevatedButton.icon(
                            onPressed: isSendingStatement.value
                                ? null
                                : () async {
                                    // Validate that at least one of email or WhatsApp is provided
                                    final hasWhatsApp = recipientController.text
                                        .trim()
                                        .isNotEmpty;
                                    final hasEmail =
                                        emailController.text.trim().isNotEmpty;

                                    if (!hasWhatsApp && !hasEmail) {
                                      ToastUtils.showErrorToast(
                                        context,
                                        'Please provide either WhatsApp number or email address',
                                        'Validation Error',
                                      );
                                      return;
                                    }

                                    // Extract numeric phone number without country code
                                    String recipient = '';
                                    if (hasWhatsApp) {
                                      try {
                                        recipient = phoneNumber.phoneNumber
                                                ?.replaceAll('+', '') ??
                                            '';
                                      } catch (e) {
                                        ToastUtils.showErrorToast(
                                          context,
                                          'Please enter a valid phone number',
                                          'Validation Error',
                                        );
                                        return;
                                      }
                                    }

                                    final success =
                                        await sendChamaStatementToWhatsApp(
                                      context: context,
                                      kittyId: Get.find<TransactionController>()
                                          .currentKittyId
                                          .value,
                                      recipient: recipient,
                                      type: selectedType,
                                      startDate:
                                          startDate?.toUtc().toIso8601String(),
                                      endDate:
                                          endDate?.toUtc().toIso8601String(),
                                      email:
                                          hasEmail ? emailController.text : '',
                                      showCharges: showCharges,
                                    );

                                    if (success) {
                                      Navigator.of(context).pop();
                                    }
                                  },

                            // icon: (Icons.send),
                            label: Text(isSendingStatement.value
                                ? 'Generating...'
                                : 'Generate'),
                          )),
                    ),
                  ],
                ),
              ],
            );
          },
        );
      },
    );
  }
}
