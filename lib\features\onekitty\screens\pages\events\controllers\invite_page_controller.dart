import 'package:flutter/material.dart';
import 'package:onekitty_admin/models/events/operators_model.dart';
import 'package:onekitty_admin/services/api_urls.dart';
import 'package:onekitty_admin/services/custom_logger.dart';
import 'package:onekitty_admin/services/http_service.dart';
import 'package:onekitty_admin/utils/crud_navigation_helper.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:dio/dio.dart';

class InvitePageController extends GetxController implements GetxService {
  RxList<OperatorsModel> operators = <OperatorsModel>[].obs;
  final dio = Dio();
  RxBool isLoadingOperators = false.obs;
  RxBool isAddingOperators = false.obs;
  RxBool isUpdatingOperator = false.obs;

  final logger = Logger(filter: CustomLogFilter());

  final HttpService apiProvider = Get.find();

  /// Clears any form data that might be stored in reactive variables
  void clearFormData() {
    // This method can be extended to clear any reactive variables
    // that store form data if they are added in the future
    // For now, it serves as a placeholder for consistent API
  }

  /// Handles successful operator operations with proper navigation and cleanup
  void handleOperatorSuccess({
    required BuildContext context,
    required String operation,
    List<TextEditingController>? controllers,
  }) {
    CrudNavigationHelper.handleOperatorCrudSuccess(
      context: context,
      operation: operation,
      controllers: controllers,
    );
  }

  Future<void> addOperator(int kittyId, String firstname, String lastname,
      String role, String phone, bool isSignatory, bool adminLocked) async {
    isAddingOperators.value = true;
    try {
      var data = {
        "kitty_id": kittyId,
        "first_name": firstname,
        "last_name": lastname,
        "role": role,
        "user_id": null,
        "is_signatory": isSignatory,
        "status": "ACTIVE",
        "phone_number": phone,
        "whatsapp_number": phone,
        "email": "",
        "is_admin_locked": adminLocked,
      };
      final response = await apiProvider.request(
        method: Method.POST,
        params: data,
        url: ApiUrls.ADDDELEGATES,
      );
      if (response.data['status'] ?? false) {
        Get.snackbar('Success',
            response.data['message'] ?? "Operator added successfully",
            backgroundColor: Colors.green);
        await fetchOperators(kittyId);

        // Clear any reactive variables that might hold form data
        clearFormData();
      } else {
        Get.snackbar(
            'Error', response.data['message'] ?? "Failed to add operator",
            backgroundColor: Colors.red);
      }
    } catch (e) {
      Get.snackbar('Error', 'Failed to add operator');
    } finally {
      isAddingOperators.value = false;
    }
  }

  Future<void> fetchOperators(int kittyId) async {
    isLoadingOperators.value = true;
    try {
      var response = await apiProvider.request(
          method: Method.GET,
          url:
              "${ApiUrls.GETDELEGATES}?kitty_id=$kittyId&show_signatories=FALSE",
          params: {"kitty_id": kittyId, "show_signatories": false});
      if (response.data['status'] ?? false) {
        final returnedData = response.data['data'] as List;
        operators.value =
            returnedData.map((item) => OperatorsModel.fromJson(item)).toList();
      } else {
        Get.snackbar('Error',
            '${response.data['message'] ?? "Failed to fetch operators"}',
            backgroundColor: Colors.red);
      }
    } catch (e) {
      Get.snackbar('Error', 'Failed to fetch operators',
          backgroundColor: Colors.red);
    } finally {
      isLoadingOperators.value = false;
    }
  }

  Future<void> deleteOperator(int id, int kittyId) async {
    isLoadingOperators.value = true;
    try {
      final response = await apiProvider.request(
        method: Method.DELETE,
        params: {
          "kitty_id": kittyId,
        },
        url: "${ApiUrls.ADDDELEGATES}$id",
      );

      if (response.data != null && response.data['status']) {
        fetchOperators(kittyId);
        Get.snackbar('Success!',
            response.data['message'] ?? 'successfully removed operator');
      } else {
        Get.snackbar('Error!',
            '${response.data['message'] ?? 'successfully removed operator'}',
            backgroundColor: Colors.red);
      }
    } catch (e) {
      Get.snackbar('Error', 'Failed to delete operator',
          backgroundColor: Colors.red);
    } finally {
      isLoadingOperators.value = false;
    }
  }

  Future<void> updateOperator(Map<String, dynamic> _data, int kittyId) async {
    isUpdatingOperator.value = true;

    try {
      final response = await apiProvider.request(
        method: Method.PUT,
        params: _data,
        url: ApiUrls.ADDDELEGATES,
      );
      if (response.data["status"] ?? false) {
        Get.snackbar('Success', response.data['message'] ?? "updated",
            backgroundColor: Colors.green);
        if (response.data['data'] != null) {
          try {
            final returnedData = response.data['data'] as List;
            operators.value = returnedData
                .map((item) => OperatorsModel.fromJson(item))
                .toList();
            // ignore: empty_catches
          } catch (e) {}
        }
        await fetchOperators(kittyId);

        // Clear any reactive variables that might hold form data
        clearFormData();
      } else {
        Get.snackbar(
            'Error!', '${response.data['message'] ?? 'Failed update operator'}',
            backgroundColor: Colors.red);
      }
    } catch (e) {
      Get.snackbar('Error', 'Failed update operator',
          backgroundColor: Colors.red);
    } finally {
      isUpdatingOperator.value = false;
    }
  }
}
