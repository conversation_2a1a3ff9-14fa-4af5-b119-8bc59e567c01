import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/controllers/verify_ticket_controller.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:mobile_scanner/mobile_scanner.dart';

class QrVerifyTicket extends StatefulWidget {
  final int eventId;
  const QrVerifyTicket({super.key, required this.eventId});

  @override
  State<QrVerifyTicket> createState() => _QRScannerScreenState();
}

class _QRScannerScreenState extends State<QrVerifyTicket> {
  final controller = Get.put(VerifyTicketController());
  final ticketCodeController = TextEditingController();
  MobileScannerController? cameraController;
  bool isScanning = true;
  bool hasCameraPermission = false;
  bool isFlashOn = false;
  bool hasScannedCode = false;

  @override
  void initState() {
    super.initState();
    checkCameraPermission();
  }

  Future<void> checkCameraPermission() async {
    final status = await Permission.camera.status;
    setState(() {
      hasCameraPermission = status.isGranted;
    });
    if (hasCameraPermission) {
      initializeCamera();
    }
  }

  void initializeCamera() {
    try {
      cameraController = MobileScannerController();
    } catch (e) {
      print('Error initializing camera: $e');
    }
  }

  void onDetect(BarcodeCapture capture) {
    if (hasScannedCode || controller.isVerifying.value) return;
    
    final List<Barcode> barcodes = capture.barcodes;
    for (final barcode in barcodes) {
      if (barcode.rawValue != null && barcode.rawValue!.isNotEmpty) {
        setState(() => hasScannedCode = true);
        
        // Haptic feedback
        HapticFeedback.mediumImpact();
        
        // Visual feedback
        _showScanSuccess();
        
        controller.verifyTicket(widget.eventId, barcode.rawValue!);
        print('Barcode found! ${barcode.rawValue}');
        
        // Reset after delay
        Future.delayed(const Duration(seconds: 2), () {
          if (mounted) setState(() => hasScannedCode = false);
        });
        break;
      }
    }
  }
  
  void _showScanSuccess() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Code scanned successfully!'),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 1),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body:
          !hasCameraPermission ? _buildPermissionRequest() : _buildScannerUI(),
    );
  }

  Widget _buildPermissionRequest() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.camera_alt, size: 100, color: Colors.white),
          const SizedBox(height: 20),
          Text(
            'camera_permission_required'.tr,
            style: const TextStyle(color: Colors.white, fontSize: 20),
          ),
          const SizedBox(height: 20),
          ElevatedButton(
            onPressed: () async {
              final status = await Permission.camera.request();
              setState(() {
                hasCameraPermission = status.isGranted;
              });
              if (hasCameraPermission) {
                initializeCamera();
              }
            },
            child: Text('grant_permission'.tr),
          ),
        ],
      ),
    );
  }

  Widget _buildScannerUI() {
    return Stack(
      children: [
        if (cameraController != null)
          MobileScanner(
            controller: cameraController!,
            onDetect: onDetect,
          ),
        ColorFiltered(
          colorFilter: ColorFilter.mode(
            Colors.black.withOpacity(0.5),
            BlendMode.srcOut,
          ),
          child: Stack(
            children: [
              Container(
                decoration: const BoxDecoration(
                  color: Colors.transparent,
                  backgroundBlendMode: BlendMode.dstOut,
                ),
              ),
              Center(
                child: Container(
                  height: 250,
                  width: 250,
                  decoration: BoxDecoration(
                    color: Colors.black,
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ],
          ),
        ),
        SafeArea(
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    IconButton(
                      icon: Icon(
                        isFlashOn ? Icons.flash_on : Icons.flash_off,
                        color: Colors.white,
                      ),
                      onPressed: () {
                        cameraController?.toggleTorch();
                        setState(() => isFlashOn = !isFlashOn);
                      },
                    ),
                    IconButton(
                      icon: const Icon(Icons.flip_camera_ios,
                          color: Colors.white),
                      onPressed: () => cameraController?.switchCamera(),
                    ),
                  ],
                ),
              ),
              const Spacer(),
              Container(
                width: 250,
                height: 250,
                decoration: BoxDecoration(
                  border: Border.all(
                    color: hasScannedCode ? Colors.green : Colors.white,
                    width: hasScannedCode ? 3 : 2,
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: hasScannedCode
                    ? const Center(
                        child: Icon(
                          Icons.check_circle,
                          color: Colors.green,
                          size: 50,
                        ),
                      )
                    : null,
              ),
              const SizedBox(height: 20),
              Text(
                hasScannedCode ? 'Processing...' : 'Position QR code within the frame',
                style: const TextStyle(color: Colors.white, fontSize: 16),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 10),
              TextButton(
                onPressed: () {
                  setState(() => isScanning = false);
                  Navigator.pop(context);
                },
                child: Text(
                  'cancel_scanning'.tr,
                  style: const TextStyle(color: Colors.white),
                ),
              ),
              const Spacer(),
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16.0),
                      decoration: const BoxDecoration(
                        color: Colors.amber,
                        shape: BoxShape.circle,
                      ),
                      child: IconButton(
                        icon: const Icon(Icons.center_focus_strong),
                        onPressed: () {},
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  @override
  void dispose() {
    cameraController?.dispose();
    super.dispose();
  }
}
