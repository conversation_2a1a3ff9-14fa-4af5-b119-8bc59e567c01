// account_blocked_controller.dart
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onekitty_admin/services/auth_manager.dart';
import 'package:url_launcher/url_launcher.dart';

import 'login_screen.dart';

class AccountBlockedController extends GetxController {
  void writeToSupport() {
    final Uri emailLaunchUri =
        Uri(scheme: 'mailto', path: '<EMAIL>', queryParameters: {
      'subject': 'Account Blocked-Support Request',
    });
    launchUrl(emailLaunchUri);
  }

  void dismissDialog() {
    Get.back();
  }
}

class BlockedPage extends StatefulWidget {
  const BlockedPage({super.key});

  @override
  State<BlockedPage> createState() => _BlockedPageState();
}

class _BlockedPageState extends State<BlockedPage> {
  final controller = Get.put(AccountBlockedController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
      ),
      body: Padding(
        padding: const EdgeInsets.all(14.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Close button aligned to the right
            Align(
              alignment: Alignment.topRight,
              child: IconButton(
                icon: const Icon(Icons.close),
                onPressed: Get.back,
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
              ),
            ),

            // Warning icon
            Container(
              width: 40,
              height: 40,
              decoration: const BoxDecoration(
                color: Color(0xFFFF5252),
                shape: BoxShape.circle,
              ),
              child: const Center(
                child: Icon(
                  Icons.info_outline,
                  color: Colors.white,
                  size: 24,
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Title
            Text(
              'your_account_has_been_blocked'.tr,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),

            // Description
            Text(
              'account_blocked_description'.tr,
              style: const TextStyle(
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            const Spacer(),
            // Write to support button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: controller.writeToSupport,
                style: ElevatedButton.styleFrom(
                  // backgroundColor: ,
                  foregroundColor: Colors.black,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text('write_to_support'.tr),
              ),
            ),
            const SizedBox(height: 12),

            // Later button
            TextButton(
              onPressed: () {
                AuthenticationManager authenticationManager = Get.find();
                authenticationManager.logOut();
                Get.offAll(
                  () => const LoginScreen(),
                  transition: Transition.leftToRightWithFade,
                );
              },
              child: Text(
                'logout'.tr,
                style: const TextStyle(
                  color: Colors.grey,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Example usage:
void showBlockedPage() {
  Get.dialog(
    const BlockedPage(),
    barrierDismissible: false,
  );
}
