import 'package:onekitty_admin/features/onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty_admin/helpers/show_toast.dart';
import 'package:onekitty_admin/models/chama/chama_model.dart';
import 'package:onekitty_admin/models/contr_kitty_model.dart';
import 'package:onekitty_admin/features/login/controllers/auth_controller.dart'; 
import 'package:onekitty_admin/models/kitty_model.dart';
import 'package:onekitty_admin/models/user_kitties_model.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/views/widgets/mpesa_transaction_widget.dart';
import 'package:onekitty_admin/services/api_urls.dart';
import 'package:onekitty_admin/services/http_service.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:logger/logger.dart';
import 'package:onekitty_admin/utils/cache_keys.dart';
class ContributeController extends GetxController implements GetxService {
  final HttpService apiProvider = Get.find();
  final box = Get.find<GetStorage>();
  final logger = Get.find<Logger>();
  final RxString apiMessage = ''.obs;
  final RxString apiMessageContri = ''.obs;

  final RxString apiMessageProcess = ''.obs;

  final RxString urlKit = ''.obs;
  final RxString kittStatus = ''.obs;

  final RxBool isloading = false.obs;
  final RxBool chargeLoading = false.obs;
  final RxBool isgetloading = false.obs;

  final RxBool isgetkittyloading = false.obs;
  final RxBool isProcessloading = false.obs;
  final RxBool isContributeloading = false.obs;
  final isFromDeepLink = false.obs;
  final RxBool getStatus = false.obs;
  final RxMap contributeData = {}.obs;
  final Rx<dynamic> toggleData = Rx<dynamic>(null);

  final RxString totalAmount = "".obs;
  final RxString volumes = "".obs;
  final RxString status = "".obs;

  final RxBool confirmpayLoading = false.obs;
  final RxMap resContrData = {}.obs;
  final RxList<Notifications> whatsappList = <Notifications>[].obs;
  final Rx<Notifications> notificationsGot = Notifications().obs;
  final RxString whatsappStatus = ''.obs;
  final Rx<UserKitty> singleKitty = UserKitty().obs;
  final RxInt kittyType = 0.obs;
  final hasSignatoryTransactions = false.obs;

  final Rx<Kitty> kittGoten = Kitty().obs;

  final RxMap contrisocials = {}.obs;

  final AuthenticationController authController =
      Get.put(AuthenticationController());

  String? get userPhoneNumber => authController.usermodel.value.phoneNumber;
  final RxList<KittyMediaModel> kittyMedia = <KittyMediaModel>[].obs;

  final description = ''.obs;

  Future<bool> report({
    required int kittyId,
    required String title,
    required String description,
    String? email,
    String? phoneNumber,
    String? imeiCode,
    String? deviceModel,
    double? latitude,
    double? longitude,
  }) async {
    isloading(true);
    try {
      var res = await apiProvider.request(
        url: ApiUrls.report_kitty,
        method: Method.POST,
        params: {
          "kitty_id": kittyId,
          "title": title,
          "description": description,
          "phone_number": phoneNumber ?? userPhoneNumber,
          "type": "REPORT_KITTY",
          "email": email ?? '',
          "imei_code": imeiCode ?? '',
          "device_model": box.read(CacheKeys.deviceModel) ?? "",
          "latitude": latitude,
          "longitude": longitude,
        },
      );

      if (res.data["status"] ?? false) {
        ToastUtils.showToast(res.data["message"]);
        return true;
      } else {
        ToastUtils.showToast(res.data["message"], toastType: ToastType.error);
        return false;
      }
    } catch (e) {
      logger.e(e);
      ToastUtils.showToast("An error occurred", toastType: ToastType.error);
      return false;
    } finally {
      isloading(false);
    }
  }

  Future<bool> getKitty({required int? id}) async {
    try {
      isgetkittyloading(true);
      hasSignatoryTransactions(false);
      update();
      var res = await apiProvider.request(
        url: ApiUrls.get_kitty + id.toString(),
        method: Method.GET,
      );
      if (res.statusCode == 401) {
        // use the unauthorized api
        res = await apiProvider.request(
          url: ApiUrls.get_kitty_by_id + id.toString(),
          method: Method.GET,
        );
      }
      apiMessage(res.data["message"]);
      getStatus(res.data["status"]);
      isgetkittyloading(false);
      if (res.data["status"] ?? false) {
        hasSignatoryTransactions(
            res.data["data"]["has_signatory_transaction"] ?? false);
        singleKitty(UserKitty.fromJson(res.data["data"]));
        kittGoten(Kitty.fromJson(res.data["data"]["kitty"]));
        kittStatus(res.data["data"]["kitty_status"]);
        volumes(res.data["data"]["volumes"].toString());
        kittyType(res.data["data"]["kitty"]["kitty_type"]);
        final List _media = res.data["data"]["kitty"]["media"] ?? [];
        kittyMedia.value = _media
            .map((e) => KittyMediaModel.fromJson(e as Map<String, dynamic>))
            .toList();
        description(res.data['data']['description'] ?? '');
        isgetkittyloading(false);
        urlKit.value = 'https://www.onekitty.co.ke/kitty/${kittGoten.value.iD}';
        if (kittyType.value == 4) {
          Get.find<ChamaDataController>().chama(
            UserChama(
              chama: Chama.fromJson(res.data["data"]["kitty"]).copyWith(
                kittyId: res.data["data"]["kitty"]['ID'],
                status: res.data["data"]['kitty_status'],
                nextOccurrence: res.data["data"]["kitty"]['end_date'] == null
                    ? null
                    : DateTime.parse(res.data["data"]["kitty"]['end_date']),
              ),
            ),
          );
        }
        update();

        return true;
      } else {
        return false;
      }
    } catch (e) {
      logger.e(e);
      return false;
    } finally {
      isgetkittyloading(false);
    }
  }

  Future<bool> getWhatsapp({required int id}) async {
    isgetloading(true);
    update();
    try {
      var res = await apiProvider.request(
        url: ApiUrls.notifications + id.toString(),
        method: Method.GET,
      );
      apiMessage(res.data["message"]);
      getStatus(res.data["status"]);
      isgetloading(false);
      if (res.data["status"]) {
        whatsappList([]);
        for (var element in res.data["data"]["accounts"] ?? []) {
          whatsappList.add(Notifications.fromJson(element));
        }
        update();
        return true;
      } else {
        return false;
      }
    } catch (e) {
      logger.e(e);
      isgetloading(false);
      return false;
    }
  }

  RmWhatsapp({
    required int kittyId,
    required int notificationId,
  }) async {
    isloading(true);
    try {
      var res = await apiProvider.request(
        url:
            '${ApiUrls.remove_kitty_whatsapp}?kitty_id=$kittyId&notification_id=$notificationId',
        method: Method.DELETE,
      );

      apiMessage(res.data["message"]);
      if (res.data["status"] ?? false) {
        ToastUtils.showToast(res.data["message"]);
        return true;
      } else {
        ToastUtils.showToast(res.data["message"], toastType: ToastType.error);
        return false;
      }
    } catch (e) {
      logger.e(e);
      ToastUtils.showToast("An error occured", toastType: ToastType.error);

      return false;
    } finally {
      isloading(false);
    }
  }

  toggleWhatsapp(
      {required int id, required int kittyid, required String status}) async {
    isloading(true);
    try {
      var res = await apiProvider.request(
          url: ApiUrls.toggleWhatsapp,
          method: Method.POST,
          params: {"id": id, "kitty_id": kittyid, "status": status});

      apiMessage(res.data["message"]);
      if (res.data["status"]) {
        toggleData(res.data["data"]);
        return true;
      } else {
        isloading(false);
        apiMessage(res.data["message"]);
        return false;
      }
    } catch (e) {
      isloading(false);
      logger.e(e);
      apiMessage("An error occured");
      return false;
    }
  }

  Future<bool> contribute(
      {required String phoneNumber,
      required int amount,
      required int channel,
      required int kittyId,
      required bool shownames,
      required bool shownumber,
      String? firstName,
      String? secondName,
      String? email,
      String? paymentRef}) async {
    isContributeloading(true);
    try {
      var res = await apiProvider
          .request(url: ApiUrls.contribute_kitty, method: Method.POST, params: {
        "amount": amount,
        "kitty_id": kittyId,
        "phone_number": phoneNumber,
        "channel_code": channel,
        "first_name": firstName ?? '',
        "second_name": secondName ?? '',
        "show_names": shownames,
        "show_number": shownumber,
        "payer_email": email,
        "payment_ref": paymentRef
      });

      if (res.data["status"]) {
        contributeData(res.data["data"]);
        apiMessageContri(res.data["message"]);
        contrisocials(res.data["data"]["social_messages"]);

        Get.defaultDialog(
            title: 'Transaction Processing',
            content: MPESATransactionWidget(
                response: MpesaTransferResponseModel.fromJson(res.data),));
        isContributeloading(false);
        return true;
      } else {
        apiMessageContri(res.data["message"]);
        isContributeloading(false);
        return false;
      }
    } catch (e) {
      logger.e(e);
      isloading(false);
      apiMessageContri('An error occured');

      return false;
    }
  }

  Future<bool> contributeProcess(
      {required String otp, required String transId}) async {
    isProcessloading(true);
    try {
      var res = await apiProvider
          .request(url: ApiUrls.contrProcess, method: Method.POST, params: {
        "transaction_id_internal": transId,
        "otp": otp.toString(),
      });
      apiMessageProcess(res.data["message"]);
      isProcessloading(false);
      if (res.data["status"]) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      isProcessloading(false);
      apiMessageProcess('An error occured');

      logger.e(e);
      return false;
    }
  }

  Future<bool> contributePayment({
    required String phoneNumber,
    required int amount,
    required int channel,
    required int kittyId,
    String? firstName,
    String? secondName,
  }) async {
    isloading(true);
    try {
      var res = await apiProvider.request(
        url: ApiUrls.pay_payment_kitty,
        method: Method.POST,
        params: {
          "amount": amount,
          "kitty_id": kittyId,
          "phone_number": phoneNumber,
          "channel_code": channel,
          "first_name": firstName ?? '',
          "second_name": secondName ?? '',
        },
      );
      isloading(false);
      if (res.data["status"]) {
        contributeData(res.data["data"]);
        apiMessageContri(res.data["message"]);

        return true;
      } else {
        apiMessageContri(res.data["message"]);

        return false;
      }
    } catch (e) {
      logger.e(e);
      isloading(false);
      apiMessageContri('An error occured');

      return false;
    }
  }

  Future<bool> confirmContribution({
    required String checkoutId,
  }) async {
    try {
      var res = await apiProvider.request(
          url: ApiUrls.confirmPay,
          method: Method.POST,
          params: {"checkout_request_id": checkoutId});
      if (res.data["status"]) {
        resContrData(res.data["data"]["transaction"]);
        status(res.data["data"]["transaction"]["status"]);

        return true;
      } else {
        apiMessageContri(res.data["message"]);

        return false;
      }
    } catch (e) {
      logger.e(e);
      apiMessageContri('An error occured');

      return false;
    }
  }

  Future<bool> getCharges({
    required int amount,
    required int channel,
  }) async {
    chargeLoading(true);
    try {
      var res = await apiProvider.request(
          url: ApiUrls.charges,
          method: Method.POST,
          params: {"amount": amount, "channel": channel});
      chargeLoading(false);
      if (res.data["status"]) {
        totalAmount(res.data["data"]["total_amount"].toString());

        return true;
      } else {
        apiMessageContri(res.data["message"]);
        return false;
      }
    } catch (e) {
      logger.e(e);
      chargeLoading(false);
      apiMessageContri('An error occured');

      return false;
    }
  }
}

class NotificationDataController extends GetxController {
  var whatsappId = RxInt(0);
  var kittyId = RxInt(0);
  var status = RxString('');
}

class WhatsappController extends GetxController {
  RxInt selectedId = RxInt(0);

  void setSelectedId(int id) {
    selectedId.value = id;
  }
}
