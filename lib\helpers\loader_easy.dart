import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:onekitty_admin/utils/themes_colors.dart';

class Loader {
  static void dismiss() {
    // if (EasyLoading.isShow) {
    //   EasyLoading.dismiss();
    // }
  }

  static showLoader({String? message}) {
    // EasyLoading.show(
    //   status: message ?? 'loading..',
    //   indicator: SizedBox(
    //     width: 60.sp,
    //     height: 65.sp,
    //     child: SpinKitCircle(
    //       color: ColorUtil.blueColor,
    //       size: 30.sp,
    //     ),
    //   ),
    //   dismissOnTap: true,
    //   maskType: EasyLoadingMaskType.custom,
    // );
  }

  static void configLoader() {
    // EasyLoading.instance
    //   ..displayDuration = const Duration(milliseconds: 3000)
    //   ..indicatorType = EasyLoadingIndicatorType.fadingCircle
    //   ..loadingStyle = EasyLoadingStyle.light
    //   ..indicatorSize = 45.0
    //   ..radius = 10.0
    //   ..progressColor = ColorUtil.blueColor
    //   ..backgroundColor = ColorUtil.blueColor
    //   ..indicatorColor = ColorUtil.blueColor
    //   ..textColor = ColorUtil.blueColor
    //   ..maskColor = Colors.black.withOpacity(0.25)
    //   ..userInteractions = true
    //   ..dismissOnTap = false;
  }
}
