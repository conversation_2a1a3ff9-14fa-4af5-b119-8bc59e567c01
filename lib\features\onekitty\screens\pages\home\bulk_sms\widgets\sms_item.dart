import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:onekitty_admin/models/bulkSms/singleMsg.dart';


import '../../../../../../../../utils/utils_exports.dart';

class SinglesmsItem extends StatelessWidget {
  final int index;
  final MsgItem item;
  const SinglesmsItem({super.key, required this.index, required this.item});
  @override
  Widget build(BuildContext context,) {
   
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Opacity(
          opacity: 0.5,
          child: Padding(
            padding: EdgeInsets.only(
              top: 6.h,
              bottom: 8.h,
            ),
            child: Text(
             "${index + 1}",
              style: theme.textTheme.titleSmall,
            ),
          ),
        ),
        CustomImageView(
          imagePath: AssetUrl.imgPerson,
          height: 36.h,
          width: 36.w,
          margin: EdgeInsets.only(left: 6.w),
        ),
        Padding(
          padding: EdgeInsets.only(
            left: 6.w,
            top: 6.h,
            bottom: 8.h,
          ),
          child: Padding(
            padding: const EdgeInsets.only(right:50.0),
            child: Text(
              item.recipientPhone??"",
              style: theme.textTheme.bodyMedium,
            ),
          ),
        ),
        _buildStatus(context),
      ],
    );
  }

  /// Section Widget
  Widget _buildStatus(BuildContext context) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            color:_getColorForStatus(item.status).withOpacity(0.2)),
        //height: 24.h,
        margin: EdgeInsets.symmetric(vertical: 6.h),
        child: Center(
          child: Text(
            item.status ?? "",
            overflow: TextOverflow.ellipsis,
            style: TextStyle(color:_getColorForStatus(item.status), fontWeight: FontWeight.bold,fontSize: 10),
          ),
        ),
      ),
    );
  }
}

Color _getColorForStatus(String? status) {
  switch (status) {
    case 'SENT':
      return const Color(0xFF0A7637); 
      case 'DeliveredToTerminal':
      return const Color(0xFF0A7637); // Green color for success
    case 'PENDING':
      return const Color(0xFF005FDB); 
      case 'SenderName Blacklisted':
      return const Color(0xFFCA150C); 

      case 'FAILED':
      return const Color(0xFFCA150C); 
    default:
      return Colors.black; 
  }
}
