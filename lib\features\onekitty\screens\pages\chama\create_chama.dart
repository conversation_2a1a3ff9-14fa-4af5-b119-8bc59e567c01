import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty_admin/features/onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty_admin/nav_routes/nav_routes.dart';

import '../../../../../../../../utils/utils_exports.dart';

class CrtChama extends StatefulWidget {
  const CrtChama({super.key});

  @override
  State<CrtChama> createState() => _CrtChamaState();
}

class _CrtChamaState extends State<CrtChama> {
  final ChamaController _chamaController = Get.put(ChamaController());
  @override
  void initState() {
    _chamaController.getConfigs();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: double.maxFinite,
        padding: EdgeInsets.symmetric(
          horizontal: 25.w,
          vertical: 42.h,
        ),
        child: SingleChildScrollView(
          child: Column(
            children: [
              CustomImageView(
                color: Colors.blue,
                imagePath: AssetUrl.imgGroup10,
                height: 265.h,
                width: 366.w,
              ),
              SizedBox(height: 30.h),
              Text(
                'ready_to_grow_together'.tr,
                style: CustomTextStyles.titleLargeGray900,
              ),
              SizedBox(height: 7.h),
              Container(
                width: 350.w,
                margin: EdgeInsets.symmetric(horizontal: 10.w),
                child: Text(
                  'start_your_chama_build_wealth'.tr,
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                  style: CustomTextStyles.bodyLargeOnPrimaryContainer,
                ),
              ),
              SizedBox(height: 20.h),
              CustomElevatedButton(
                onPressed: () {
                  Get.toNamed(NavRoutes.chamaStepper);
                },
                text: 'create_a_chama'.tr,
                buttonTextStyle: CustomTextStyles.titleSmallWhiteA700,
              ),
              SizedBox(height: 20.h),
            ],
          ),
        ),
      ),
    );
  }
}
