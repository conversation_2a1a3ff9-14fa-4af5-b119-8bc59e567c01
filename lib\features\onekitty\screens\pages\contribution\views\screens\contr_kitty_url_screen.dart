import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty_admin/features/onekitty/controllers/contribute_controller.dart';
import 'package:onekitty_admin/helpers/show_snack_bar.dart';
import 'package:onekitty_admin/nav_routes/nav_routes.dart';
import '../../../../../../../../utils/utils_exports.dart';

// ignore: must_be_immutable
class EnteringUrlForAKittyScreen extends StatelessWidget {
  EnteringUrlForAKittyScreen({super.key});
  final ContributeController contributeController = Get.find();
  final kittyUrlController = TextEditingController();
  final greeting = getGreeting();

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        body: Form(
          child: SingleChildScrollView(
            child: Container(
              width: double.maxFinite,
              padding: EdgeInsets.symmetric(
                horizontal: 30.w,
                vertical: 23.h,
              ),
              child: Column(
                children: [
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Padding(
                      padding: EdgeInsets.only(left: 2.w),
                      child: Row(
                        children: [
                          CustomImageView(
                            imagePath: AssetUrl.imgEllipse1,
                            height: 44.h,
                            width: 44.w,
                            radius: BorderRadius.circular(
                              22.w,
                            ),
                          ),
                          Container(
                            width: 106.w,
                            margin: EdgeInsets.only(left: 6.w),
                            child: Text(
                              greeting,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              style: CustomTextStyles.titleSmallGray900,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(height: 5.h),
                  SizedBox(
                    height: 180.h,
                    width: 314.w,
                    child: CustomImageView(
                      imagePath: AssetUrl.imgGroup6,
                      height: 159.h,
                      width: 215.w,
                      alignment: Alignment.bottomCenter,
                    ),
                  ),
                  SizedBox(height: 33.h),
                  Text(
                    "contribute_to_a_kitty".tr,
                    style: CustomTextStyles.titleLargeBlack900,
                  ),
                  SizedBox(height: 10.h),
                  Container(
                    width: 347.w,
                    margin: EdgeInsets.only(
                      left: 8.w,
                      right: 13.w,
                    ),
                    child: Text(
                      "join_hands_today".tr,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center,
                      style: CustomTextStyles.bodyLargeSenGray600,
                    ),
                  ),
                  SizedBox(height: 29.h),
                  _buildEnteringUrlForLink(context),
                  SizedBox(height: 5.h),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Section Widget
  Widget _buildEnteringUrlForLink(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(right: 4.w),
      padding: EdgeInsets.all(12.w),
      decoration: AppDecoration.shadow1.copyWith(
        borderRadius: BorderRadiusStyle.roundedBorder6,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "kitty_url_or_id".tr,
                style: CustomTextStyles.titleSmallGray900,
              ),
              SizedBox(height: 2.h),
              CustomTextFormField(
                autofocus: false,
                controller: kittyUrlController,
                hintText: "url_example".tr,
                textInputAction: TextInputAction.done,
              ),
            ],
          ),
          SizedBox(height: 24.h),
          Obx(
            () => CustomKtButton(
              isLoading: contributeController.isgetkittyloading.isTrue,
              onPress: () async {
                final kittId = kittyUrlController.text;
                int?
                    id; // Declare id variable outside if block to make it accessible later

                if (!RegExp(r'^\d+$').hasMatch(kittId)) {
                  // Extract ID from URL if it consists of letters and symbols
                  id = extractUrlId(kittId);
                } else {
                  // If the input is already an ID, use it directly
                  id = int.tryParse(kittId);
                }

                final res = await contributeController.getKitty(id: id);
                if (res && contributeController.kittyType.value == 4) {
                  String route = NavRoutes.chamaContributeRoute;
                  //remove the id from the route
                  route = route.replaceFirst(':id', '');
                  Get.toNamed("$route$id");
                  return;
                } else if (res && contributeController.kittyType.value == 3) {
                  String route = NavRoutes.viewsingleEvent;
                  //remove the id from the route
                  // try to get the username
                  final username =
                      contributeController.kittGoten.value.username;
                  route = route.replaceFirst(':username', '');
                  Get.toNamed("$route$username");
                  return;
                }
                // any other use the contribution page
                if (res) {
                  Snack.showInfo(
                      message1: contributeController.apiMessage.string);
                  String route = NavRoutes.kittycontributionScreen;
                  //remove the id from the route
                  route = route.replaceFirst(':id', '');
                  Get.toNamed(route + id.toString());
                }
              },
              height: 44.h,
              btnText: "submit".tr,
            ),
          ),
        ],
      ),
    );
  }
}

int? extractUrlId(String url) {
  final RegExp regExp = RegExp(r'(?<=\/)\d+(?=\/?$)');
  final Match? match = regExp.firstMatch(url);
  int? id;
  if (match != null) {
    id = int.tryParse(match.group(0) ?? '');
  }
  return id;
}

String getGreeting() {
  var hour = DateTime.now().hour;
  if (hour < 12) {
    return "good_morning".tr;
  } else if (hour < 18) {
    return "good_afternoon".tr;
  } else {
    return "good_evening".tr;
  }
}
