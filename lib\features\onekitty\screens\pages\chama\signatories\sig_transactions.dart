import 'dart:io';
import 'package:animations/animations.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'package:onekitty_admin/features/onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty_admin/helpers/colors.dart';
import 'package:onekitty_admin/helpers/extensions/text_styles.dart';
import 'package:onekitty_admin/helpers/show_snack_bar.dart';
import 'package:onekitty_admin/helpers/show_toast.dart';
import 'package:onekitty_admin/models/chama/signatory_approval.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/views/screens/signatory_transactions.dart';

import 'package:onekitty_admin/features/login/screens/passwd_req_screen.dart';
import 'package:onekitty_admin/features/onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty_admin/utils/cache_keys.dart';
import 'package:onekitty_admin/utils/size_config.dart';
import 'package:onekitty_admin/main.dart' show isLight;
import '../../../../../../../utils/utils_exports.dart';

class SignatoryTransactions extends StatefulWidget {
  static const headerStyle = TextStyle(
      color: Color(0xffffffff), fontSize: 18, fontWeight: FontWeight.bold);
  static const contentStyleHeader = TextStyle(
      color: Color(0xff999999), fontSize: 14, fontWeight: FontWeight.w700);
  static const contentStyle = TextStyle(
      color: Color(0xff999999), fontSize: 14, fontWeight: FontWeight.normal);
  const SignatoryTransactions({super.key});

  @override
  State<SignatoryTransactions> createState() => _SignatoryTransactionsState();
}

class _SignatoryTransactionsState extends State<SignatoryTransactions> {
  final ChamaController chamaController = Get.put(ChamaController());
  final ChamaDataController chamaDataController =
      Get.put(ChamaDataController());

  TextEditingController commentController = TextEditingController();
  final formKey = GlobalKey<FormState>();
  String deviceId = "";
  String deviceModel = "";
  final box = GetStorage();
  @override
  void dispose() {
    super.dispose();
    commentController.dispose();
  }

  @override
  void initState() {
    super.initState();
    commentController.clear();
    getDeviceInfo();
  }

  void getDeviceInfo() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;

      deviceId = androidInfo.id;
      deviceModel = androidInfo.model;

      print('Running on ${androidInfo.id} ${androidInfo.model}');
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      print(
          'Running on ${iosInfo.utsname.machine} ${iosInfo.identifierForVendor} ${iosInfo.model}');

      deviceId = iosInfo.identifierForVendor!;
      deviceModel = iosInfo.model;
    } else {
      WebBrowserInfo webBrowserInfo = await deviceInfo.webBrowserInfo;
      print('Running on ${webBrowserInfo.userAgent}');

      deviceId = webBrowserInfo.userAgent!;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: FittedBox(
          child: Text("signatory_approval_transactions".tr),
        ),
      ),
      body: Column(
        children: [
          Expanded(
            child: GetX<ChamaController>(
              init: ChamaController(),
              initState: (state) {
                Future.delayed(Duration.zero, () async {
                  try {
                    await state.controller?.getSigTransactions(
                      chamaId: chamaDataController.chama.value.chama?.id ?? 0,
                      page: 0,
                    );
                  } catch (e) {
                    rethrow;
                  }
                });
              },
              builder: (ChamaController chamaController) {
                if (chamaController.isGetSigTraLoading.isTrue &&
                    chamaController.sigTransactions.isEmpty) {
                  return SizedBox(
                    height: SizeConfig.screenHeight * .33,
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SpinKitDualRing(
                            color: ColorUtil.blueColor,
                            lineWidth: 4.sp,
                            size: 40.0.sp,
                          ),
                           Text(
                            "loading".tr,
                            style: const TextStyle(color: Colors.white),
                          )
                        ],
                      ),
                    ),
                  );
                } else if (chamaController.sigTransactions.isEmpty) {
                  return Center(
                    child: Column(
                      children: [
                        Image.asset(
                          AssetUrl.notFound,
                          height: 130.h,
                        ),
                        Text("no_transactions_added_to_chama".tr),
                        const SizedBox(height: 10),
                      ],
                    ),
                  );
                } else {
                  return DefaultTabController(
                    length: 2,
                    child: Column(
                      children: [
                        ColoredBox(
                          color: isLight.value
                              ? AppColors.primary
                              : Colors.transparent,
                          child:  Padding(
                            padding: const EdgeInsets.only(bottom: 2.0),
                            child: TabBar(
                              labelColor: Colors.white,
                              unselectedLabelColor: Colors.grey,
                              tabs: [
                                Tab(text: 'pending'.tr),
                                Tab(text: 'processed'.tr),
                              ],
                            ),
                          ),
                        ),
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 12),
                            child: TabBarView(
                              children: [
                                // Pending transactions tab
                                Builder(
                                  builder: (context) {
                                    // Using && so that only items that are neither PROCESSED nor DECLINED appear
                                    final list = chamaController.sigTransactions
                                        .where((element) =>
                                            element.status != "PROCESSED" &&
                                            element.status != "DECLINED")
                                        .toList();
                                    return ListView.separated(
                                      controller: chamaController
                                          .pendingScrollController,
                                      itemCount: list.length,
                                      separatorBuilder: (context, index) =>
                                          const Divider(height: 18),
                                      itemBuilder: (context, index) {
                                        final sigTransaction = list[index];
                                        return OpenContainer(
                                          closedColor: isLight.value
                                              ? Colors.white
                                              : Colors.transparent,
                                          openBuilder: (context, action) =>
                                              SignatoryReadMore(
                                            isChama: true,
                                            index: index,
                                            transaction: sigTransaction,
                                          ),
                                          closedShape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(8),
                                            side: BorderSide(
                                              color: AppColors.primary
                                                  .withOpacity(0.5),
                                              width: 1,
                                            ),
                                          ),
                                          closedBuilder: (context, action) =>
                                              ExpansionTile(
                                            title: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  sigTransaction.reason,
                                                  style: const TextStyle(
                                                    fontSize: 18,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                ),
                                                Text(
                                                  FormattedCurrency
                                                      .getFormattedCurrency(
                                                          sigTransaction
                                                              .amount),
                                                  style: const TextStyle(
                                                      fontSize: 14),
                                                ),
                                                Text(
                                                  sigTransaction.status,
                                                  style: TextStyle(
                                                    fontSize: 14,
                                                    color: getStatusColor(
                                                        sigTransaction.status),
                                                  ),
                                                )
                                              ],
                                            ),
                                            childrenPadding:
                                                const EdgeInsets.all(16),
                                            children: [
                                              Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    sigTransaction.reason,
                                                    style: context.titleText
                                                        ?.copyWith(
                                                            fontSize: 14),
                                                  ),
                                                  _buildRow("ACCOUNT:",
                                                      "${sigTransaction.receiverAccount} ${sigTransaction.receiverAccountRef}"),
                                                  _buildRow(
                                                      "TRANSFER MODE:",
                                                      sigTransaction
                                                          .transferMode),
                                                  _buildRow(
                                                      "DATE:",
                                                      DateFormat(
                                                              "MM/dd/yyyy hh:mm a")
                                                          .format(DateTime.parse(
                                                                  sigTransaction
                                                                      .initiatedAt!
                                                                      .toIso8601String())
                                                              .toLocal())),
                                                  _buildRow(
                                                      "AMOUNT:",
                                                      FormattedCurrency
                                                          .getFormattedCurrency(
                                                              sigTransaction
                                                                  .amount),
                                                      valueColor: Colors.green),
                                                  _buildRow("INITIATED BY:",
                                                      "${sigTransaction.initator?.firstName} ${sigTransaction.initator?.secondName}"),
                                                  if (sigTransaction.response !=
                                                      "")
                                                    _buildRow(
                                                        "RESPONSE: ",
                                                        sigTransaction
                                                            .response),
                                                  _buildRow("STATUS:",
                                                      sigTransaction.status,
                                                      valueColor:
                                                          getStatusColor(
                                                              sigTransaction
                                                                  .status)),
                                                  Align(
                                                    alignment:
                                                        Alignment.centerRight,
                                                    child: TextButton(
                                                      onPressed: () =>
                                                          action.call(),
                                                      child: const Text(
                                                          "Read more"),
                                                    ),
                                                  ),
                                                  Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceBetween,
                                                    children: [
                                                      OutlinedButton(
                                                        style: OutlinedButton
                                                            .styleFrom(
                                                                side: BorderSide(
                                                                    color: Colors
                                                                        .red
                                                                        .shade900)),
                                                        onPressed: () =>
                                                            showApproveDialog(
                                                                index, false),
                                                        child: Text("decline".tr,
                                                            style: TextStyle(
                                                                color: Colors
                                                                    .red
                                                                    .shade900)),
                                                      ),
                                                      ElevatedButton(
                                                        onPressed: () =>
                                                            showApproveDialog(
                                                                index, true),
                                                        child:  Text(
                                                            "approve".tr),
                                                      )
                                                    ],
                                                  )
                                                ],
                                              )
                                            ],
                                          ),
                                        );
                                      },
                                    );
                                  },
                                ),
                                // Processed transactions tab
                                Builder(
                                  builder: (context) {
                                    final list = chamaController.sigTransactions
                                        .where((element) =>
                                            element.status == "PROCESSED" ||
                                            element.status == "DECLINED")
                                        .toList();
                                    if (list.isEmpty) {
                                      return SizedBox(
                                        height: 550.h,
                                        child:  Center(
                                          child: Text(
                                              'approved_transactions_shown_here'.tr),
                                        ),
                                      );
                                    }
                                    return ListView.separated(
                                      controller: chamaController
                                          .processedScrollController,
                                      itemCount: list.length +
                                          (chamaController.isPaginating.value
                                              ? 1
                                              : 0),
                                      separatorBuilder: (context, index) =>
                                          const Divider(height: 18),
                                      itemBuilder: (context, index) {
                                        if (index == list.length) {
                                          return const Center(
                                            child: Padding(
                                              padding: EdgeInsets.all(16.0),
                                              child:
                                                  CircularProgressIndicator(),
                                            ),
                                          );
                                        }
                                        final sigTransaction = list[index];
                                        return OpenContainer(
                                          openBuilder: (context, action) =>
                                              SignatoryReadMore(
                                            isChama: true,
                                            index: index,
                                            isApproved: true,
                                            transaction: sigTransaction,
                                          ),
                                          closedShape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(8),
                                            side: BorderSide(
                                              color: AppColors.primary
                                                  .withOpacity(0.5),
                                              width: 1,
                                            ),
                                          ),
                                          closedBuilder: (context, action) =>
                                              ExpansionTile(
                                            title: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  sigTransaction.reason,
                                                  style: const TextStyle(
                                                    fontSize: 18,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                ),
                                                Text(
                                                  FormattedCurrency
                                                      .getFormattedCurrency(
                                                          sigTransaction
                                                              .amount),
                                                  style: const TextStyle(
                                                      fontSize: 14),
                                                ),
                                                Text(
                                                  sigTransaction.status,
                                                  style: TextStyle(
                                                    fontSize: 14,
                                                    color: getStatusColor(
                                                        sigTransaction.status),
                                                  ),
                                                )
                                              ],
                                            ),
                                            childrenPadding:
                                                const EdgeInsets.all(16),
                                            children: [
                                              Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    sigTransaction.reason,
                                                    style: context.titleText
                                                        ?.copyWith(
                                                            fontSize: 14),
                                                  ),
                                                  _buildRow("ACCOUNT:",
                                                      "${sigTransaction.receiverAccount} ${sigTransaction.receiverAccountRef}"),
                                                  _buildRow(
                                                      "TRANSFER MODE:",
                                                      sigTransaction
                                                          .transferMode),
                                                  _buildRow(
                                                      "DATE:",
                                                      DateFormat(
                                                              "MM/dd/yyyy hh:mm a")
                                                          .format(DateTime.parse(
                                                                  sigTransaction
                                                                      .initiatedAt!
                                                                      .toIso8601String())
                                                              .toLocal())),
                                                  _buildRow(
                                                      "AMOUNT:",
                                                      FormattedCurrency
                                                          .getFormattedCurrency(
                                                              sigTransaction
                                                                  .amount),
                                                      valueColor: Colors.green),
                                                  _buildRow("INITIATED BY:",
                                                      "${sigTransaction.initator?.firstName} ${sigTransaction.initator?.secondName}"),
                                                  if (sigTransaction.response !=
                                                      "")
                                                    _buildRow(
                                                        "RESPONSE: ",
                                                        sigTransaction
                                                            .response),
                                                  _buildRow("STATUS:",
                                                      sigTransaction.status,
                                                      valueColor:
                                                          getStatusColor(
                                                              sigTransaction
                                                                  .status)),
                                                  Align(
                                                    alignment:
                                                        Alignment.centerRight,
                                                    child: TextButton(
                                                      onPressed: () =>
                                                          action.call(),
                                                      child: const Text(
                                                          "Read more"),
                                                    ),
                                                  ),
                                                ],
                                              )
                                            ],
                                          ),
                                        );
                                      },
                                    );
                                  },
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }
              },
            ),
          ),
        ],
      ),
      bottomNavigationBar: Obx(
        () => chamaController.isGetSigTraLoading.value
            ? const SizedBox(
                height: 72,
                width: 72,
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: CupertinoActivityIndicator(),
                ))
            : const SizedBox(),
      ),
    );
  }

  Color getStatusColor(String status) {
    switch (status.toUpperCase()) {
      case 'INITIALIZED':
        return Colors.blue; // Example: Blue for initialized
      case 'PROCESSING':
        return Colors.orange;
      case 'SUCCESS':
        return Colors.green;
      default:
        return Colors.red; // Default color for unknown statuses
    }
  }

  Widget _buildRow(String label, String value, {Color? valueColor}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: context.dividerTextLarge),
          Text(value,
              style: valueColor != null
                  ? context.dividerTextLarge?.copyWith(color: valueColor)
                  : null),
        ],
      ),
    );
  }

  showApproveDialog(index, bool isApprove) {
    showDialog(
        context: context,
        builder: (context) {
          return AlertDialog(
            title: Text(
              isApprove
                  ? "kindly_fill_out_field".tr
                  : "confirm_to_decline_transaction".tr,
              style: context.titleText,
            ),
            content: Visibility(
              visible: isApprove,
              child: Form(
                key: formKey,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CustomTextField(
                      controller: commentController,
                      labelText: "comment".tr,
                      isRequired: true,
                      validator: (p0) {
                        if (p0!.isEmpty) {
                          return "this_field_cannot_be_empty".tr;
                        }
                        return null;
                      },
                    )
                  ],
                ),
              ),
            ),
            actions: [
              isApprove
                  ? const SizedBox()
                  : OutlinedButton(
                      onPressed: () async {
                        await approveTransaction(index, false).whenComplete(() {
                          Get.find<ChamaController>().getSigTransactions(
                              chamaId:
                                  chamaDataController.chama.value.chama?.id ??
                                      0);
                        });
                        Navigator.pop(context);
                      },
                      child: Text("ok".tr)),
              isApprove
                  ? Obx(() => CustomKtButton(
                      width: 120.w,
                      isLoading:
                          chamaController.isSignatoryApproveLoading.isTrue,
                      onPress: () async {
                        await approveTransaction(index, true).whenComplete(() {
                          Get.find<ChamaController>().getSigTransactions(
                              chamaId:
                                  chamaDataController.chama.value.chama?.id ??
                                      0);
                        });
                      },
                      btnText: "approve".tr))
                  : const SizedBox()
            ],
          );
        });
  }

  approveTransaction(index, bool isApprove) async {
    //if (formKey.currentState!.validate()) {
    final tra = chamaController.sigTransactions[index];
    var isAuthenticated =
        await Get.to(() => AuthPasswdScreen(), arguments: [false]);

    SignatoryApprovalModel request = SignatoryApprovalModel(
        chamaId: chamaDataController.chama.value.chama?.id,
        memberId: chamaDataController.chama.value.member?.id,
        isApproved: isApprove ? true : false,
        comment: isApprove ? commentController.text.trim() : "Decline",
        transactionId: tra.id,
        latitude: box.read(CacheKeys.lat),
        longitude: box.read(CacheKeys.long),
        deviceId: deviceId,
        deviceModel: deviceModel);
    if (isAuthenticated != null && isAuthenticated == true) {
      bool res = await chamaController.signatoryApproval(request: request);
      if (res) {
        if (!mounted) return;
        Snack.show(res, chamaController.apiMessage.string);
        setState(() {
          chamaController.sigTransactions.removeAt(index);
          Navigator.pop(context);
        });
      } else {
        if (!mounted) return;
        Snack.show(res, chamaController.apiMessage.string);
      }
    } else {
      ToastUtils.showInfoToast(
          context, "you_need_to_authenticate".tr, "oops".tr);
    }
    //}
  }
}
