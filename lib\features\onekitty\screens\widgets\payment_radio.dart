// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty_admin/configs/payment_channels.dart';
import 'package:onekitty_admin/helpers/colors.dart';
import 'package:onekitty_admin/utils/payment_kensbuns.dart';

class PaymentChannelsBuilder extends StatefulWidget {
  PaymentChannelsBuilder({
    required this.selectedChannel,
    required this.onChange,
    this.itemsPerRow = 4, // Default value for items per row
    super.key,
  });

  String selectedChannel;
  Function(String?)? onChange;
  int itemsPerRow; // Number of items to display per row

  @override
  State<PaymentChannelsBuilder> createState() => _PaymentChannelsBuilderState();
}

class _PaymentChannelsBuilderState extends State<PaymentChannelsBuilder> {
  final paymentControllers = Get.isRegistered<PaymentChannel>()
      ? Get.find<PaymentChannel>()
      : Get.put(PaymentChannel());
  @override
  Widget build(BuildContext context) {
    final channels = paymentControllers.paymentChannels;
    return Wrap(
      alignment: WrapAlignment.spaceEvenly,
      children: List.generate(
        channels.length,
        (index) {
          if (index % widget.itemsPerRow == 0) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: List.generate(
                widget.itemsPerRow,
                (innerIndex) {
                  final channelIndex = index + innerIndex;
                  if (channelIndex >= channels.length) {
                    return const SizedBox.shrink();
                  }
                  return _buildChannelItem(channelIndex);
                },
              ),
            );
          }
          return const SizedBox.shrink(); // Empty placeholder for padding
        },
      ),
    );
  }

  Widget _buildChannelItem(int index) {
    final channels = paymentControllers.paymentChannels;
    return Tooltip(
      message: channels[index].toString(),
      child: Column(
        children: [
          Radio<String>(
            value: channels[index].name,
            groupValue: widget.selectedChannel,
            onChanged: (value) {
              setState(() {
                widget.selectedChannel = value ?? "none";
              });
              widget.onChange!(value);
            },
          ),
          Image.asset(
            channels[index].imageUrl,
            height: 45.spMin,
            width: 60.spMin,
          ),
        ],
      ),
    );
  }
}

class PaymentChannelsBuilder2 extends StatefulWidget {
  PaymentChannelsBuilder2({
    required this.selectedChannel,
    required this.onChange,
    this.itemsPerRow = 4, // Default value for items per row
    super.key,
  });

  String selectedChannel;
  Function(String?)? onChange;
  int itemsPerRow; // Number of items to display per row

  @override
  State<PaymentChannelsBuilder2> createState() =>
      _PaymentChannelsBuilder2State();
}

class _PaymentChannelsBuilder2State extends State<PaymentChannelsBuilder2> {

  final paymentControllers = Get.isRegistered<PaymentChannel>()
      ? Get.find<PaymentChannel>()
      : Get.put(PaymentChannel());
  @override
  Widget build(BuildContext context) {
    final channels = paymentControllers.paymentChannels;
    return Wrap(
      alignment: WrapAlignment.spaceEvenly,
      children: List.generate(
        channels.length,
        (index) {
          if (index % widget.itemsPerRow == 0) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: List.generate(
                widget.itemsPerRow,
                (innerIndex) {
                  final channelIndex = index + innerIndex;
                  if (channelIndex >= channels.length) {
                    return const SizedBox.shrink();
                  }
                  return _buildChannelItem(channelIndex);
                },
              ),
            );
          }
          return const SizedBox.shrink(); // Empty placeholder for padding
        },
      ),
    );
  }

  Widget _buildChannelItem(int index) {
    final channels = paymentControllers.paymentChannels;
    return Tooltip(
      message: channels[index].name.toString(),
      child: Column(
        children: [
          Radio<String>(
            value: channels[index].name,
            groupValue: widget.selectedChannel,
            onChanged: (value) {
              setState(() {
                widget.selectedChannel = value ?? "none";
              });
              widget.onChange!(value);
            },
          ),
          Image.asset(
            channels[index].imageUrl,
            height: 45.spMin,
            width: 60.spMin,
          ),
        ],
      ),
    );
  }
}

class ContributeChannelsBuilder extends StatefulWidget {
  ContributeChannelsBuilder({
    required this.selectedChannel,
    required this.onChange,
    this.itemsPerRow = 4, // Default value for items per row
    super.key,
  });

  int selectedChannel;
  Function(int?)? onChange;
  int itemsPerRow; // Number of items to display per row

  @override
  State<ContributeChannelsBuilder> createState() =>
      _ContributeChannelsBuilderState();
}

class _ContributeChannelsBuilderState extends State<ContributeChannelsBuilder> {
  final paymentControllers = Get.isRegistered<PaymentChannel>()
      ? Get.find<PaymentChannel>()
      : Get.put(PaymentChannel());

  @override
  Widget build(BuildContext context) {
    final channels = paymentControllers.paymentChannels;
    return SingleChildScrollView(
      scrollDirection: Axis.vertical,
      hitTestBehavior: HitTestBehavior.translucent,
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: MediaQuery.of(context).size.width > 600 ? 4 : 3,
          childAspectRatio: 0.8,
          crossAxisSpacing: 8,
          mainAxisSpacing: 8,
        ),
        itemCount: channels.length,
        itemBuilder: (context, index) => _buildChannelItem(index),
      ),
    );
  }

  Widget _buildChannelItem(int index) {
    final channels = paymentControllers.paymentChannels;
    if (index >= channels.length) {
      return const SizedBox.shrink();
    }
    return Container(
      height: 117.h,
      width: 100.w,
      margin: const EdgeInsets.all(8.0),
      decoration: BoxDecoration(
        border: Border.all(
          color: Colors.grey.shade300,
          width: 1.0,
        ),
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Tooltip(
        message: channels[index].name.toString(),
        child: Column(
          children: [
            Radio<int>(
              value: channels[index].channelCode,
              groupValue: widget.selectedChannel,
              onChanged: (value) {
                setState(() {
                  widget.selectedChannel = value ?? 63902;
                });
                widget.onChange!(value);
              },
            ),
            Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: channels[index].imageUrl == "assets/images/credit_card.png"
                  ? const PaymentKensbuns()
                  : Image.asset(
                      channels[index].imageUrl,
                      color: channels[index].imageUrl ==
                              "assets/images/credit_card.png"
                          ? AppColors.primary
                          : null,
                      height: 45.spMin,
                      width: 60.spMin,
                    ),
            ),
          ],
        ),
      ),
    );
  }
}
