// Receipt content - Enhanced to match PDF design
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/controllers/user_ktty_controller.dart';
import 'package:onekitty_admin/utils/formatted_currency.dart';
import 'package:qr_flutter/qr_flutter.dart';
// Helper method to build receipt header with logo
Widget buildReceiptHeader() {
  return Column(
    children: [
      // Logo Container
      Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Image.asset(
          'assets/images/launcher.png',
          height: 60.h,
          width: 120.w,
          fit: BoxFit.contain,
        ),
      ),
      
      SizedBox(height: 8.h),

      // Company Name
      Text(
        'OneKitty',
        style: TextStyle(
          fontSize: 28.sp,
          fontWeight: FontWeight.bold,
          color: Colors.grey[800],
        ),
      ),
      
      SizedBox(height: 4.h),

      // Receipt Badge
      Container(
        padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 8.h),
        decoration: BoxDecoration(
          color: Colors.green[50],
          borderRadius: BorderRadius.circular(20.r),
          border: Border.all(color: Colors.green[200]!),
        ),
        child: Text(
          'TRANSACTION RECEIPT',
          style: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeight.bold,
            color: Colors.green[800],
            letterSpacing: 1.2,
          ),
        ),
      ),
    ],
  );
}

// Build Receipt Header Section (inside main card)
Widget buildReceiptHeaderSection(dynamic transaction) {
  final DateFormat format = DateFormat('dd MMM yyyy, hh:mm a');
  final DateTime createdAt = transaction.createdAt ?? DateTime.now();
  
  return Container(
    width: double.infinity,
    padding: EdgeInsets.all(24.w),
    decoration: BoxDecoration(
      color: Colors.green[50],
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(12.r),
        topRight: Radius.circular(12.r),
      ),
    ),
    child: Column(
      children: [
        Text(
          'receipt_details'.tr,
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
            color: Colors.green[800],
          ),
        ),
        
        SizedBox(height: 12.h),

        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            buildInfoItem(
              'receipt_no'.tr,
              transaction.transactionCode ?? 'N/A',
            ),
            buildInfoItem(
              'date_time'.tr,
              format.format(createdAt),
            ),
          ],
        ),
        
        SizedBox(height: 12.h),

        buildStatusBadge(transaction.status ?? 'COMPLETED'),
      ],
    ),
  );
}

// Build Customer Information Section
Widget buildCustomerSection(dynamic transaction) {
  return Container(
    width: double.infinity,
    padding: EdgeInsets.all(24.w),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: EdgeInsets.all(8.w),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(6.r),
              ),
              child: Icon(
                Icons.person_rounded,
                color: Colors.blue[700],
                size: 16.sp,
              ),
            ),
            SizedBox(width: 12.w),
            Text(
              'customer_information'.tr,
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
                color: Colors.grey[800],
              ),
            ),
          ],
        ),
        
        SizedBox(height: 12.h),

        if (transaction.firstName != null || transaction.secondName != null)
          buildDetailRow(
            'customer_name'.tr,
            '${transaction.firstName ?? ''} ${transaction.secondName ?? ''}'.trim(),
          ),
        
        if (transaction.phoneNumber != null && transaction.phoneNumber!.isNotEmpty)
          buildDetailRow('phone_number'.tr, transaction.phoneNumber!),
        
        if (transaction.email != null && transaction.email!.isNotEmpty)
          buildDetailRow('email_address'.tr, transaction.email!),
        
        Container(
          margin: EdgeInsets.only(top: 12.h),
          height: 1.h,
          color: Colors.grey[200],
        ),
      ],
    ),
  );
}

// Build Transaction Details Section
Widget buildTransactionSection(dynamic transaction) {
  final DataController dataController = Get.find<DataController>();
  
  return Container(
    width: double.infinity,
    padding: EdgeInsets.all(24.w),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: EdgeInsets.all(8.w),
              decoration: BoxDecoration(
                color: Colors.orange[50],
                borderRadius: BorderRadius.circular(6.r),
              ),
              child: Icon(
                Icons.receipt_rounded,
                color: Colors.orange[700],
                size: 16.sp,
              ),
            ),
            SizedBox(width: 12.w),
            Text(
              'transaction_details'.tr,
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
                color: Colors.grey[800],
              ),
            ),
          ],
        ),
        
        SizedBox(height: 12.h),

        if (dataController.kitty.value.kitty?.title != null)
          buildDetailRow('kitty_group'.tr, dataController.kitty.value.kitty!.title!),
        
        if (transaction.product != null && transaction.product!.isNotEmpty)
          buildDetailRow('transaction_type'.tr, transaction.product!.toUpperCase()),
        
        if (transaction.transactionRef != null && transaction.transactionRef!.isNotEmpty)
          buildDetailRow('reference'.tr, transaction.transactionRef!),
        
        buildDetailRow('payment_method'.tr, 'mobile_money'.tr),
        
        Container(
          margin: EdgeInsets.only(top: 12.h),
          height: 1.h,
          color: Colors.grey[200],
        ),
      ],
    ),
  );
}

// Build Amount Section (Highlighted)
Widget buildAmountSection(dynamic transaction) {
  return Container(
    width: double.infinity,
    margin: EdgeInsets.all(24.w),
    padding: EdgeInsets.all(24.w),
    decoration: BoxDecoration(
      color: Colors.green[50],
      borderRadius: BorderRadius.circular(12.r),
      border: Border.all(color: Colors.green[200]!, width: 2),
    ),
    child: Column(
      children: [
        Text(
          'total_amount'.tr.toUpperCase(),
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.bold,
            color: Colors.green[700],
            letterSpacing: 1.1,
          ),
        ),
        
        SizedBox(height: 6.h),

        Text(
          FormattedCurrency.getFormattedCurrency(transaction.amount),
          style: TextStyle(
            fontSize: 28.sp,
            fontWeight: FontWeight.bold,
            color: Colors.green[800],
          ),
        ),
      ],
    ),
  );
}

// Build Receipt Footer Section
Widget buildReceiptFooterSection(dynamic transaction) {
  return Container(
    width: double.infinity,
    padding: EdgeInsets.all(24.w),
    decoration: BoxDecoration(
      color: Colors.grey[50],
      borderRadius: BorderRadius.only(
        bottomLeft: Radius.circular(12.r),
        bottomRight: Radius.circular(12.r),
      ),
    ),
    child: Column(
      children: [
        // Real Barcode
        Container(
          height: 250.h,
          width: 250.w,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(4.r),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Padding(
            padding: EdgeInsets.all(8.w),
            child: QrImageView(
              data: transaction.transactionCode ?? 'ONEKITTY${DateTime.now().millisecondsSinceEpoch}',
              version: QrVersions.auto,
              size: 200.w,
              backgroundColor: Colors.white,
              foregroundColor: Colors.black,
            ),
          ),
        ),
        
        SizedBox(height: 12.h),

        Text(
          'scan_barcode_verification'.tr,
          style: TextStyle(
            fontSize: 10.sp,
            color: Colors.grey[600],
            fontStyle: FontStyle.italic,
          ),
        ),
      ],
    ),
  );
}

// Build Company Footer
Widget buildCompanyFooter() {
  return Column(
    children: [
      Text(
        'thank_you_for_choosing_onekitty'.tr,
        style: TextStyle(
          fontSize: 16.sp,
          fontWeight: FontWeight.w600,
          color: Colors.grey[700],
        ),
      ),
      
      SizedBox(height: 6.h),

      Text(
        'trusted_financial_companion'.tr,
        style: TextStyle(
          fontSize: 12.sp,
          color: Colors.grey[500],
          fontStyle: FontStyle.italic,
        ),
      ),
      
      SizedBox(height: 8.h),

      Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(20.r),
        ),
        child: Text(
          'www.onekitty.co.ke',
          style: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeight.w500,
            color: Colors.grey[700],
          ),
        ),
      ),
    ],
  );
}

// Helper method to build info items for header
Widget buildInfoItem(String label, String value) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.center,
    children: [
      Text(
        label,
        style: TextStyle(
          fontSize: 10.sp,
          color: Colors.green[700],
          fontWeight: FontWeight.w600,
        ),
      ),
      SizedBox(height: 4.h),
      Text(
        value,
        style: TextStyle(
          fontSize: 11.sp,
          fontWeight: FontWeight.bold,
          color: Colors.grey[800],
        ),
        textAlign: TextAlign.center,
      ),
    ],
  );
}

// Helper method to build status badge
Widget buildStatusBadge(String status) {
  final isSuccess = status.toUpperCase() == 'COMPLETED' || 
                   status.toUpperCase() == 'SUCCESS' ||
                   status.toUpperCase() == 'PAID';
  
  return Container(
    padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 6.h),
    decoration: BoxDecoration(
      color: isSuccess ? Colors.green[100] : Colors.orange[100],
      borderRadius: BorderRadius.circular(20.r),
    ),
    child: Text(
      status.toUpperCase(),
      style: TextStyle(
        fontSize: 10.sp,
        fontWeight: FontWeight.bold,
        color: isSuccess ? Colors.green[800] : Colors.orange[800],
        letterSpacing: 0.5,
      ),
    ),
  );
}

// Helper method to build detail rows
Widget buildDetailRow(String label, String value) {
  return Padding(
    padding: EdgeInsets.symmetric(vertical: 6.h),
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 120.w,
          child: Text(
            '$label:',
            style: TextStyle(
              fontSize: 12.sp,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.w600,
              color: Colors.grey[800],
            ),
          ),
        ),
      ],
    ),
  );
}