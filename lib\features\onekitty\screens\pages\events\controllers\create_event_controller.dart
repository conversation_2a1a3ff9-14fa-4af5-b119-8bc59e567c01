import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onekitty_admin/models/events/categories_model.dart';
import 'package:onekitty_admin/models/events/events_model.dart';
import 'package:onekitty_admin/models/events/media_models.dart';
import 'package:onekitty_admin/models/events/tickets_model.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/create_event/time_and_location.dart'; 
import 'package:onekitty_admin/services/api_urls.dart';
import 'package:onekitty_admin/services/http_service.dart';
import 'package:onekitty_admin/utils/crud_navigation_helper.dart';
import 'package:dio/dio.dart' as dios;

class CreateEventController extends GetxController implements GetxService {
  final HttpService apiProvider = Get.find();
  final logger = Get.find<Logger>();
  RxString apiMessage = ''.obs;
  RxString checkoutId = ''.obs;
  RxBool isloading = false.obs;
  RxList<CategoriesModel> categories = <CategoriesModel>[].obs;
  RxBool isLoadingCategories = false.obs;
  RxBool isUploading = false.obs;
  RxInt uploadingCount = 0.obs;
  final eventMedia = <Map<String, dynamic>>[].obs;

  // Media limit constants and reactive state
  static const int maxMediaLimit = 3;

  /// Check if media limit is reached
  bool get isMediaLimitReached => eventMedia.length >= maxMediaLimit;

  /// Get remaining media slots
  int get remainingMediaSlots => maxMediaLimit - eventMedia.length;

  /// Check if batch upload would exceed limit
  bool canUploadBatch(int fileCount) => eventMedia.length + fileCount <= maxMediaLimit;

  /// Get media limit message for UI display
  String getMediaLimitMessage() {
    if (isMediaLimitReached) {
      return 'Maximum $maxMediaLimit images allowed';
    }
    return '${eventMedia.length}/$maxMediaLimit images added ($remainingMediaSlots remaining)';
  }

  RxInt eventId = 0.obs;
  final eventName = "".obs;
  final eventUsername = "".obs;
  final kittyId = 0.obs;
  /// Main method that creates event and tickets (optimized with fallback)
   Future<int> createEventWithTickets({
    required List eventMedia,
    required String title,
    required String username,
    required String description,
    required String phoneNumber,
    required String email,
    required String locationTip,
    required String venue,
    required int? referralCode,
    required double lat,
    required double long,
    required int catId,
    required DateTime? startDate,
    required DateTime? endDate,
    required List<Ticket> tickets,
  }) async {
    try {
      isloading(true);

      // Create Event model and use its toJson method
      final event = Event(
        title: title,
        username: username,
        description: description,
        phoneNumber: phoneNumber,
        email: email,
        locationTip: locationTip,
        venue: venue,
        latitude: lat,
        longitude: long,
        categoryId: catId,
        startDate: startDate,
        endDate: endDate,
        eventMedia: eventMedia.map((e) => EventMedia.fromJson(e)).toList(),
        tickets:  tickets,
        referralCode : referralCode,
      );

      // Single API call to create event with tickets
      var response = await apiProvider.request(
        url: ApiUrls.CREATEEVENT,
        method: Method.POST,
        params: event.toJson(),
      );

      if(response.data['status'] ?? false){
        // Clean up all controller variables on successful event creation
        cleanupController();
      }

      if (!(response.data["status"] ?? false)) {
        Get.snackbar(
          'Error',
          response.data['message'] ?? 'Could not create event with tickets',
          backgroundColor: Colors.red
        );
        throw Exception(response.data['message'] ?? 'Could not create event with tickets');
      }

      // Parse event data from optimized response
      int createdEventId = response.data["data"]["event"]["ID"] ?? 0;
      eventId.value = createdEventId;
      kittyId.value = response.data["data"]["event"]["kitty_id"] ?? 0;
      eventName.value = response.data["data"]["event"]["title"] ?? "";
      eventUsername.value = response.data["data"]["event"]["username"] ?? "";

      // Use CRUD Navigation Helper for consistent success handling
      if (Get.context != null) {
        CrudNavigationHelper.handleEventCrudSuccess(
          context: Get.context!,
          operation: 'create',
        );
      }

      return createdEventId;
    } catch (e) {logger.e("Create event with tickets error: $e");      
      return 0;
      
    } finally {
      isloading(false);
    }
  }
  Future<bool> addSocialMedia({
    required int eventId,
    required List media,
    required String? facebook,
    required String? tiktok,
    required String? instagram,
    required String? youtube,
    required String? twitter,
    required String? hearthis,
    required String? website,
  }) async {
    if (isloading.value) {
      logger.w('Social media operation already in progress');
      return false;
    }
    
    isloading(true);
    final List<String> failedUrls = [];
    
    try {
      // Validate event ID
      if (eventId <= 0) {
        throw Exception('Invalid event ID');
      }

      // Validate and clean URLs if provided
      final Map<String, String?> socialUrls = {
        'facebook': facebook?.trim(),
        'tiktok': tiktok?.trim(),
        'Instagram': instagram?.trim(),
        'youtube': youtube?.trim(),
        'twitter': twitter?.trim(),
        'hearthis': hearthis?.trim(),
        'website': website?.trim(),
      };

      // Remove empty URLs
      socialUrls.removeWhere((key, value) => value == null || value.isEmpty);

      // Validate remaining URLs and track failures
      for (final entry in socialUrls.entries) {
        final url = entry.value!;
        try {
          final uri = Uri.parse(url);
          if (!uri.hasScheme || (!uri.scheme.startsWith('http'))) {
            failedUrls.add('${entry.key}: Invalid URL format');
          }
        } catch (e) {
          failedUrls.add('${entry.key}: Invalid URL');
        }
      }
      
      if (failedUrls.isNotEmpty) {
        logger.w('Invalid URLs detected: ${failedUrls.join(', ')}');
      }

      var res = await apiProvider
          .request(url: ApiUrls.ADDSOCIALMEDIA, method: Method.POST, params: {
        "event_id": eventId,
        "media": media,
        "SocialAccounts": socialUrls,
      });

      if (res.data["status"] == true) {
        logger.i("Social media added successfully for event $eventId");
        return true;
      }
      
      final errorMessage = res.data["message"] ?? "Failed to add social media";
      logger.e("Social media API error: $errorMessage");
      throw Exception(errorMessage);
    } catch (e) {
      logger.e("Social media error: $e");
      return false;
    } finally {
      isloading(false);
    }
  }

  final  RxList<Map<String, dynamic>> bannerList = <Map<String, dynamic>>[].obs;
  final RxInt category = 0.obs;
  final  Rx<CategoriesModel?>? selCategory = Rx<CategoriesModel?>(null);

  Future<String?> uploadFile(
      {required String path, required String fileName}) async {
    try {
      // Check file size (limit to 10MB)
      final file = File(path);
      final fileSize = await file.length();
      if (fileSize > 10 * 1024 * 1024) {
        throw Exception('File too large. Maximum size is 10MB');
      }
      
      var data = dios.FormData.fromMap({
        'file': await dios.MultipartFile.fromFile(path, filename: fileName),
        'bucket': 'onekitty'
      });
      
      final response = await apiProvider.request(
        url: "${HttpService.baseUrl!}${ApiUrls.UPLOADFILE}",
        method: Method.POST,
        formdata: data,
      );
      
      if (response.data['status'] ?? false) {
        return response.data['data']['file_path'];
      } else {
        final errorMsg = response.data['message'] ?? 'Upload failed';
        throw Exception(errorMsg);
      }
    } catch (e) {
      logger.e('Error uploading file: $e');
      throw Exception('Upload failed: ${e.toString()}');
    }
  }

  Future<void> getCategories() async {
    try {
      categories.clear();
      categories([]);
      isLoadingCategories(true);
      final httpService = HttpService();
      final dio = httpService.initializeDio();
      final response = await dio.get(ApiUrls.GETCATEGORIES);
      if (response.data != null) {
        final _returneddata = response.data['data']['categories'] as List;
        categories = _returneddata
            .map((item) {
              return CategoriesModel.fromJson(item);
            })
            .toList()
            .obs;
      } else {
        logger.v('No data or unexpected response structure');
      }
      isLoadingCategories(false);
    } catch (e) {
      logger.e('Error fetching events: $e');
      isLoadingCategories(false);
    }
  }

  /// Upload event image and update state
  Future<void> uploadEventImage(File imageFile) async {
    if (isMediaLimitReached) {
      throw Exception('Maximum $maxMediaLimit images allowed');
    }

    isUploading(true);
    try {
      final fileName = 'event_${DateTime.now().millisecondsSinceEpoch}_${imageFile.path.split('/').last}';
      final uploadedPath = await uploadFile(path: imageFile.path, fileName: fileName);
      
      if (uploadedPath != null) {
        eventMedia.add({
          'file_path': uploadedPath,
          'media_type': 'image',
          'original_name': imageFile.path.split('/').last,
        });
        logger.i('Image uploaded successfully: $uploadedPath');
      }
    } catch (e) {
      logger.e('Failed to upload image: $e');
      rethrow;
    } finally {
      isUploading(false);
    }
  }

  /// Remove event media by index
  void removeEventMedia(int index) {
    if (index >= 0 && index < eventMedia.length) {
      eventMedia.removeAt(index);
    }
  }

  //tickets controller
  RxList<Ticket> tickets = <Ticket>[].obs;
  var slotType = 1.obs;

  /// Validate and clean media data before sending to API
  List<Map<String, dynamic>> getValidatedEventMedia() {
    // Remove any media without valid URLs
    final validMedia = eventMedia.where((media) {
      final url = media['url']?.toString();
      return url != null && url.isNotEmpty && url != 'null';
    }).toList();

    // Remove duplicates based on URL
    final Map<String, Map<String, dynamic>> uniqueMedia = {};
    for (final media in validMedia) {
      final url = media['url'].toString();
      if (!uniqueMedia.containsKey(url)) {
        uniqueMedia[url] = media;
      }
    }

    // Convert to proper format for API
    return uniqueMedia.values.map((media) => {
      'url': media['url'],
      'type': media['type'] ?? 'image',
    }).toList();
  }

  /// Sync bannerList and eventMedia to ensure consistency
  void syncMediaLists() {
    // Remove any bannerList items that don't have corresponding eventMedia
    bannerList.removeWhere((banner) {
      final bannerId = banner['id'];
      return !eventMedia.any((media) => media['id'] == bannerId);
    });

    // Remove any eventMedia items that don't have valid URLs
    eventMedia.removeWhere((media) {
      final url = media['url']?.toString();
      return url == null || url.isEmpty || url == 'null';
    });
  }

  /// Clean up all controller variables when user exits or successfully creates an event
  void cleanupController() {
    // Reset loading states
    isloading.value = false;
    isLoadingCategories.value = false;
    isUploading.value = false;
    uploadingCount.value = 0;
    
    // Clear API messages and IDs
    apiMessage.value = '';
    checkoutId.value = '';
    eventId.value = 0;
    kittyId.value = 0;
    eventName.value = '';
    eventUsername.value = '';
    
    // Clear media and banners
    eventMedia.clear();
    bannerList.clear();
    
    // Reset category selection
    category.value = 0;
    selCategory?.value = null;
    
    // Clear tickets
    tickets.clear();
    slotType.value = 1;
    
    // Clean up TimeAndLocationController if it exists
    try {
      final timeLocationController = Get.find<TimeAndLocationController>();
      timeLocationController.cleanupController();
    } catch (e) {
      // TimeAndLocationController might not be initialized, which is fine
      logger.d('TimeAndLocationController not found during cleanup: $e');
    }
    
    // Note: categories list is kept as it's reusable data
    // categories.clear(); // Uncomment if you want to clear categories too
    
    logger.i('CreateEventController cleaned up successfully');
  }

  @override
  void onClose() {
    cleanupController();
    super.onClose();
  }
}
