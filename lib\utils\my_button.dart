import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:onekitty_admin/helpers/colors.dart';
class MyButton extends StatelessWidget {
  final String label;
  final Function()? onClick;
  final IconData? icon;
  final bool? showLoading;
  final bool? isExpanded;
  final bool? isGreyedOut;
  final double? width;
  final Color? color;
  final bool? outlined;
  final double fontSize ,padding;
  
  const MyButton(
      {super.key,
      required this.label,
      this.onClick,
      this.showLoading = false,
      this.icon,
      this.color = AppColors.primary,
      this.isExpanded = false,
      
      this.isGreyedOut,
      this.outlined = false,
      this.width, this.fontSize = 18, this.padding = 8});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      child: Material(
        borderRadius: BorderRadius.circular(25.r),
        color: outlined ?? false
            ? Colors.transparent
            : isGreyedOut ?? false
                ? Colors.grey.shade400
                : color,
        child: isExpanded ?? false
            ? Row(
                children: [
                  outlined ?? false
                      ? Expanded(
                          child: OutlinedButton(
                            style: OutlinedButton.styleFrom(
                              padding:  EdgeInsets.all(padding),
                              minimumSize: Size(double.infinity, 45.h),
                              backgroundColor: Colors.transparent,
                              side: const BorderSide(color: AppColors.primary),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(25.r),
                              ),
                            ),
                            onPressed: onClick,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                if (showLoading ?? false)
                                  const SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                          Colors.white),
                                    ),
                                  ),
                                if (icon != null)
                                  Icon(
                                    icon,
                                    color: AppColors.primary,
                                  ),
                                FittedBox(
                                  child: Text(label,
                                    
                                      style: TextStyle(
                                          color: AppColors.primary,
                                          fontWeight: FontWeight.w400,
                                          fontSize: fontSize)),
                                ),
                              ],
                            ),
                          ),
                        )
                      : Expanded(
                          child: MaterialButton(
                          height: 45.h,
                          onPressed: onClick,
                          child: Padding(
                              padding:  EdgeInsets.all(padding),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  if (showLoading ?? false)
                                    const SizedBox(
                                      width: 20,
                                      height: 20,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor:
                                            AlwaysStoppedAnimation<Color>(
                                                Colors.white),
                                      ),
                                    ),
                                  if (icon != null)
                                    Icon(
                                      icon,
                                      color: Colors.white,
                                    ),
                                  FittedBox(
                                    child: Text(label,
                                    
                                   
                                        style: TextStyle(
                                            color: Colors.white,
                                            fontWeight: FontWeight.w400,
                                            fontSize: fontSize)),
                                  ),
                                ],
                              )),
                        ))
                ],
              )
            : outlined ?? false
                ? OutlinedButton(
                    style: OutlinedButton.styleFrom(
                      padding:  EdgeInsets.all(padding),
                      minimumSize: Size(double.infinity, 45.h),
                      side: const BorderSide(color: AppColors.primary),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(25.r),
                      ),
                    ),
                    onPressed: onClick,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        if (showLoading ?? false)
                          const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          ),
                        if (icon != null)
                          Icon(
                            icon,
                            color: AppColors.primary,
                          ),
                        FittedBox(
                          child: Text(label,
                                    
                              style: TextStyle(
                                  color: AppColors.primary,
                                  fontWeight: FontWeight.w400,
                                  fontSize: fontSize)),
                        ),
                      ],
                    ),
                  )
                : MaterialButton(
                    height: 45.h,
                    onPressed: onClick,
                    child: Padding(
                        padding:  EdgeInsets.all(padding),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            if (icon != null)
                              Icon(
                                icon,
                                color:  Colors.white ,
                              ),
                            SizedBox(width: 8.w),
                            FittedBox(
                              child: Text(label,
                                    
                                  style: TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.w400,
                                      fontSize: fontSize)),
                            ),
                            SizedBox(width: 8.w),
                            if (showLoading ?? false)
                              const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white),
                                ),
                              ),
                          ],
                        )),
                  ),
      ),
    );
  }
}
