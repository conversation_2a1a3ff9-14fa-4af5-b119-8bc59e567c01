// ==================== action_button.dart ====================
import 'dart:ui' show Color;

import 'package:flutter/widgets.dart' show IconData, VoidCallback;

class ActionButton {
  final IconData icon;
  final String label;
  final VoidCallback onTap;
  final String? showMessage;
  final Color? color;

  ActionButton({
    required this.icon,
    required this.label,
    required this.onTap,
    this.showMessage,
    this.color,
  });
}