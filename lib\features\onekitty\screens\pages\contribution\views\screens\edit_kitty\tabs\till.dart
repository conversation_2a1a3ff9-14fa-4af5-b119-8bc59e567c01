import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onekitty_admin/helpers/extensions/text_styles.dart';
import 'package:onekitty_admin/features/onekitty/screens/widgets/text_form_field.dart';

class TillPage2 extends StatelessWidget {
  final TextEditingController tillController;
  const TillPage2(
      {super.key,
      required this.tillController,});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 5.0),
            child: Text(
              "mpesa_till_number_label".tr,
              style: context.titleText,
            ),
          ),
          CustomTextField(
            labelText: "till_number_label".tr,
            controller: tillController,
            showNoKeyboard: true,
            isRequired: true,
            hintText: "till_number_hint".tr,
            validator: (value) {
              RegExp regex = RegExp(r'[a-zA-Z]');
              if (value!.isEmpty) {
                return "enter_till_number_validation".tr;
              } else if (regex.hasMatch(value)) {
                return "till_no_alphabets".tr;
              } else {
                return null;
              }
            },
          ),
        ],
      ),
    );
  }
}
