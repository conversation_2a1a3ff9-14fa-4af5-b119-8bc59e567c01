# Transfer System Migration Guide

## 🚀 Migration Steps

### 1. Update Imports

#### Before (Event Transfers)
```dart
import 'package:onekitty_admin/features/onekitty/controllers/events/transfer_page_controller.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/transfers_page.dart';
```

#### After (Unified Transfers)
```dart
import 'package:onekitty_admin/features/onekitty/screens/pages/transfers/transfers_exports.dart';
```

### 2. Update Navigation Calls

#### Event Transfers
```dart
// Before
Get.to(() => TransferScreen(kittyId: eventId));

// After
Get.toEventTransfer(eventId: eventId);
```

#### Chama Transfers
```dart
// Before
// Complex chama transfer logic scattered across files

// After
Get.toChamaTransfer(chamaId: chamaId);
Get.toPenaltyTransfer(chamaId: chamaId); // For penalty transfers
```

### 3. Update Controller Dependencies

#### Before
```dart
final controller = Get.put(TransferPageController());
```

#### After
```dart
final controller = Get.put(TransferController());
controller.initialize(TransferPageConfig(
  transferType: TransferType.event,
  entityId: eventId,
));
```

## 📝 File-by-File Migration

### Event Transfer Files
- ❌ `lib/controllers/events/transfer_page_controller.dart` → **DEPRECATED**
- ❌ `lib/screens/dashboard/pages/events/transfers_page.dart` → **DEPRECATED**
- ✅ Use unified system instead

### Chama Transfer Files
- ✅ `lib/controllers/chama/chama_controller.dart` → **KEEP** (transferReq method)
- ✅ `lib/screens/dashboard/pages/chama/signatories/sig_transactions.dart` → **KEEP**
- ✅ Use unified system for new transfers

## 🔄 Backward Compatibility

The migration maintains backward compatibility:
- Existing API endpoints unchanged
- Original controllers still work
- Gradual migration possible

## ⚡ Quick Migration Script

Run this to update your codebase:

```bash
# Replace event transfer imports
find . -name "*.dart" -exec sed -i 's/import.*transfer_page_controller.dart.*/import "package:onekitty\/screens\/dashboard\/pages\/transfers\/transfers_exports.dart";/g' {} \;

# Replace navigation calls
find . -name "*.dart" -exec sed -i 's/Get\.to(() => TransferScreen(kittyId: \([^)]*\)))/Get.toEventTransfer(eventId: \1)/g' {} \;
```