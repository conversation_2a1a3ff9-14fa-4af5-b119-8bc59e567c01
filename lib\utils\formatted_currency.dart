import 'dart:convert';

import 'package:currency_formatter/currency_formatter.dart';
import 'package:onekitty_admin/configs/country_specifics.dart';


class FormattedCurrency {
  
  static String getFormattedCurrency(dynamic balance) {
    
    const CurrencyFormat kenyanSettings = CurrencyFormat(
      symbol: CountryConfig.getCurrencyCode,
      symbolSide: SymbolSide.left,
      thousandSeparator: ',',
      decimalSeparator: '.',
    );
    String formatted = CurrencyFormatter.format(balance ?? 0, kenyanSettings);
    return formatted;
  }
}

// class KittyDescripion {
//   getKittyDescription(String description, ) {
//     q.QuillController descrpcontroller = q.QuillController.basic();
//     final DataController dataController = Get.put(DataController());
//       var myJSON =
//           jsonDecode(description ?? "");
//       descrpcontroller = q.QuillController(
//         document: q.Document.fromJson(myJSON),
//         selection: const TextSelection.collapsed(offset: 0),
//       );
//     return myJSON;
//   }
// }

String extractText(String jsonString) {
  try {
    // First try to parse as JSON
    Map<String, dynamic> jsonMap = json.decode(jsonString);
    if (jsonMap.containsKey('ops')) {
      dynamic ops = jsonMap['ops'];
      if (ops is List && ops.isNotEmpty) {
        dynamic insert = ops[0]['insert'];
        if (insert is String) {
          return insert;
        }
      }
    }
  } catch (e) {
    // If JSON parsing fails, return the original string
    // This handles both plain text and HTML content
    return jsonString;
  }
  return '';
}

class CleanPhoneNumberInput{
 static String cleanPhoneNumber(String phoneNumber) {
  if (phoneNumber.startsWith("+")) {
    return phoneNumber.substring(1);
  }
  return phoneNumber;
}

}