import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:onekitty_admin/configs/country_specifics.dart';
import 'package:onekitty_admin/configs/is_phone_valid.dart';
import 'package:onekitty_admin/helpers/show_toast.dart';
import 'package:onekitty_admin/utils/contact_picker_helper.dart';
import 'package:onekitty_admin/features/onekitty/widgets/custom_international_phone_input.dart';

import 'package:onekitty_admin/features/onekitty/screens/pages/events/controllers/controllers.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/controllers/events_controller.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/controllers/invite_page_controller.dart';
import 'package:onekitty_admin/models/events/operators_model.dart';
import 'package:onekitty_admin/utils/my_text_field.dart';
import 'package:onekitty_admin/utils/my_button.dart';
import 'package:onekitty_admin/utils/themes_colors.dart';
import 'package:shimmer/shimmer.dart';

class InvitePage extends StatefulWidget {
  final int kittyId;
  final String eventname;

  /// whether its event, chama or kitty;
  /// Specifies the classification or category of the operation being performed.
  final String type;
  const InvitePage(
      {super.key,
      required this.kittyId,
      required this.eventname,
      this.type = "event"});

  @override
  State<InvitePage> createState() => _InvitePageState();
}

class _InvitePageState extends State<InvitePage> {
  final TextEditingController _phoneNumber = TextEditingController();
  String? number;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final GlobalControllers _controller = Get.put(GlobalControllers());
  final InvitePageController controller = Get.put(InvitePageController());
  @override
  void initState() {
    super.initState();
    controller.fetchOperators(widget.kittyId);
    _controller.onInit();
  }

  @override
  void dispose() {
    _phoneNumber.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(),
        body: Padding(
          padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 20.h),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  widget.eventname,
                  style: TextStyle(
                    fontSize: 20.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Obx(
                  () => Text(
                    '${controller.operators.length} ${controller.operators.length > 1 ? 'operators'.tr : 'operator'.tr}',
                    style: TextStyle(
                      fontSize: 24.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                SizedBox(height: 20.h),
                Form(
                  key: _formKey,
                  child: CustomInternationalPhoneInput(
                    controller: _phoneNumber,
                    onInputChanged: (PhoneNumber numbers) {
                      number =
                          numbers.phoneNumber.toString().replaceAll("+", '');
                    },
                    suffix: IconButton(
                        onPressed: () async {
                          final cont =
                              await ContactPickerHelper.pickSingleContact(
                                  context: context);
                          if (cont == null) {
                            ToastUtils.showErrorToast(
                                context, 'Error', 'please pick a contact');
                            return;
                          }
                          ValidationResult result =
                              PhoneNumberValidator.isValidPhone(
                                  cont.phones[0].number);
                          if (result.formattedNumber == null) {
                            ToastUtils.showErrorToast(context, 'Error',
                                'please pick a valid contact');
                            return;
                          }
                          String finalNumber = '';
                          if (result.countryCode == null ||
                              result.countryCode == '') {
                            final ogNumber =
                                result.originalNumber.startsWith('0')
                                    ? result.originalNumber.substring(1)
                                    : result.originalNumber;
                            final sanitizedNumber = ogNumber
                                .replaceAll(' ', '')
                                .replaceAll('(', '')
                                .replaceAll(')', '')
                                .replaceAll('-', '')
                                .replaceAll('+', "")
                                .replaceAll('*', '')
                                .replaceAll('#', '');
                            finalNumber =
                                "${CountryConfig.dialCodeMinus}$sanitizedNumber";
                          } else {
                            finalNumber = (result.formattedNumber ?? '');
                          }
                          showDialog(
                              context: context,
                              builder: (BuildContext context) =>
                                  OperatorsDetailsForm(
                                    phone: finalNumber,
                                    kittyId: widget.kittyId,
                                    name: cont.displayName,
                                  )).whenComplete(() {
                            _phoneNumber.clear();
                          });
                        },
                        icon: const Icon(Icons.contacts)),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'phone_number_is_required'.tr;
                      }
                      return null;
                    },
                  ),
                ),
                SizedBox(height: 20.h),
                ElevatedButton(
                  onPressed: () {
                    if (_formKey.currentState!.validate()) {
                      showDialog(
                          context: context,
                          builder: (BuildContext context) =>
                              OperatorsDetailsForm(
                                phone: number ?? '',
                                kittyId: widget.kittyId,
                              )).whenComplete(() {
                        _phoneNumber.clear();
                      });
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: primaryColor,
                    padding: EdgeInsets.symmetric(vertical: 10.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(24.r),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.add, color: Colors.white),
                      SizedBox(width: 8.w),
                      Text(
                        'invite_member'.tr,
                        style: TextStyle(fontSize: 16.sp, color: Colors.white),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 20.h),
                Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    '${widget.type} ${'operators'.tr}',
                    style:
                        TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600),
                  ),
                ),
                SizedBox(height: 8.h),
                Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    'operators_management_click'
                        .tr
                        .replaceAll('{type}', widget.type),
                    style: TextStyle(
                        fontSize: 13.sp,
                        color: Colors.grey[900],
                        fontStyle: FontStyle.italic),
                  ),
                ),
                SizedBox(height: 20.h),
                Obx(() => controller.isLoadingOperators.value
                    ? ListView.builder(
                        itemCount: 3,
                        shrinkWrap: true,
                        itemBuilder: (context, index) {
                          return Shimmer.fromColors(
                            baseColor: Colors.grey[300]!,
                            highlightColor: Colors.grey[100]!,
                            child: Container(
                              margin: const EdgeInsets.all(4),
                              width: double.infinity,
                              height: 100.h,
                              color: Colors.white,
                            ),
                          );
                        })
                    : controller.operators.isEmpty
                        ? Center(child: Text('no_operators_found'.tr))
                        : ListView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: controller.operators.length,
                            itemBuilder: (context, index) {
                              if (controller.isLoadingOperators.value) {
                                return ListView.builder(
                                    itemCount: 3,
                                    shrinkWrap: true,
                                    physics:
                                        const NeverScrollableScrollPhysics(),
                                    itemBuilder: (context, index) {
                                      return Shimmer.fromColors(
                                        baseColor: Colors.grey[300]!,
                                        highlightColor: Colors.grey[100]!,
                                        child: Container(
                                          margin: const EdgeInsets.all(4),
                                          width: double.infinity,
                                          height: 100.h,
                                          color: Colors.white,
                                        ),
                                      );
                                    });
                              }
                              return Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  InkWell(
                                    onTap: () {
                                      showDialog(
                                          context: context,
                                          builder: (context) => EditOperator(
                                              details: controller.operators[
                                                  index])).whenComplete(
                                          () => _phoneNumber.clear());
                                    },
                                    child: Row(children: [
                                      Text('${index + 1}'),
                                      SizedBox(width: 8.w),
                                      const CircleAvatar(
                                        child: Icon(Icons.person),
                                      ),
                                      SizedBox(width: 8.w),
                                      Expanded(
                                        child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                  "${controller.operators[index].firstName == "" && controller.operators[index].lastName == "" ? controller.operators[index].phoneNumber : ''}"
                                                  "${controller.operators[index].firstName} ${controller.operators[index].lastName}",
                                                  style: const TextStyle(
                                                      fontSize: 16,
                                                      fontWeight:
                                                          FontWeight.w600)),
                                              SizedBox(height: 2.h),
                                              Row(
                                                children: [
                                                  Text(
                                                    controller
                                                        .operators[index].role,
                                                    style: TextStyle(
                                                        fontSize: 14.spMin,
                                                        color:
                                                            Colors.grey[700]),
                                                  ),
                                                  SizedBox(width: 8.w),
                                                  Expanded(
                                                    child: Row(children: [
                                                      if (controller
                                                          .operators[index]
                                                          .isSignatory)
                                                        Container(
                                                          width: 82.w,
                                                          padding:
                                                              const EdgeInsets
                                                                  .symmetric(
                                                                  horizontal:
                                                                      6.0,
                                                                  vertical:
                                                                      2.0),
                                                          decoration:
                                                              BoxDecoration(
                                                            border: Border.all(
                                                                color: const Color(
                                                                    0xFFD4AF37)),
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        25),
                                                            color: Colors
                                                                .transparent,
                                                          ),
                                                          child: Row(
                                                            children: [
                                                              Icon(
                                                                  Icons
                                                                      .verified_outlined,
                                                                  color: const Color(
                                                                      0xFFD4AF37),
                                                                  size: 12.sp),
                                                              const SizedBox(
                                                                  width: 4),
                                                              FittedBox(
                                                                child: Text(
                                                                  'signatory'
                                                                      .tr,
                                                                  style:
                                                                      TextStyle(
                                                                    color: const Color(
                                                                        0xFFD4AF37),
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .bold,
                                                                    fontSize: 10
                                                                        .spMin,
                                                                  ),
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      SizedBox(width: 4.sp),
                                                      if (controller
                                                          .operators[index]
                                                          .adminLocked)
                                                        Container(
                                                          width: 120.w,
                                                          padding:
                                                              const EdgeInsets
                                                                  .symmetric(
                                                                  horizontal:
                                                                      6.0,
                                                                  vertical:
                                                                      2.0),
                                                          decoration:
                                                              BoxDecoration(
                                                            border: Border.all(
                                                              color: Theme.of(
                                                                      context)
                                                                  .primaryColor,
                                                            ),
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        25),
                                                            color: Colors
                                                                .transparent,
                                                          ),
                                                          child: Row(
                                                            children: [
                                                              Icon(Icons.lock,
                                                                  color: Theme.of(
                                                                          context)
                                                                      .primaryColor,
                                                                  size: 12.sp),
                                                              const SizedBox(
                                                                  width: 4),
                                                              Expanded(
                                                                child:
                                                                    AutoSizeText(
                                                                  'admin_locked'
                                                                      .tr,
                                                                  maxLines: 1,
                                                                  minFontSize:
                                                                      7,
                                                                  style:
                                                                      TextStyle(
                                                                    color: Theme.of(
                                                                            context)
                                                                        .primaryColor,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .bold,
                                                                  ),
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                    ]),
                                                  )
                                                ],
                                              ),
                                            ]),
                                      ),
                                      if (controller
                                              .operators[index].phoneNumber !=
                                          Get.put(Eventcontroller())
                                              .getLocalUser()
                                              ?.phoneNumber)
                                        IconButton(
                                          icon: const Icon(Icons.close,
                                              color: Colors.grey),
                                          onPressed: () {
                                            showDialog(
                                              context: context,
                                              builder: (BuildContext context) {
                                                return AlertDialog(
                                                  title: Text(
                                                      'confirm_deletion'.tr),
                                                  content: Text(
                                                      'confirm_delete_operator'
                                                          .tr),
                                                  actions: [
                                                    TextButton(
                                                      onPressed: () {
                                                        Navigator.of(context)
                                                            .pop(); // Close the dialog
                                                      },
                                                      child: Text('cancel'.tr),
                                                    ),
                                                    TextButton(
                                                      onPressed: () {
                                                        controller.deleteOperator(
                                                            controller
                                                                    .operators[
                                                                        index]
                                                                    .id ??
                                                                0,
                                                            widget.kittyId);
                                                        Navigator.of(context)
                                                            .pop(); // Close the dialog
                                                      },
                                                      child: Text('delete'.tr),
                                                    ),
                                                  ],
                                                );
                                              },
                                            );
                                          },
                                        ),
                                    ]),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 8.0),
                                    child: Divider(color: Colors.grey[300]),
                                  ),
                                ],
                              );
                            })),
              ],
            ),
          ),
        ));
  }
}

class OperatorsDetailsForm extends StatelessWidget {
  final int kittyId;
  final String phone;
  final String? name;
  const OperatorsDetailsForm(
      {super.key, required this.kittyId, required this.phone, this.name});

  @override
  Widget build(BuildContext context) {
    final isAdminLocked = false.obs;
    List<String> nameParts = name?.split(' ') ?? [];
    String firstname = nameParts.isNotEmpty ? nameParts.first : '';
    String secondname =
        nameParts.length > 1 ? nameParts.sublist(1).join(' ') : '';
    final formValid = GlobalKey<FormState>();
    final firstName = TextEditingController(text: firstname);
    final lastName = TextEditingController(text: secondname);
    RxString selectedRole = 'VIEWER'.obs;
    RxBool isSignatory = false.obs;
    final _controller = Get.put(InvitePageController());
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 18),
      child: AlertDialog(
        insetPadding: const EdgeInsets.all(8),
        contentPadding: const EdgeInsets.all(8),
        title: Text('${'invite_member'.tr} - $phone'),
        content: SingleChildScrollView(
          child: Form(
            key: formValid,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                MyTextFieldwValidator(
                  title: '${'first_name'.tr}: ',
                  controller: firstName,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'first_name_required'.tr;
                    }
                    return null;
                  },
                ),
                MyTextFieldwValidator(
                  title: 'last_name'.tr,
                  controller: lastName,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'last_name_required'.tr;
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 20),
                Text('${'select_role'.tr}: ',
                    style: const TextStyle(fontWeight: FontWeight.w600)),
                const SizedBox(height: 10),
                Row(
                  children: [
                    Obx(() => Checkbox(
                          value: isSignatory.value,
                          onChanged: (bool? value) {
                            isSignatory.value = value!;
                          },
                        )),
                    Text('is_signatory'.tr),
                    const SizedBox(width: 5),
                    Tooltip(
                      message: 'signatory_tooltip'.tr,
                      triggerMode: TooltipTriggerMode.tap,
                      child: const Icon(Icons.info_outline, size: 18),
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                Obx(() => Wrap(
                      spacing: 8.0,
                      children: ["VIEWER", "ADMIN", "TREASURER", "SECRETARY"]
                          .map((String role) {
                        return ChoiceChip(
                          label: Text(role),
                          selected: selectedRole.value == role,
                          showCheckmark: true,
                          selectedColor:
                              ColorUtil.yellowOrange.withOpacity(0.85),
                          onSelected: (bool selected) {
                            if (selected) {
                              selectedRole.value = role;
                            }
                          },
                        );
                      }).toList(),
                    )),
              ],
            ),
          ),
        ),
        actions: <Widget>[
          MyButton(
            width: 120.w,
            outlined: true,
            label: 'cancel'.tr,
            onClick: () => Navigator.of(context).pop(),
          ),
          Obx(
            () => MyButton(
              width: 150.w,
              showLoading: _controller.isAddingOperators.value,
              label: 'invite'.tr,
              onClick: () async {
                if (formValid.currentState!.validate()) {
                  await _controller
                      .addOperator(
                          kittyId,
                          firstName.text,
                          lastName.text,
                          selectedRole.value,
                          phone,
                          isSignatory.value,
                          isAdminLocked.value)
                      .whenComplete(() {
                    // Clear text fields after successful operation
                    firstName.clear();
                    lastName.clear();
                    selectedRole.value = 'VIEWER';
                    isSignatory.value = false;
                    Navigator.pop(context);
                  });
                }
              },
            ),
          ),
        ],
      ),
    );
  }
}

class EditOperator extends StatelessWidget {
  final OperatorsModel details;

  const EditOperator({super.key, required this.details});

  @override
  Widget build(BuildContext context) {
    final isAdminLocked = details.adminLocked.obs;
    final number = CountryConfig.initPhone(phoneNumber: details.whatsappNumber);
    final whatsappController = TextEditingController(
        text: details.whatsappNumber.length >= 9
            ? details.whatsappNumber
                .substring(details.whatsappNumber.length - 9)
            : details.whatsappNumber);

    final formValid = GlobalKey<FormState>();
    final firstName = TextEditingController(text: details.firstName);
    final lastName = TextEditingController(text: details.lastName);
    RxBool isSignatory = details.isSignatory.obs;
    final email = TextEditingController(text: details.email);
    RxString selectedRole = details.role.obs;
    String whatsappNumber = details.whatsappNumber;
    final _controller = Get.put(InvitePageController());

    return Padding(
        padding: const EdgeInsets.all(8.0),
        child: Dialog(
          insetPadding: const EdgeInsets.all(8),
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              children: [
                  Row(
                            children: [
                              Expanded(child: Text('edit_member'.tr, style: Theme.of(context).textTheme.titleMedium,),),
                              details.adminLocked
                                  ? Container(
                                      width: 100.w,
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 6.0, vertical: 2.0),
                                      decoration: BoxDecoration(
                                        border: Border.all(
                                          color: Theme.of(context).primaryColor,
                                        ),
                                        borderRadius: BorderRadius.circular(25),
                                        color: Colors.transparent,
                                      ),
                                      child: Row(
                                        children: [
                                          Icon(Icons.lock,
                                              color: Theme.of(context)
                                                  .primaryColor,
                                              size: 12.sp),
                                          const SizedBox(width: 4),
                                          Expanded(
                                            child: Text(
                                              'admin_locked'.tr,
                                              style: TextStyle(
                                                color: Theme.of(context)
                                                    .primaryColor,
                                                fontWeight: FontWeight.bold,
                                                fontSize: 10.spMin,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    )
                                  : const SizedBox()
                            ],
                          ),
                Expanded(
                  child: SingleChildScrollView(
                    child: Form(
                      key: formValid,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                        
                          MyTextFieldwValidator(
                            title: '${'first_name'.tr}: ',
                            controller: firstName,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'first_name_required'.tr;
                              }
                              return null;
                            },
                          ),
                          MyTextFieldwValidator(
                            title: 'last_name'.tr,
                            controller: lastName,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'last_name_required'.tr;
                              }
                              return null;
                            },
                          ),
                          SizedBox(height: 8.h),
                          Text('whatsapp_number'.tr,
                              style:
                                  const TextStyle(fontWeight: FontWeight.bold)),
                          CustomInternationalPhoneInput(
                            controller: whatsappController,
                            initialPhoneNumber: whatsappNumber,
                            onInputChanged: (PhoneNumber number) {
                              whatsappNumber = number.phoneNumber
                                  .toString()
                                  .replaceAll("+", '');
                              print(whatsappNumber);
                            },
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'phone_number_is_required'.tr;
                              }
                              return null;
                            },
                          ),
                          MyTextFieldwValidator(
                            title: 'email'.tr,
                            controller: email,
                          ),
                          const SizedBox(height: 20),
                          details.adminLocked
                              ? const SizedBox()
                              : Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text('select_role'.tr,
                                        style: const TextStyle(
                                            fontWeight: FontWeight.w600)),
                                    Row(
                                      children: [
                                        Obx(() => Checkbox(
                                              value: isSignatory.value,
                                              onChanged: (bool? value) {
                                                isSignatory.value = value!;
                                              },
                                            )),
                                        Text('is_signatory'.tr),
                                        const SizedBox(width: 5),
                                        const Tooltip(
                                          message:
                                              'A signatory is a person authorized to approve financial transactions on behalf of the members.',
                                          triggerMode: TooltipTriggerMode.tap,
                                          child: Icon(Icons.info_outline,
                                              size: 18),
                                        ),
                                      ],
                                    ),
                                    SizedBox(height: 8.sp),
                                  ],
                                ),
                          Obx(() => Wrap(
                                spacing: 8.0,
                                children: [
                                  "VIEWER",
                                  "ADMIN",
                                  "TREASURER",
                                  "SECRETARY"
                                ].map((String role) {
                                  return ChoiceChip(
                                    label: Text(role),
                                    selected: selectedRole.value == role,
                                    showCheckmark: true,
                                    selectedColor:
                                        ColorUtil.yellowOrange.withOpacity(0.8),
                                    onSelected: (bool selected) {
                                      if (selected) {
                                        selectedRole.value = role;
                                      }
                                    },
                                  );
                                }).toList(),
                              )),
                          details.adminLocked
                              ? const SizedBox()
                              : Obx(
                                  () => Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      const Divider(),
                                      OutlinedButton.icon(
                                        onPressed: () {
                                          showDialog(
                                            context: context,
                                            builder: (BuildContext context) {
                                              return AlertDialog(
                                                title: Text('admin_lock'.tr),
                                                content: isAdminLocked.value
                                                    ? Text('admin_lock_info'.tr)
                                                    : Text('admin_lock_confirm'
                                                        .tr),
                                                actions: [
                                                  if (isAdminLocked.value)
                                                    TextButton(
                                                      onPressed: () =>
                                                          Navigator.of(context)
                                                              .pop(),
                                                      child: Text('close'.tr),
                                                    ),
                                                  if (!isAdminLocked.value)
                                                    TextButton(
                                                      onPressed: () =>
                                                          Navigator.of(context)
                                                              .pop(),
                                                      child: Text('cancel'.tr),
                                                    ),
                                                  if (!isAdminLocked.value)
                                                    TextButton(
                                                      onPressed: () {
                                                        isAdminLocked.value =
                                                            true;
                                                        Navigator.of(context)
                                                            .pop();
                                                      },
                                                      child:
                                                          Text('lock_admin'.tr),
                                                    ),
                                                ],
                                              );
                                            },
                                          );
                                        },
                                        label: Text('admin_locked'.tr),
                                        icon: isAdminLocked.value
                                            ? const Icon(Icons.lock)
                                            : const Icon(Icons.lock_open),
                                      ),
                                      const Divider(),
                                    ],
                                  ),
                                ),
                        ],
                      ),
                    ),
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: <Widget>[
                    MyButton(
                      width: 163.w,
                      outlined: true,
                      label: 'cancel'.tr,
                      onClick: () => Navigator.of(context).pop(),
                    ),
                    Obx(
                      () => MyButton(
                        width: 163.w,
                        showLoading: _controller.isUpdatingOperator.value,
                        label: 'update'.tr,
                        onClick: () async {
                          if (formValid.currentState!.validate()) {
                            await _controller.updateOperator({
                              "ID": details.id,
                              "kitty_id": details.kittyId,
                              "first_name": firstName.text,
                              "last_name": lastName.text,
                              "role": selectedRole.value,
                              "user_id": null,
                              "is_signatory": isSignatory.value,
                              "status": "ACTIVE",
                              // "phone_number": details.phon,
                              "whatsapp_number": whatsappNumber,
                              "email": email.text,
                              "is_admin_locked": isAdminLocked.value,
                            }, details.kittyId).whenComplete(() {
                              // Clear text fields after successful operation
                              firstName.clear();
                              lastName.clear();
                              email.clear();
                              whatsappController.clear();
                              selectedRole.value = 'VIEWER';
                              isSignatory.value = false;
                              Navigator.pop(context);
                            });
                          }
                        },
                      ),
                    ),
                  ],
                )
              ],
            ),
          ),
        ));
  }
}
