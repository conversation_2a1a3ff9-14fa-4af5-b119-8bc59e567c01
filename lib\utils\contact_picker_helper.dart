import 'package:flutter/material.dart';
import 'package:flutter_contacts/contact.dart';
import 'package:get/get.dart';
import 'package:onekitty_admin/configs/country_specifics.dart';
import 'package:onekitty_admin/features/onekitty/widgets/getx_contact_picker.dart';

/// Helper class for consistent contact picker usage across the app
class ContactPickerHelper {
  /// Show contact picker for multiple contact selection
  /// Returns List<Contact>? - null if cancelled, empty list if no contacts selected
  static Future<List<Contact>?> pickMultipleContacts({
    required BuildContext context,
    String title = 'Select Contacts',
    List<Contact>? preSelectedContacts,
    bool useFullScreen = true,
  }) async {
    if (useFullScreen) {
      return await Get.to(() => GetXContactPicker(
        mode: ContactPickerMode.multiple,
        display: ContactPickerDisplay.fullScreen,
        title: title,
        preSelectedContacts: preSelectedContacts,
      ));
    } else {
      return await showDialog<List<Contact>>(
        context: context,
        builder: (context) => Dialog(
          child: GetXContactPicker(
            mode: ContactPickerMode.multiple,
            title: title,
            preSelectedContacts: preSelectedContacts,
          ),
        ),
      );
    }
  }

  /// Show contact picker for single contact selection
  /// Returns Contact? - null if cancelled
  static Future<Contact?> pickSingleContact({
    required BuildContext context,
    String title = 'Select Contact',
    bool useFullScreen = false,
  }) async {
    if (useFullScreen) {
      return await Get.to(() => GetXContactPicker(
        mode: ContactPickerMode.single,
        display: ContactPickerDisplay.fullScreen,
        title: title,
      ));
    } else {
      return await showDialog<Contact>(
        context: context,
        builder: (context) => Dialog(
          child: GetXContactPicker(
            mode: ContactPickerMode.single,
            title: title,
          ),
        ),
      );
    }
  }

  /// Helper method to format phone number consistently
  static String formatPhoneNumber(String phoneNumber) {
    // Remove any formatting
    phoneNumber = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    
    // Convert to standard format
    if (phoneNumber.startsWith('${CountryConfig.dialCodeMinus}')) {
      phoneNumber = '0${phoneNumber.substring(3)}';
    } else if (!phoneNumber.startsWith('0')) {
      phoneNumber = '0$phoneNumber';
    }
    
    return phoneNumber;
  }

  /// Helper method to get normalized phone number for API calls
  static String getNormalizedPhoneNumber(Contact contact) {
    if (contact.phones.isEmpty) return '';
    
    String phoneNumber = contact.phones.first.normalizedNumber;
    if (phoneNumber.isEmpty) {
      phoneNumber = contact.phones.first.number;
    }
    
    return formatPhoneNumber(phoneNumber);
  }

  /// Helper method to get display name for contact
  static String getDisplayName(Contact contact) {
    if (contact.displayName.isNotEmpty) {
      return contact.displayName;
    }
    
    if (contact.name.first.isNotEmpty || contact.name.last.isNotEmpty) {
      return '${contact.name.first} ${contact.name.last}'.trim();
    }
    
    if (contact.phones.isNotEmpty) {
      return contact.phones.first.number;
    }
    
    return 'Unknown Contact';
  }
}
