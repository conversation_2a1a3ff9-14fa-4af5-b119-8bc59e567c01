import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty_admin/utils/custom_button.dart';

class Alert {
  void showDeleteDialog(index, context,
      {required Function()? function,
      required String title,
      required bool isLoading}) {
    showDialog(
        context: context,
        builder: (context) {
          return AlertDialog(
            actions: [
              OutlinedButton(
                  onPressed: () {
                    Get.back();
                  },
                  child: const Text("No")),
              Obx(() => CustomKtButton(
                  width: 55.w,
                  height: 35.h,
                  isLoading: isLoading,
                  onPress: () {
                    function;
                    Navigator.pop(context);
                  },
                  btnText: "Yes"))
            ],
            content: Text(title),
          );
        });
  }
}

void showConfirmDialog(context,
    {required Function()? function, required String title}) {
  showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          actions: [
            OutlinedButton(
                onPressed: () {
                  Get.back();
                },
                child: const Text("No")),
            CustomKtButton(
                width: 100.w, height: 40.h, onPress: function, btnText: "Yes")
          ],
          content: Text(title),
        );
      });
}
