import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onekitty_admin/features/admin/controllers/kitty_dashboard_controller.dart';
import 'package:onekitty_admin/features/admin/core/my_pluto_grid/my_pluto_grid.dart';
import 'package:onekitty_admin/features/admin/core/widgets/kitty_loading_widget.dart';
import 'package:onekitty_admin/features/admin/widgets/kitty_filter_widget.dart';

class KittyDashboard extends StatefulWidget {
  const KittyDashboard({super.key});

  @override
  State<KittyDashboard> createState() => _KittyDashboardState();
}

class _KittyDashboardState extends State<KittyDashboard> {
  late final KittyDashboardController controller;

  @override
  void initState() {
    super.initState();
    controller = Get.put(KittyDashboardController());
  }

  @override
  void dispose() {
    Get.delete<KittyDashboardController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() {
        if (controller.isLoading.value && controller.kitties.isEmpty) {
          return const KittyLoadingWidget(
            message: 'Loading kitties...',
            size: 70.0,
          );
        }

        if (controller.hasError.value && controller.kitties.isEmpty) {
          return _buildErrorState(context);
        }

        return _buildTable(context);
      }),
    );
  }



  Widget _buildTable(BuildContext context) {
    return Obx(() => AppTable(
          title: 'Kitty Dashboard',
          actions: [
            ActionButton(
              icon: Icons.filter_list,
              label: 'Filters',
              onTap: () => _showFilterDialog(context),
            ),
            ActionButton(
              icon: Icons.refresh,
              label: 'Refresh',
              onTap: controller.refreshData,
            ),
            ActionButton(
              icon: Icons.download,
              label: 'Export',
              onTap: () => _showExportDialog(context),
            ),
          ],
          columns: controller.plutoColumns,
          rows: controller.plutoRows,
          totalPages: controller.totalPages.value,
          totalSizePerPage: controller.pageSize.value,
          onPageNavigated: controller.handlePageChange,
          onItemClicked: controller.onKittyTapped,
          onRefresh: controller.refreshData,
          onSearch: controller.handleSearch,
          cacheKey: 'kitty_dashboard',
          enableSorting: true,
          enableExport: true,
          enableMultiSelect: false,
          emptyStateWidget: _buildEmptyState(context),
          loadingWidget: const KittyLoadingWidget(
            message: 'Loading kitties...',
            size: 60.0,
          ),
          isExternalLoading: controller.isLoading.value && controller.kitties.isNotEmpty,
        ));
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inbox_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No kitties found',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.grey[600],
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try adjusting your search or filters',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[500],
                ),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: controller.clearFilters,
            icon: const Icon(Icons.clear_all),
            label: const Text('Clear Filters'),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Failed to load kitties',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.red[600],
                ),
          ),
          const SizedBox(height: 8),
          Obx(() => Text(
                controller.apiMessage.value,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.red[500],
                    ),
                textAlign: TextAlign.center,
              )),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: controller.refreshData,
            icon: const Icon(Icons.refresh),
            label: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: 400,
          padding: const EdgeInsets.all(24),
          child: KittyFilterWidget(controller: controller),
        ),
      ),
    );
  }

  void _showExportDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.file_download),
              title: const Text('Export as CSV'),
              onTap: () {
                Navigator.pop(context);
                controller.exportData('csv');
              },
            ),
            ListTile(
              leading: const Icon(Icons.file_download),
              title: const Text('Export as Excel'),
              onTap: () {
                Navigator.pop(context);
                controller.exportData('excel');
              },
            ),
            ListTile(
              leading: const Icon(Icons.file_download),
              title: const Text('Export as PDF'),
              onTap: () {
                Navigator.pop(context);
                controller.exportData('pdf');
              },
            ),
          ],
        ),
      ),
    );
  }
}
