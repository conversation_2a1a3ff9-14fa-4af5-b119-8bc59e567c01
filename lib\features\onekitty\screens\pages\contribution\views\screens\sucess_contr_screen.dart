import 'package:date_time_format/date_time_format.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animated_dialog_updated/flutter_animated_dialog.dart';
// import 'package:flutter_animated_dialog_updated/flutter_animated_dialog.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty_admin/features/onekitty/controllers/contribute_controller.dart';
import 'package:onekitty_admin/helpers/colors.dart';
import 'package:onekitty_admin/helpers/show_toast.dart';
import 'package:onekitty_admin/utils/size_config.dart';
import 'package:onekitty_admin/utils/utils_exports.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';

class SuccessfulContibutionPage extends StatelessWidget {
  final  contributeController = Get.put(ContributeController());
  SuccessfulContibutionPage({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        //appBar: _buildAppBar(context),
        body: SingleChildScrollView(
          child: Container(
            width: double.maxFinite,
            padding: EdgeInsets.symmetric(horizontal: 31.w, vertical: 5.h),
            child: Column(
              children: [
                SizedBox(height: 3.h),
                const RowAppBar(),
                Text(contributeController.kittGoten.value.title ?? "",
                    style: theme.textTheme.titleLarge),
                SizedBox(height: 16.h),
                Align(
                    alignment: Alignment.centerLeft,
                    child: Container(
                        width: 343.w,
                        margin: EdgeInsets.only(right: 24.w),
                        child: RichText(
                            text: TextSpan(children: [
                              TextSpan(
                                  text:
                                      "${"every_shilling_counts_message".tr} ",
                                  style: CustomTextStyles.bodySmallff545963),
                              TextSpan(
                                  text: contributeController
                                          .kittGoten.value.title ??
                                      "",
                                  style: CustomTextStyles.labelLargeff545963)
                            ]),
                            textAlign: TextAlign.center))),
                SizedBox(height: 16.h),
                _buildFrame(context),
                SizedBox(height: 40.h),
                const CustomImageView(
                    imagePath: AssetUrl.tickCircle, height: 160, width: 160),
                SizedBox(height: 39.h),
                Text("thank_you".tr, style: theme.textTheme.titleLarge),
                SizedBox(height: 5.h),
                Text("contribution_successfully_received".tr,
                    style: CustomTextStyles.bodyMediumff545963),
                SizedBox(height: 2.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CustomImageView(
                        imagePath: AssetUrl.imgGrommetIconsMoney,
                        height: 24.h,
                        width: 20.w,
                        margin: EdgeInsets.only(top: 3.h)),
                    Padding(
                        padding: EdgeInsets.only(left: 8.w),
                        child: RichText(
                            text: TextSpan(
                              children: [
                                TextSpan(
                                    text: "amount_received".tr,
                                    style: CustomTextStyles.bodyMediumff545963),
                                TextSpan(
                                    text: contributeController
                                        .resContrData["trans_amount"]
                                        .toString(),
                                    style: CustomTextStyles.titleMediumff545963)
                              ],
                            ),
                            textAlign: TextAlign.left))
                  ],
                ),
                SizedBox(height: 8.h),
                CustomOutlinedButton(
                    onPressed: () {
                      showTransactionDialog(
                        context: context,
                        controller: ContributeController(),
                        amount:
                            contributeController.resContrData["trans_amount"],
                        title: contributeController.kittGoten.value.title,
                        phoneNo:
                            contributeController.resContrData["phone_number"],
                        firstName:
                            contributeController.resContrData["first_name"],
                        secondName:
                            contributeController.resContrData["second_name"],
                        transactioncode: contributeController
                            .resContrData["transaction_code"],
                        kittyId: contributeController.kittGoten.value.iD,
                      );
                    },
                    height: 40.h,
                    width: 201.w,
                    text: "view_payment_details".tr,
                    buttonStyle: CustomButtonStyles.outlinePrimary,
                    buttonTextStyle: CustomTextStyles.titleSmallIndigo500),
                SizedBox(height: 36.h),
                Container(
                    width: 344.w,
                    margin: EdgeInsets.only(left: 10.w, right: 12.w),
                    child: Text(
                        "share_kitty_contribution_link".tr,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.center,
                        style: CustomTextStyles.bodyMediumff545963)),
                SizedBox(height: 11.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: InkWell(
                          onTap: () async {
                            await Share.share(contributeController
                                .contrisocials["instagram"]
                                .toString());
                          },
                          child: Image.asset("assets/images/insta.png")),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: InkWell(
                          onTap: () async {
                            await Share.share(contributeController
                                .contrisocials["tiktok"]
                                .toString());
                          },
                          child: SvgPicture.asset(
                            "assets/images/tiktok-outline.svg",
                            colorFilter: const ColorFilter.mode(
                                AppColors.primary, BlendMode.srcIn),
                          )),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: InkWell(
                          onTap: () async {
                            final Uri url = Uri.parse(
                                "https://twitter.com/intent/tweet?text=${contributeController.contrisocials["twitter"].toString()}");
                            if (!await launchUrl(url)) {
                              ToastUtils.showErrorToast(
                                  context, "could_not_launch_url".tr, "error".tr);
                            }
                          },
                          child: Image.asset("assets/images/twitter.png")),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: InkWell(
                          onTap: () async {
                            final Uri url = Uri.parse(
                                "https://www.facebook.com/sharer/sharer.php?quote=${contributeController.contrisocials["facebook"]}");
                            if (!await launchUrl(url)) {
                              ToastUtils.showErrorToast(
                                  context, "could_not_launch_url".tr, "error".tr);
                            }
                          },
                          child: Image.asset(
                            "assets/images/Vector (7).png",
                            //color: AppColors.blueButtonColor
                          )),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: InkWell(
                          onTap: () async {
                            await Share.share(contributeController
                                .contrisocials["youtube"]
                                .toString());
                          },
                          child: Image.asset(
                            "assets/images/Vector (6).png",
                            //color: AppColors.blueButtonColor,
                          )),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Section Widget
  Widget _buildFrame(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(right: 2.w),
      padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 14.h),
      decoration: AppDecoration.shadow1
          .copyWith(borderRadius: BorderRadiusStyle.roundedBorder6),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              CustomImageView(
                  imagePath: AssetUrl.imgCalendar,
                  height: 24,
                  width: 24,
                  margin: EdgeInsets.only(top: 4.h)),
              Padding(
                padding: EdgeInsets.only(left: 4.w, top: 6.h, bottom: 4.h),
                child: RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: "${"created_on".tr} ",
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: appTheme.gray90001,
                        ),
                      ),
                      TextSpan(
                        text: DateFormat('MMM dd, yyyy').format(
                            contributeController.kittGoten.value.createdAt ??
                                DateTime.now()),
                        style: CustomTextStyles.bodySmallGray900,
                      ),
                    ],
                  ),
                ),
              ),
              const Spacer(),
              Container(
                width: 80.w,
                padding:
                    const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                decoration: BoxDecoration(
                  border:
                      Border.all(color: Colors.white), // Add border decoration
                  borderRadius:
                      BorderRadius.circular(5), // Optional: add border radius
                ),
                child: Text(
                  contributeController.kittStatus.value,
                  style: const TextStyle(
                    color: Colors.green,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 13.h),
          Row(
            children: [
              const CustomImageView(
                  imagePath: AssetUrl.imgClock, height: 24, width: 24),
              Padding(
                padding: EdgeInsets.only(left: 4.w, top: 4.h),
                child: RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: DateTimeFormat.relative(
                          contributeController.kittGoten.value.endDate ??
                              DateTime.now(),
                          levelOfPrecision: 2,
                          prependIfBefore: 'ends_in_label'.tr,
                          ifNow: "now".tr,
                          appendIfAfter: 'ago'.tr,
                        ),
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: appTheme.gray90001,
                        ),
                      ),
                    ],
                  ),
                ),
              )
            ],
          ),
          SizedBox(height: 4.h)
        ],
      ),
    );
  }

  /// Navigates back to the previous screen.
  onTapArrowLeft(BuildContext context) {
    Navigator.pop(context);
  }
}

showTransactionDialog({
  required BuildContext context,
  required ContributeController controller,
  required amount,
  required title,
  required phoneNo,
  required firstName,
  required secondName,
  required transactioncode,
  required kittyId,
}) {
  return showAnimatedDialog(
    barrierDismissible: true,
    animationType: DialogTransitionType.sizeFade,
    curve: Curves.fastOutSlowIn,
    duration: const Duration(milliseconds: 900),
    context: context,
    builder: (BuildContext context) {
      return Dialog(
        child: SizedBox(
          height: SizeConfig.screenHeight * .36,
          width: SizeConfig.screenWidth * .8,
          child: Padding(
            padding: EdgeInsets.symmetric(
                horizontal: getProportionalScreenWidth(10)),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                    margin: EdgeInsets.only(top: 3.h, bottom: 2.h),
                    padding: EdgeInsets.all(7.h),
                    decoration: AppDecoration.fillAGray.copyWith(
                        borderRadius: BorderRadiusStyle.circleBorder16),
                    child: Text(
                      '${firstName?.isNotEmpty ?? false ? firstName[0] : '  '}${secondName?.isNotEmpty ?? false ? secondName[0] : '  '}',
                      style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold),
                    )),
                SizedBox(height: 9.h),
                Text(title, style: CustomTextStyles.titleSmallIndigo500),
                SizedBox(height: 7.h),
                Text(
                  "+${amount.toString()}",
                  style: const TextStyle(color: Colors.green),
                ),
                SizedBox(height: 7.h),
                Text("$firstName $secondName", style: const TextStyle()),
                SizedBox(height: 5.h),
                Text(phoneNo.toString(), style: const TextStyle()),
                SizedBox(height: 7.h),
                Text("Transaction ID: $transactioncode",
                    style: const TextStyle()),
                SizedBox(height: 8.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).canvasColor,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20.sp),
                          side: BorderSide(
                              width: 2.sp,
                              color: Theme.of(context).primaryColor),
                        ),
                      ),
                      onPressed: () async {
                        String shareMsg = 'share_msg_transaction_format'.tr
                            .replaceAll('{phoneNo}', phoneNo.toString())
                            .replaceAll('{amount}', amount.toString())
                            .replaceAll('{transactionCode}', transactioncode.toString())
                            .replaceAll('{kittyId}', kittyId.toString());
                        //  \nChannel:${details?.channel}\nDate: ${format.format(createdAt.toLocal())}\nKitty: https://onekitty.co.ke/kitty/${details?.kittyId}";

                        // "Phone Number: ${phoneNo.toString()}\nAmount: ${CountryConfig.getCurrencyCode} ${amount.toString()}\nTransaction Code: $transactioncode\nStatus: ${details.status}\nChannel:${details.channelCode}\nDate: ${format.format(createdAt.toLocal())}\nKitty: https://onekitty.co.ke/kitty/${details.kittyId}";
                        await Share.share(shareMsg,
                            subject: 'Contribution details');
                      },
                      child: Text(
                        "share".tr,
                        style: const TextStyle(color: Colors.black),
                      ),
                    ),

                    // CustomElevatedButton(
                    //   buttonStyle: ButtonStyle(
                    //     backgroundColor: MaterialStateProperty.all<Color>(
                    //         Color.fromARGB(255, 184, 129, 57)),
                    //   ),
                    //   width: 90.w,
                    //   height: 30.h,
                    //   text: "Export",
                    //   buttonTextStyle: TextStyle(
                    //       fontSize: 12.h, fontWeight: FontWeight.bold),
                    //   leftIcon: Container(
                    //       margin: EdgeInsets.only(right: 1.w),
                    //       child: CustomImageView(
                    //           imagePath: AssetUrl.expIcon,
                    //           height: 12.h,
                    //           width: 12.w)),
                    //   onPressed: () async {
                    //     showModalBottomSheet(
                    //       context: context,
                    //       builder: (BuildContext context) {
                    //         return ExportContentWidget();
                    //       },
                    //     );
                    //   },
                    // ),
                    SizedBox(
                      height: 10.sp,
                    ),
                  ],
                )
              ],
            ),
          ),
        ),
      );
    },
  );
}
