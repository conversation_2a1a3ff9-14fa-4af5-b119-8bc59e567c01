import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty_admin/features/onekitty/controllers/contribute_controller.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/controllers/kitty_controller.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/controllers/user_ktty_controller.dart';
import 'package:onekitty_admin/nav_routes/nav_routes.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/views/screens/viewing_single_kitty/viewing_single_kitty_page.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/views/screens/signatory_transactions.dart';

import 'package:onekitty_admin/features/onekitty/screens/pages/home/<USER>/controller/notification_controller.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/home/<USER>/model/notification_model.dart';

class NotificationDetailView extends StatefulWidget {
  final AppNotification notification;

  const NotificationDetailView({super.key, required this.notification});

  @override
  State<NotificationDetailView> createState() => _NotificationDetailViewState();
}

class _NotificationDetailViewState extends State<NotificationDetailView> {
  final controller = Get.find<NotificationController>();
  @override
  void initState() {
    controller.updateNotificationReadStatus(
        [int.tryParse(widget.notification.id) ?? 0]);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final isLoading = false.obs;
    return Scaffold(
      persistentFooterButtons: [
        widget.notification.type == 'signatory_transaction'
            ? SizedBox(
                width: double.infinity,
                child: Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Obx(
                    () => FilledButton.icon(
                      onPressed: () async {
                        if (widget.notification.kittyId != null) {
                          // Navigate to kitty details page
                          Get.to(() => SignatoryTransactions(
                              kittyId: widget.notification.kittyId ?? 0));
                        }
                      },
                      icon: isLoading.value
                          ? SizedBox(
                              width: 24.w,
                              height: 24.w,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: Theme.of(context).colorScheme.onPrimary,
                              ),
                            )
                          : const Icon(Icons.visibility),
                      label: Text(isLoading.value
                          ? 'opening'.tr
                          : 'open_signatory_approvals'.tr),
                      style: ElevatedButton.styleFrom(
                        padding: EdgeInsets.symmetric(vertical: 16.h),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                    ),
                  ),
                ),
              )
            : widget.notification.action == 'view_transactions'
                ? SizedBox(
                    width: double.infinity,
                    child: Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Obx(
                        () => FilledButton.icon(
                          onPressed: () async {
                            if (widget.notification.kittyId != null) {
                              isLoading(true);

                              await Get.find<ContributeController>()
                                  .getKitty(
                                    id: widget.notification.kittyId,
                                  )
                                  .whenComplete(() async =>
                                      await Get.find<KittyController>()
                                          .getKittyContributions(
                                              kittyId:
                                                  widget.notification.kittyId ??
                                                      0)
                                          .whenComplete(() {
                                        print(Get.find<KittyController>()
                                            .transactionsKitty
                                            .toString());
                                        Get.back();
                                        isLoading(false);
                                        Get.toNamed(
                                            NavRoutes.seeAlltranscScreen);
                                      }));
                              // Close loading dialog
                            }
                          },
                          icon: isLoading.value
                              ? SizedBox(
                                  width: 24.w,
                                  height: 24.w,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    color:
                                        Theme.of(context).colorScheme.onPrimary,
                                  ),
                                )
                              : const Icon(Icons.visibility),
                          label: Text(isLoading.value
                              ? 'opening'.tr
                              : 'view_transaction'.tr),
                          style: ElevatedButton.styleFrom(
                            padding: EdgeInsets.symmetric(vertical: 16.h),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                        ),
                      ),
                    ),
                  )
                : widget.notification.action == 'view_kitty'
                    ? SizedBox(
                        width: double.infinity,
                        child: Padding(
                          padding: const EdgeInsets.all(12.0),
                          child: Obx(
                            () => FilledButton.icon(
                              onPressed: () async {
                                if (widget.notification.kittyId != null) {
                                  isLoading(true);
                                  final contributeController =
                                      Get.find<ContributeController>();
                                  final dataController =
                                      Get.put(DataController());
                                  // Load kitty details
                                  final success =
                                      await contributeController.getKitty(
                                    id: widget.notification.kittyId,
                                  );

                                  // Close loading dialog
                                  Get.back();

                                  if (success) {
                                    isLoading(false);
                                    // Update data controller with kitty details
                                    dataController.kitty(
                                        contributeController.singleKitty.value);

                                    // Navigate to kitty details page
                                    Get.to(
                                        () => const ViewingSingleKittyScreen());
                                  } else {
                                    isLoading(false);
                                    Get.snackbar(
                                      'error'.tr,
                                      'failed_to_load_kitty_details'.tr,
                                      snackPosition: SnackPosition.bottom,
                                    );
                                  }
                                }
                              },
                              icon: isLoading.value
                                  ? SizedBox(
                                      width: 24.w,
                                      height: 24.w,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onPrimary,
                                      ),
                                    )
                                  : const Icon(Icons.visibility),
                              label: Text(isLoading.value
                                  ? 'opening'.tr
                                  : 'view_kitty_details'.tr),
                              style: ElevatedButton.styleFrom(
                                padding: EdgeInsets.symmetric(vertical: 16.h),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(10),
                                ),
                              ),
                            ),
                          ),
                        ),
                      )
                    : const SizedBox()
      ],
      appBar: AppBar(
        leadingWidth: 60.w,
        centerTitle: true,
        leading: GestureDetector(
          onTap: () => Navigator.pop(context),
          child: Row(
            children: [
              const Icon(Icons.navigate_before),
              Text('back'.tr),
            ],
          ),
        ),
        title: Text(
          'notification_details'.tr,
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [],
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _DetailCard(
                title: 'title'.tr,
                content: widget.notification.title,
                icon: Icons.title,
              ),
              SizedBox(height: 16.h),
              _DetailCard(
                title: 'description'.tr,
                content: widget.notification.description ??
                    widget.notification.subtitle,
                icon: Icons.description,
              ),
              // if (widget.notification.phoneNumber != null ) ...[
              //   SizedBox(height: 16.h),
              //   if(widget.notification.phoneNumber!.toString().isNotEmpty)
              //   _DetailCard(
              //     title: 'Phone Number',
              //     content: widget.notification.phoneNumber!,
              //     icon: Icons.phone,
              //   ),
              // ],
              if (widget.notification.kittyId != null) ...[
                SizedBox(height: 16.h),
                _DetailCard(
                  title: 'kitty_id'.tr,
                  content: widget.notification.kittyId.toString(),
                  icon: Icons.numbers,
                ),
              ],
              SizedBox(height: 16.h),
              // _DetailCard(
              //   title: 'Type',
              //   content:
              //       notification.type?.replaceAll('_', ' ').toUpperCase() ?? 'N/A',
              //   icon: Icons.category,
              // ),
              SizedBox(height: 16.h),
              // _DetailCard(
              //   title: 'Action',
              //   content: notification.action?.replaceAll('_', ' ').toUpperCase() ?? 'N/A',
              //   icon: Icons.touch_app,
              // ),
              // SizedBox(height: 16.h),
              _DetailCard(
                title: 'created'.tr,
                content:
                    '${widget.notification.created.day}/${widget.notification.created.month}/${widget.notification.created.year} ${'at'.tr} ${TimeOfDay.fromDateTime(widget.notification.created).format(context)}',
                icon: Icons.calendar_today,
              ),
              SizedBox(height: 16.h),
              // _DetailCard(
              //   title: 'Last Updated',
              //   content: '${notification.received.day}/${notification.received.month}/${notification.received.year} at ${TimeOfDay.fromDateTime(notification.received).format(context)}',
              //   icon: Icons.update,
              // ),
              // SizedBox(height: 24.h),
            ],
          ),
        ),
      ),
    );
  }
}

class _DetailCard extends StatelessWidget {
  final String title;
  final String content;
  final IconData icon;

  const _DetailCard({
    required this.title,
    required this.content,
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            size: 24.w,
            color: Theme.of(context).colorScheme.primary,
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Theme.of(context).textTheme.bodySmall?.color,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  content,
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: Theme.of(context).textTheme.bodyLarge?.color,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
