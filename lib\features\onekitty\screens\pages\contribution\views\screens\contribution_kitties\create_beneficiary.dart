import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:onekitty_admin/configs/country_specifics.dart';
import 'package:onekitty_admin/configs/payment_channels.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/controllers/beneficiary_controller.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/controllers/controllers.dart';
import 'package:onekitty_admin/models/auth/payments_channels.dart' as pm;
import 'package:onekitty_admin/models/kitty/beneficiary_model.dart';
import 'package:onekitty_admin/utils/my_button.dart';
import 'package:onekitty_admin/utils/my_text_field.dart';
import 'package:onekitty_admin/utils/payment_kensbuns.dart';
import 'package:onekitty_admin/utils/show_cached_network_image.dart';
import 'package:onekitty_admin/main.dart' show isLight;
import 'package:onekitty_admin/features/onekitty/widgets/custom_international_phone_input.dart';

class CreateBeneficiary extends StatefulWidget {
  final BeneficiaryModel? edit;
  final int kittyId;
  const CreateBeneficiary({super.key, required this.kittyId, this.edit});

  @override
  State<CreateBeneficiary> createState() => _CreateBeneficiaryState();
}

class _CreateBeneficiaryState extends State<CreateBeneficiary> {
  late final PageController pageController;
  final controller = Get.find<BeneficiaryController>();
  final accountNameController = TextEditingController();
  final phoneNumber = TextEditingController();
  final percentageAmountController = TextEditingController();
  final endDateController = TextEditingController();
  final accountNumber = TextEditingController();
  final accountRef = TextEditingController();
  final includeEndDate = false.obs;
  String transferMode = 'WALLET';
  double? amount = 0;
  final Rx<double?> percentage = 0.0.obs;
  final splitConfig = 'Amount'.obs;
  PhoneNumber number = CountryConfig.phoneNumber;
  final accNumber = CountryConfig.phoneNumber.obs;
  bool firstTime = false;
  final maxPercentage = 0.0.obs;
  final paymentChannel = Get.isRegistered<PaymentChannel>()
      ? Get.find<PaymentChannel>()
      : Get.put(PaymentChannel());

  void loadValues() async {
    final beneficiary = widget.edit!;
    accountNameController.text = beneficiary.accountName ?? '';
    if (beneficiary.phoneNumber.isNotEmpty) {
      number =
          PhoneNumber(phoneNumber: beneficiary.phoneNumber.replaceAll(' ', ''));
      phoneNumber.text =
          beneficiary.phoneNumber.replaceAll(' ', '').replaceAll('+', '');
    }

    transferMode = beneficiary.transferMode;

    // Set payment-specific information based on transfer mode
    if (transferMode == 'WALLET') {
      // Mobile Money
      controller.page.value = 0;
      controller.channelName.value = beneficiary.channelName;
      controller.channel = beneficiary.channel;

      // Select the correct channel radio button
      if (beneficiary.channelName == 'Airtel Money') {
        controller.setChannel('Airtel Money', 63903);
      } else if (beneficiary.channelName == 'SasaPay') {
        controller.setChannel('SasaPay', 0);
      } else {
        controller.setChannel('Mpesa', 63902);
      }
    } else if (transferMode == 'PAYBILL') {
      // Paybill
      controller.page.value = 1;

      controller.channelName.value = beneficiary.channelName;
      controller.channel = beneficiary.channel;
      accountNumber.text = beneficiary.accountNumber;
      accountRef.text = beneficiary.accountNumberRef;
    } else if (transferMode == 'TILL') {
      // Till
      controller.page.value = 2;
      controller.channelName.value = beneficiary.channelName;
      controller.channel = beneficiary.channel;
      accountNumber.text = beneficiary.accountNumber;
    } else if (transferMode == 'BANK') {
      // Bank
      controller.page.value = 3;
      controller.channelName.value = 'BANK';

      // Find and set the selected bank
      try {
        final banksList = Get.find<GlobalControllers>()
            .paymentChannels
            .where((e) => e.category == pm.Category.BANK)
            .toList();

        if (banksList.isNotEmpty) {
          var matchingBank = banksList
              .where((e) => e.channelCode == beneficiary.channel)
              .toList();
          if (matchingBank.isNotEmpty) {
            controller.selectedBank.value = matchingBank.first;
          } else {
            controller.selectedBank.value = banksList.first;
          }

          if (controller.selectedBank.value != null) {
            controller.channel = controller.selectedBank.value!.channelCode;
          }
        }

        accountNumber.text = beneficiary.accountNumber;
      } catch (e) {
        print("Error setting bank: $e");
      }
    }

    percentageAmountController.text = beneficiary.splitConfig == "PERCENTAGE"
        ? ((beneficiary.percentage ?? 0) * 100).toInt().toString()
        : beneficiary.amount.toString();

    includeEndDate(beneficiary.endDate != null);
    if (beneficiary.endDate != null) {
      endDateController.text = DateFormat('d MMM yyyy HH : mm a')
          .format(beneficiary.endDate?.toLocal() ?? DateTime.now());
    }

    amount = double.tryParse(beneficiary.amount.toString());
    percentage.value = (beneficiary.percentage?.toDouble() ??
            controller.remainingPercentage.value) *
        100;

    print(
        'percentage :${beneficiary.percentage?.toDouble()}\nremaining:${controller.remainingPercentage.value}');
    if (percentage.value != null &&
        percentage.value! > controller.remainingPercentage.value) {
      maxPercentage.value =
          percentage.value! + (controller.remainingPercentage.value * 100);
    } else {
      maxPercentage.value = controller.remainingPercentage.value * 100;
    }

    splitConfig(
        capitalizeFirstLetter(beneficiary.splitConfig?.toLowerCase() ?? ''));

    // // Important: Update the UI after setting values
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   if (pageController.hasClients) {
    //     print(
    //         'PageController has clients, jumping to page: ${controller.page.value}');
    //     pageController.jumpToPage(controller.page.value);
    //     // Force a UI refresh
    //     setState(() {});
    //   } else {
    //     print('PageController has no clients yet');
    //   }
    // });
  }

  clearText() {
    accountNumber.clear();
    accountRef.clear();
  }

  String capitalizeFirstLetter(String text) {
    if (text.isEmpty) {
      return text;
    }
    return text[0].toUpperCase() + text.substring(1);
  }

  @override
  void initState() {
    if (widget.edit != null) {
      loadValues();
    } else {
      // Set default values only for new beneficiary
      controller.page.value = 0;

      maxPercentage.value = controller.remainingPercentage.value * 100;
    }
    pageController = PageController(initialPage: controller.page.value);

    super.initState();
  }

  @override
  void dispose() {
    accountNameController.dispose();
    phoneNumber.dispose();
    percentageAmountController.dispose();
    endDateController.dispose();
    accountNumber.dispose();
    accountRef.dispose();
    includeEndDate(false);
    transferMode = 'WALLET';
    amount = 0;
    percentage.value = 0.0;
    splitConfig.value = 'Amount';
    number = CountryConfig.phoneNumber;
    accNumber.value = CountryConfig.phoneNumber;
    controller.selectedBank.value = null;
    controller.page.value = 0;
    controller.channelName.value = 'M-PESA';
    controller.channel = 63902;

    // Always dispose controllers
    pageController.dispose();
    // accountNameController.dispose();
    // phoneNumber.dispose();
    // percentageAmountController.dispose();
    // endDateController.dispose();
    // accountNumber.dispose();
    // accountRef.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final formKey = GlobalKey<FormState>();

    return Scaffold(
      appBar: AppBar(
        title: Text(
            widget.edit != null ? "edit_beneficiary".tr : "add_beneficiary".tr),
      ),
      body: Form(
        key: formKey,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: SingleChildScrollView(
            child: Column(
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: MyTextFieldwValidator(
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'account_name_required'.tr;
                      }
                      return null;
                    },
                    controller: accountNameController,
                    title: 'account_name'.tr,
                  ),
                ),
                SizedBox(height: 8.h),
                splitConfig.value == "Percentage"
                    ? Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0),
                        child: Row(
                          children: [
                            Text(
                              '${'split_config'.tr}',
                              style: TextStyle(
                                  fontSize: 14.spMin,
                                  fontWeight: FontWeight.w600),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Obx(
                                () => DropdownButton<String>(
                                  value: splitConfig.value,
                                  hint: Text('select_option'.tr),
                                  items: <String>['Percentage', 'Amount']
                                      .map<DropdownMenuItem<String>>(
                                          (String value) {
                                    return DropdownMenuItem<String>(
                                      value: value,
                                      child: Text(value == 'Percentage'
                                          ? 'percentage'.tr
                                          : 'amount'.tr),
                                    );
                                  }).toList(),
                                  onChanged: (String? newValue) {
                                    if (newValue != null) {
                                      splitConfig.value = newValue;

                                      percentageAmountController.clear();
                                    }
                                  },
                                ),
                              ),
                            ),
                            const Tooltip(
                              triggerMode: TooltipTriggerMode.tap,
                              message:
                                  'Split config determines how the funds will be divided. You can choose to split by Percentage or by a fixed Amount.',
                              child: Icon(Icons.info_outline, size: 18),
                            ),
                          ],
                        ),
                      )
                    : Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0),
                        child: Row(children: [
                          Text(
                            'amount'.tr,
                            style: TextStyle(
                                fontSize: 14.spMin,
                                // color: isLight.value ?  Colors.black : Colors.white,
                                fontWeight: FontWeight.w600),
                          ),
                        ])),
                Obx(
                  () => Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: splitConfig.value == "Percentage"
                        ? Obx(() {
                            print('maxPercentage : $maxPercentage');

                            return Slider(
                              value: percentage.value != null
                                  ? percentage.value!
                                  : controller.remainingPercentage.value,
                              min: 0,
                              max: maxPercentage.value,
                              divisions: 100,
                              label:
                                  '${(percentage.value != null ? percentage.value! : 0).round()}%',
                              onChanged: (double newValue) {
                                percentage.value = newValue;
                                amount = null;
                                percentageAmountController.text =
                                    newValue.toStringAsFixed(0);
                              },
                            );
                          })
                        : MyTextFieldwValidator(
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'amount_required'.tr;
                              }
                              return null;
                            },
                            maxLength:
                                splitConfig.value == 'Percentage' ? 3 : 7,
                            controller: percentageAmountController,
                            onChanged: (val) {
                              if (splitConfig.value == "Percentage") {
                                percentage.value =
                                    (double.tryParse(val) ?? 0) / 100;
                                amount = null;
                              } else {
                                percentage(null);
                                amount = double.tryParse(val) ?? 0;
                              }
                            },
                            hint: splitConfig.value,
                            keyboardType: TextInputType.number,
                          ),
                  ),
                ),
                Obx(
                  () => Container(
                      padding: const EdgeInsets.symmetric(
                          vertical: 6, horizontal: 8),
                      margin: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(15),
                        color: primaryColor.withOpacity(0.15),
                      ),
                      child: Row(
                          children: List.generate(
                              paymentChannel.paymentGateways().length, (index) {
                        return Expanded(
                            child: GestureDetector(
                          onTap: () => {
                            clearText(),
                            if (paymentChannel.paymentGateways()[index] ==
                                "Mobile")
                              {
                                transferMode = 'WALLET',
                                controller.channelName.value = 'M-PESA',
                                controller.channel = 63902,
                                controller.page.value = 0,
                                pageController.jumpToPage(0),
                              },
                            if (paymentChannel.paymentGateways()[index] ==
                                "Paybill")
                              {
                                transferMode = 'PAYBILL',
                                controller.channelName.value = 'M-PESA',
                                controller.channel = 63902,
                                controller.page.value = 1,
                                pageController.jumpToPage(1),
                              },
                            if (paymentChannel.paymentGateways()[index] ==
                                "Till")
                              {
                                transferMode = 'TILL',
                                controller.channelName.value = 'M-PESA',
                                controller.channel = 63902,
                                controller.page.value = 2,
                                pageController.jumpToPage(2),
                              },
                            if (paymentChannel.paymentGateways()[index] ==
                                "Bank")
                              {
                                transferMode = 'BANK',
                                controller.channelName.value = 'BANK',
                                controller.page.value = 3,
                                pageController.jumpToPage(3)
                              }
                          },
                          child: Container(
                            padding: const EdgeInsets.all(8),
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(7.5),
                              color: controller.page.value == index
                                  ? Colors.white
                                  : null,
                            ),
                            child: Text(paymentChannel.paymentGateways()[index],
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  fontSize: 14.spMin,
                                  color: controller.page.value == index
                                      ? primaryColor
                                      : null,
                                )),
                          ),
                        ));
                      }))),
                ),
                const SizedBox(height: 16),
                Padding(
                  padding: EdgeInsets.all(8.0.spMin),
                  child: Obx(
                    () => SizedBox(
                      height: controller.page.value == 1 ||
                              controller.page.value == 3
                          ? 195.h
                          : controller.page.value == 0
                              ? 100.h
                              : 90.h,
                      width: 520,
                      child: Obx(() {
                        final supportedChannels = paymentChannel.paymentChannels.value.where((e) => e.typeIn != false).toList();
                        return PageView(
                          controller: pageController,
                          onPageChanged: (page) => {
                            controller.page(page),
                            if (firstTime) {clearText()},
                            firstTime = true
                          },
                          children: [
                            paymentChannel.supportsMobile.value
                                ? Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceEvenly,
                                    children: List.generate(
                                        
                                           supportedChannels.length, (index) {
                                      return GestureDetector(
                                          onTap: () {
                                            controller.setChannel(
                                                supportedChannels[index].name, supportedChannels[index].channelCode);
                                          },
                                          child: Column(
                                            children: [
                                              supportedChannels[index].name == "Card" ? 
                                              const PaymentKensbuns()
                                               :
                                              Image.asset(
                                                supportedChannels[index].imageUrl,
                                                width: 50.w,
                                                height: 50.h,
                                              ),
                                              Radio(
                                                value: supportedChannels[index].name,
                                                groupValue: controller
                                                    .channelName.value,
                                                onChanged: (value) {
                                                  controller.setChannel(
                                                      value.toString(), supportedChannels[index].channelCode);
                                                },
                                              ),
                                            ],
                                          ));
                                    }))
                                : const SizedBox(),
                            paymentChannel.supportsPaybill.value
                                ? Column(
                                    children: [
                                      MyTextFieldwValidator(
                                        keyboardType: TextInputType.text,
                                        controller: accountNumber,
                                        title: 'mpesa_paybill'.tr,
                                        validator: (value) {
                                          if (value == null || value.isEmpty) {
                                            return 'paybill_required'.tr;
                                          }
                                          // Allow alphanumeric characters for paybill
                                          if (!RegExp(r'^[a-zA-Z0-9]+$')
                                              .hasMatch(value)) {
                                            return 'paybill_validation'.tr;
                                          }
                                          return null;
                                        },
                                      ),
                                      SizedBox(height: 10.h),
                                      MyTextFieldwValidator(
                                        keyboardType: TextInputType.text,
                                        controller: accountRef,
                                        title: 'account_number'.tr,
                                        validator: (value) {
                                          if (value == null || value.isEmpty) {
                                            return 'account_number_required'.tr;
                                          }
                                          // Allow alphanumeric characters for account numbers
                                          if (!RegExp(r'^[a-zA-Z0-9]+$')
                                              .hasMatch(value)) {
                                            return 'account_number_validation'
                                                .tr;
                                          }
                                          return null;
                                        },
                                      )
                                    ],
                                  )
                                : const SizedBox(),
                            paymentChannel.supportsTill.value
                                ? Column(
                                    children: [
                                      MyTextFieldwValidator(
                                        onChanged: (_) {
                                          accountRef.text = "";
                                        },
                                        validator: (value) {
                                          if (value == null || value.isEmpty) {
                                            return 'till_number_required'.tr;
                                          }
                                          return null;
                                        },
                                        controller: accountNumber,
                                        keyboardType: TextInputType.number,
                                        title: 'mpesa_till_number'.tr,
                                      ),
                                    ],
                                  )
                                : const SizedBox(),
                            paymentChannel.supportBanks.value
                                ? Obx(
                                    () => Column(
                                      children: [
                                        Align(
                                          alignment: Alignment.topLeft,
                                          child: Text(
                                            'select_bank'.tr,
                                            style: TextStyle(
                                                fontSize: 14.spMin,
                                                fontWeight: FontWeight.w600),
                                          ),
                                        ),
                                        GestureDetector(
                                            onTap: () {
                                              showModalBottomSheet(
                                                  context: context,
                                                  isScrollControlled: true,
                                                  builder: (_) {
                                                    return DraggableScrollableSheet(
                                                        maxChildSize: 0.97,
                                                        initialChildSize: 0.7,
                                                        expand: false,
                                                        builder: (context,
                                                            scrollController) {
                                                          final GlobalControllers
                                                              globalController =
                                                              Get.find<
                                                                  GlobalControllers>();
                                                          final RxString
                                                              searchText =
                                                              ''.obs;

                                                          final banksList =
                                                              globalController
                                                                  .paymentChannels
                                                                  .where((e) =>
                                                                      e.category ==
                                                                      pm.Category
                                                                          .BANK)
                                                                  .toList();

                                                          return Padding(
                                                            padding:
                                                                const EdgeInsets
                                                                    .all(12.0),
                                                            child: Column(
                                                              children: [
                                                                Padding(
                                                                  padding:
                                                                      const EdgeInsets
                                                                          .all(
                                                                          8.0),
                                                                  child:
                                                                      CupertinoSearchTextField(
                                                                    onChanged:
                                                                        (value) {
                                                                      searchText
                                                                              .value =
                                                                          value;
                                                                    },
                                                                  ),
                                                                ),
                                                                Expanded(
                                                                  child:
                                                                      Obx(() {
                                                                    final filteredBanks =
                                                                        banksList
                                                                            .where((bank) {
                                                                      return bank
                                                                          .name
                                                                          .toLowerCase()
                                                                          .contains(searchText
                                                                              .value
                                                                              .toLowerCase());
                                                                    }).toList();

                                                                    return ListView
                                                                        .builder(
                                                                            itemCount:
                                                                                filteredBanks.length,
                                                                            itemBuilder: (context, index) {
                                                                              return GestureDetector(
                                                                                  onTap: () {
                                                                                    controller.selectedBank.value = filteredBanks[index];
                                                                                    accountRef.text = "";
                                                                                    controller.channelName.value = 'BANK';
                                                                                    controller.channel = filteredBanks[index].channelCode;
                                                                                    Navigator.of(context).pop();
                                                                                  },
                                                                                  child: ListTile(
                                                                                    leading: ShowCachedNetworkImage(
                                                                                      imageurl: filteredBanks[index].imageUrl,
                                                                                      height: 30,
                                                                                      width: 30,
                                                                                      errorWidget: const Icon(Icons.account_balance),
                                                                                    ),
                                                                                    title: Text(filteredBanks[index].name),
                                                                                  ));
                                                                            });
                                                                  }),
                                                                ),
                                                              ],
                                                            ),
                                                          );
                                                        });
                                                  });
                                            },
                                            child: Container(
                                                height: 60.h,
                                                padding:
                                                    const EdgeInsets.all(8),
                                                alignment: Alignment.center,
                                                width: 390.w,
                                                decoration: BoxDecoration(
                                                  border: Border.all(
                                                      color: Colors.grey),
                                                  borderRadius:
                                                      BorderRadius.circular(8),
                                                ),
                                                child: Obx(
                                                  () => ListTile(
                                                    leading:
                                                        ShowCachedNetworkImage(
                                                      imageurl: controller
                                                              .selectedBank
                                                              .value
                                                              ?.imageUrl ??
                                                          '',
                                                      height: 30,
                                                      width: 30,
                                                      errorWidget: const Icon(
                                                          Icons
                                                              .account_balance),
                                                    ),
                                                    title: Text(controller
                                                            .selectedBank
                                                            .value
                                                            ?.name ??
                                                        ''),
                                                  ),
                                                ))),
                                        SizedBox(height: 12.h),
                                        if (controller.selectedBank.value !=
                                            null)
                                          MyTextFieldwValidator(
                                            validator: (value) {
                                              if (value == null ||
                                                  value.isEmpty) {
                                                return 'bank_account_required'
                                                    .tr;
                                              }
                                              return null;
                                            },
                                            controller: accountNumber,
                                            title: 'bank_account'.tr,
                                            onChanged: (val) {
                                              accountRef.text = "";
                                            },
                                          )
                                      ],
                                    ),
                                  )
                                : const SizedBox(),
                          ],
                        );
                      }),
                    ),
                  ),
                ),
                SizedBox(height: 8.h),
                Obx(
                  () => controller.page.value == 0
                      ? Column(
                          children: [
                            Align(
                              alignment: Alignment.topLeft,
                              child: Text(
                                'enter_phone_number'.tr,
                                style: TextStyle(
                                    fontSize: 14.spMin,
                                    color: isLight.value
                                        ? Colors.black
                                        : Colors.white,
                                    fontWeight: FontWeight.w600),
                              ),
                            ),
                            SizedBox(height: 8.h),
                            CustomInternationalPhoneInput(
                              onInputChanged: (PhoneNumber no) {
                                phoneNumber.text = no.phoneNumber
                                    .toString()
                                    .replaceAll("+", '');
                              }, 
                              
                              initialPhoneNumber:number.phoneNumber,
                              validator: (value) {
                                if (value == null ||
                                    value.isEmpty ||
                                    value.length < 9) {
                                  return 'phone_number_required_validation'.tr;
                                }
                                return null;
                              },

                            ),
                          ],
                        )
                      : const SizedBox(),
                ),
                Obx(
                  () => CheckboxListTile(
                    contentPadding: const EdgeInsets.all(2),
                    value: includeEndDate.value,
                    onChanged: (val) {
                      includeEndDate.value = val ?? false;
                    },
                    title: Text('include_end_date'.tr),
                  ),
                ),
                Obx(() => includeEndDate.value
                    ? Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0),
                        child: MyTextFieldwValidator(
                          title: 'end_date'.tr,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'end_date_required'.tr;
                            }
                            return null;
                          },
                          controller: endDateController,
                          hint: 'click_select_end_date'.tr,
                          readOnly: true,
                          onTap: () async {
                            DateTime? pickedDateTime = await showDatePicker(
                              context: context,
                              initialDate: DateTime.now().toLocal(),
                              firstDate: DateTime.now().toLocal(),
                              lastDate:
                                  DateTime.now().add(const Duration(days: 365)),
                            );

                            if (pickedDateTime != null) {
                              TimeOfDay? pickedTime = await showTimePicker(
                                context: context,
                                initialTime: TimeOfDay.now(),
                              );

                              if (pickedTime != null) {
                                DateTime finalDateTime = DateTime(
                                  pickedDateTime.year,
                                  pickedDateTime.month,
                                  pickedDateTime.day,
                                  pickedTime.hour,
                                  pickedTime.minute,
                                );

                                String formattedDateTime =
                                    DateFormat('d MMM yyyy HH : mm a')
                                        .format(finalDateTime);
                                endDateController.text = formattedDateTime;
                              }
                            }
                          },
                        ),
                      )
                    : const SizedBox()),
                const SizedBox(height: 200)
              ],
            ),
          ),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      floatingActionButton: Obx(
        () => Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 4),
          child: MyButton(
            showLoading: controller.isAddingBeneficiaries.value,
            onClick: () async {
              if (formKey.currentState!.validate()) {
                if (transferMode != "PAYBILL") {
                  accountRef.clear();
                }
                if (widget.edit == null) {
                  print('endDate : ${includeEndDate.value}');
                  if (await controller.addBeneficiaries({
                    "account_name": accountNameController.text,
                    "percentage": percentage.value == null
                        ? null
                        : (percentage.value! / 100),
                    "user_id": null,
                    "kitty_id": widget.kittyId,
                    "transfer_mode": transferMode,
                    "amount": amount,
                    "channel_name": controller.channelName.value,
                    "channel": controller.channel,
                    "status": "ACTIVE",
                    "account_number": transferMode == "WALLET"
                        ? phoneNumber.text
                        : accountNumber.text,
                    "account_number_ref": accountRef.text,
                    "split_config": splitConfig.value.toUpperCase(),
                    "phone_number": phoneNumber.text,
                    "role": "SECONDARY",
                    "end_date": includeEndDate.value
                        ? DateFormat('d MMM yyyy HH : mm a')
                            .parse(endDateController.text)
                            .toUtc()
                            .toIso8601String()
                        : null
                  }, widget.kittyId)) {
                    
                    Get.back();
                  }
                } else {
                  print('endDate : ${includeEndDate.value}');
                  if (await controller.updateBeneficiary({
                    "ID": widget.edit!.id,
                    // "CreatedAt": widget.edit!.createdAt.toUtc().toIso8601String(),
                    // "UpdatedAt": DateTime.now().toUtc().toIso8601String(),
                    "DeletedAt": null,
                    "account_name": accountNameController.text,
                    "percentage": percentage.value == null
                        ? null
                        : (percentage.value! / 100),
                    "user_id": null,
                    "kitty_id": widget.kittyId,
                    "transfer_mode": transferMode,
                    "amount": amount,
                    "channel_name": controller.channelName.value,
                    "channel": controller.channel,
                    "status": "ACTIVE",
                    "account_number": transferMode == "WALLET"
                        ? phoneNumber.text
                        : accountNumber.text,
                    "account_number_ref": accountRef.text,
                    "split_config": splitConfig.value.toUpperCase(),
                    "phone_number": phoneNumber.text,
                    "role": widget.edit!.role,
                    "end_date": includeEndDate.value
                        ? DateFormat('d MMM yyyy HH : mm a')
                            .parse(endDateController.text)
                            .toUtc()
                            .toIso8601String()
                        : null
                  }, widget.kittyId)) { 
                    Get.back();
                  }
                }
              }
            },
            label: widget.edit != null
                ? "edit_beneficiary".tr
                : "add_beneficiary".tr,
          ),
        ),
      ),
    );
  }
}
