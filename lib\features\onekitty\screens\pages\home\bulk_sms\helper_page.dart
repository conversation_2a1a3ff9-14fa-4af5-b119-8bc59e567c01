import 'dart:io';
import 'dart:typed_data';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart' show rootBundle;
import 'package:get/get_utils/get_utils.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:onekitty_admin/services/web_path_provider_service.dart';
import 'package:onekitty_admin/services/web_file_handler_service.dart';

class InfoPage extends StatelessWidget {
  const InfoPage({super.key});

  Future<void> _saveSampleFile(BuildContext context) async {
    try {
      // Check platform and Android version for permissions
      if (Platform.isAndroid) {
        // For Android versions below 10 (API 29), request storage permission
        final androidInfo = await DeviceInfoPlugin().androidInfo;
        if (androidInfo.version.sdkInt < 29) {
          var status = await Permission.storage.status;
          if (!status.isGranted) {
            status = await Permission.storage.request();
            if (!status.isGranted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                    content: Text('Storage permission is required')),
              );
              return;
            }
          }
        }
      }

      // Load the asset file
      final byteData =
          await rootBundle.load('assets/resources/contacts_sample.xlsx');
      final bytes = byteData.buffer
          .asUint8List(byteData.offsetInBytes, byteData.lengthInBytes);

      if (kIsWeb) {
        // On web, trigger download
        WebFileHandlerService.downloadExcelFile(bytes, 'contacts_sample.xlsx');
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('File download started')),
        );
      } else {
        // On mobile, save to downloads directory
        final directory = await WebPathProviderService.getDownloadsDirectory();
        if (directory == null) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Could not access Downloads folder')),
          );
          return;
        }

        // Define the file path
        final filePath = '${directory.path}/contacts_sample.xlsx';
        final file = File(filePath);

        // Write the file
        await file.writeAsBytes(bytes);

        // Notify user
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('File saved to ${file.path}')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('contacts_excel_info'.tr),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
           
               Text(
              'how_to_create_excel_sheet_contacts'.tr,
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 16),
            // Explanation / instructions
            Text(
              'Follow these steps to prepare your contacts Excel sheet:\n'
              '1. Open your preferred spreadsheet application (e.g., Excel, Google Sheets).\n'
              '2. In the first row of the spreadsheet, set up the column headers as follows:\n'
              '   - Column A: First Name\n'
              '   - Column B: Second Name\n'
              '   - Column C: Phone Number (append \' mark before the phoneNumber) \n'
              '3. Enter each contact’s information in the rows below these headers.\n'
              '4. Save the file as an .xlsx format to ensure compatibility.\n'
              '\n'
              'You can download a sample Excel file with the correct structure by tapping the button below:',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 24),
           
            Center(
              child: ElevatedButton.icon(
                onPressed: () => _saveSampleFile(context),
                icon: const Icon(Icons.download),
                label: Text('save_sample_excel_downloads'.tr),
              ),
            ),
          ],
        ),
      ),
    );
  }
}