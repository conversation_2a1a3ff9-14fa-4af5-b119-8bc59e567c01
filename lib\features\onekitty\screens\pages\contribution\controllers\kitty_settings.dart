// settings_controller.dart
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart'; 
import 'package:onekitty_admin/models/kitty_settings_model.dart';
import 'package:onekitty_admin/services/api_urls.dart';
import 'package:onekitty_admin/services/custom_logger.dart';
import 'package:onekitty_admin/services/http_service.dart';

class SettingsController extends GetxController {
  final logger = Logger(filter: CustomLogFilter());
  final HttpService apiProvider = Get.find();
  Rx<KittySettings> settings = KittySettings().obs;
  final hideAmount = false.obs;
  final hideNames = false.obs;
  final isSubmitting = false.obs;
  Future deactivateKitty(Map<String, dynamic> params) async {
    try {
      final response = await apiProvider.request(
         
          method: Method.POST, url: ApiUrls.deactivateKitty, params: params);
      if (response.data['status'] ?? false) {
        Get.snackbar('Success', response.data['message'] ?? 'Kitty deactivated',
            backgroundColor: Colors.green);
      } else {
        Get.snackbar('Error', response.data['message'] ?? 'An error occured',
            backgroundColor: Colors.red);
      }
    } catch (e) {
      Get.snackbar('Error', 'an error has occured', backgroundColor: Colors.amber);
      logger.e(e);
    }
  }
final isFetchingSettings = false.obs;
  Future fetchSettings(int kittyId) async {

    try {
      isFetchingSettings(true);
      final response = await apiProvider.request(
          method: Method.GET,
          url: "${ApiUrls.kitty_settings}?kitty_id=$kittyId",
          params: {
            "kitty_id": kittyId,
          });
      if (response.data['status'] ?? false) {
        settings(
            KittySettings.fromJson(response.data['data']['kitty_settings']));
      }else{
        // ToastUtils.showErrorToast(Get.context!, response.data['message'] ?? 'An error occured', 'Error');
      }
    } catch (e) {
      Get.snackbar('Error', 'an error has occured', backgroundColor: Colors.red);
      logger.e(e);
    }finally{ 
      isFetchingSettings(false);
    }
  }

  Future<bool> submitSettings(Map<String, dynamic> payload) async {
    isSubmitting(true);
    try {
      var response = await apiProvider.request(
          method: Method.POST,
           
          url: ApiUrls.update_kitty_settings,
          params: payload);
      if (response.data['status'] ?? false) {
        fetchSettings(payload['kitty_id']);
        Get.snackbar('Success', response.data['message'] ?? 'Settings updated successfully',
            backgroundColor: Colors.green);
        return true;
      } else {
      Get.snackbar('Error', '${response.data['message']}', backgroundColor: Colors.red);  }
      return false;
    } catch (e) {
      Get.snackbar('Error', '$e', backgroundColor: Colors.amber);      
      logger.e(e);
      return false;
    } finally {
      isSubmitting(false);
    }
  }
}
