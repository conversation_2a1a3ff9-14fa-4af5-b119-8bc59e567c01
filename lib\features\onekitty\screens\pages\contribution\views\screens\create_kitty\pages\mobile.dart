// ignore_for_file: must_be_immutable
import 'package:fl_country_code_picker/fl_country_code_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:onekitty_admin/configs/country_specifics.dart';
import 'package:onekitty_admin/utils/payment_kensbuns.dart';
import 'package:onekitty_admin/features/onekitty/widgets/custom_international_phone_input.dart';
import 'package:onekitty_admin/helpers/extensions/text_styles.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/widgets/date_picker.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/widgets/row_widget.dart';
import 'package:onekitty_admin/features/onekitty/screens/widgets/payment_radio.dart';
import 'package:onekitty_admin/utils/common_strings.dart';

class MobileMobile extends StatefulWidget {
  final String benPhone;
  final TextEditingController date;
  final TextEditingController time;
  final TextEditingController phoneController;
  final Function(PhoneNumber)? onInputChanged;
  final PaymentChannelsBuilder paymentChannelsBuilder;
  const MobileMobile(
      {super.key,
      this.benPhone = "",
      required this.date,
      required this.time,
      required this.paymentChannelsBuilder,
      required this.onInputChanged,
      required this.phoneController});

  @override
  State<MobileMobile> createState() => _MobileMobileState();
}

class _MobileMobileState extends State<MobileMobile> {
  int selectedValue = 0;
  final countryPicker = const FlCountryCodePicker();
  CountryCode? countryCode;
  PhoneNumber num = CountryConfig.phoneNumber;

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          widget.paymentChannelsBuilder,
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Text("beneficiary_phone_number".tr,
                style: context.titleText
                    ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15)),
          ),
          CustomInternationalPhoneInput(
            onInputChanged: (PhoneNumber number) {
              if (widget.onInputChanged != null) {
                widget.onInputChanged!(number);
              }
            }, 
            controller: widget.phoneController, 
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'phone_number_is_required'.tr;
              }
              return null;
            },
          ),
          Text("who_should_receive_contribution".tr),
          SingleLineRow(
            text: "Expected contribution end date",
            popup: KtStrings.endDateInfo,
          ),
          DatePicker(
            date: widget.date,
            time: widget.time,
            isAllow: true,
          ),
        ],
      ),
    );
  }

  Widget buildRadio(String image, String text, int value) {
    return Column(
      children: [
        Radio(
            value: value,
            groupValue: selectedValue,
            onChanged: (value) {
              setState(() {
                selectedValue = 1;
              });
            }),
            value == 55 ?
        Image.asset(image) : const PaymentKensbuns(),
        Text(text)
      ],
    );
  }
}
