import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/controllers/user_ktty_controller.dart';
import 'package:onekitty_admin/helpers/colors.dart';
import 'package:onekitty_admin/models/kitty/beneficiary_model.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/views/screens/contribution_kitties/create_beneficiary.dart';
import 'package:onekitty_admin/utils/formatted_currency.dart';
import 'package:onekitty_admin/utils/my_button.dart';
import 'package:onekitty_admin/utils/timeSince.dart';

class ViewSingleBeneficiary extends StatelessWidget {
  final BeneficiaryModel beneficiary;
  const ViewSingleBeneficiary({super.key, required this.beneficiary});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('beneficiary_details'.tr),
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0.sp),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 16.sp),
            Text(
              '${'beneficiary_name'.tr}: ${beneficiary.accountName}',
              style: TextStyle(fontSize: 20.sp, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 10.sp),
            Text(
              '${'account_number'.tr}: ${beneficiary.accountNumber}',
              style: TextStyle(fontSize: 16.sp),
            ),
            SizedBox(height: 10.sp),
            Text(
              '${'transfer_mode'.tr} ${beneficiary.transferMode} - ${beneficiary.channelName}',
              style: TextStyle(fontSize: 16.sp),
            ),
            SizedBox(height: 10.sp),
            if (beneficiary.transferMode == "PAYBILL")
              Text(
                '${'account_number_reference'.tr}: ${beneficiary.accountNumberRef}',
                style: TextStyle(fontSize: 16.sp),
              ),
            SizedBox(height: 10.sp),
            if (beneficiary.endDate != null)
              Text.rich(TextSpan(children: [
                TextSpan(
                    text:
                        '${'end_date'.tr} ${DateFormat('d MMM HH:MM a').format(beneficiary.endDate!)}'),
                TextSpan(
                    style: const TextStyle(
                        // fontSize: 12.spMin,
                        ),
                    text:
                        ' - ${highPrecisiontimeSince(beneficiary.endDate!.toLocal())}')
              ])),
            SizedBox(height: 10.sp),
            Row(
              children: [
                Text("${beneficiary.splitConfig?.toLowerCase() ?? ''}: "),
                beneficiary.splitConfig == "AMOUNT"
                    ? Text(
                        FormattedCurrency.getFormattedCurrency(beneficiary.amount ?? 0),
                      )
                    : SizedBox(
                        width: 60.w,
                        height: 60.h,
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            CircularProgressIndicator(
                              value: beneficiary.percentage?.toDouble(),
                              strokeWidth: 4,
                              backgroundColor: Colors.transparent,
                              valueColor: const AlwaysStoppedAnimation<Color>(
                                  AppColors.primary),
                            ),
                            Text(
                              '${((beneficiary.percentage ?? 0) * 100).toStringAsFixed(1)}%',
                              style: TextStyle(fontSize: 8.spMin),
                            ),
                          ],
                        ),
                      ),
              ],
            ),
            SizedBox(height: 20.sp),
            MyButton(
              onClick: () async { 
                await Get.to(() => CreateBeneficiary(
                    kittyId:
                        Get.find<DataController>().kitty.value.kitty?.iD ?? 0,
                    edit: beneficiary))?.whenComplete(Get.back);
              },
              label: 'edit'.tr,
            ),
          ],
        ),
      ),
    );
  }
}
