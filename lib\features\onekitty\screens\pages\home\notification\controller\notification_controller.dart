// notification_controller.dart
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/controllers/events_controller.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/home/<USER>/model/notification_model.dart';
import 'package:onekitty_admin/services/api_urls.dart';
import 'package:onekitty_admin/services/http_service.dart';

class NotificationController extends GetxController {
  final RxList<AppNotification> notifications = <AppNotification>[].obs;
  RxBool isLoadingTypes = false.obs;
  RxBool isLoading = false.obs;
  final selectedFilter = 'all'.obs;
  final RxList<String> notificationTypes = <String>[].obs;
  final Rx<DateTime?> startDate = Rx<DateTime?>(null);
  final Rx<DateTime?> endDate = Rx<DateTime?>(null);
  final Rx<String?> readFilter =
      Rx<String?>(null); // null = all, 'read', 'unread'
  RxBool isApplyingFilters = false.obs;
  final page = 0.obs;
  final maxPage = 0.obs;
  RxBool isLoadingMore = false.obs;
  final _loadedIds = <String>{};

  @override
  void onInit() {
    super.onInit();
    loadStoredNotifications();
    getNotificationTypes();
  }

  @override
  void onReady() {
    super.onReady();
    // Add both initial loads here
    getNotificationTypes();
    loadStoredNotifications(refresh: true);
  }

  void markAsRead(String id) {
    final index = notifications.indexWhere((n) => n.id == id);
    if (index != -1) {
      final updated = notifications[index].copyWith(read: true);
      notifications[index] = updated;
    }
  }

  void addNotification(AppNotification notification) {
    notifications.insert(0, notification);
    _saveNotifications();
  }

  void _saveNotifications() {
    // ignore: unused_local_variable
    final notificationsList = notifications.map((n) => n.toJson()).toList();
  }

  final HttpService apiProvider = Get.find();
  final logger = Get.find<Logger>();
  RxString apiMessage = ''.obs;
  RxString checkoutId = ''.obs;

  final data = ''.obs;

  final type = ''.obs;
  final Rx<bool?> isRead = Rx<bool?>(null);

  Future<void> loadStoredNotifications({bool refresh = false}) async {
    try {
      if (refresh) {
        page.value = 0;
        notifications.clear();
        _loadedIds.clear();
        maxPage.value = 0;
      }

      if (page.value > maxPage.value && maxPage.value != 0) return;
      if (!refresh && (isLoading.value || isLoadingMore.value)) return;

      isLoading.value = !isApplyingFilters.value && notifications.isEmpty;
      isLoadingMore.value =
          !isApplyingFilters.value && notifications.isNotEmpty;

      // Always include is_read parameter in the API call
      String readFilterValue = readFilter.value == 'read' ? 'true' : 
                             readFilter.value == 'unread' ? 'false' : '';

      var res = await apiProvider.request(
        url:
            "${ApiUrls.getNotifications}?size=10&page=${page.value}&user_id=${Get.find<Eventcontroller>().getLocalUser()?.id}&type=${selectedFilter.value == 'all' ? '' : selectedFilter.value}${readFilterValue == '' ? "" : "&is_read=$readFilterValue"}&start_date=${startDate.value?.toUtc().toIso8601String() ?? ''}&end_date=${endDate.value?.toUtc().toIso8601String() ?? ''}",
        method: Method.GET,
      );

      if (res.data != null) {
        final newNotifications = (res.data['data']['items'] as List)
            .map<AppNotification>((json) => AppNotification.fromJson(json))
            .where((notification) => !_loadedIds.contains(notification.id))
            .toList();

        _loadedIds.addAll(newNotifications.map((n) => n.id));

        notifications.addAll(newNotifications);

        page.value = res.data['data']['page'] + 1;
        maxPage.value = res.data['data']['total_pages'];

        if (newNotifications.isEmpty && !refresh) {
          page.value = maxPage.value + 1;
        }
      }
    } catch (e) {
      logger.e('Error loading notifications: $e');
    } finally {
      isLoading.value = false;
      isLoadingMore.value = false;
      isApplyingFilters.value = false;
    }
  }

  final isFilterVisible = false.obs;

  void toggleFilterVisibility() {
    isFilterVisible.value = !isFilterVisible.value;
  }

  Future<void> getNotificationTypes() async {
    isLoadingTypes.value = true;
    try {
      var res = await apiProvider.request(
        url: ApiUrls.getNotificationsTypes,
        method: Method.GET,
      );
      if (res.data != null) {
        // Convert the response data to a list of strings
        final List<dynamic> types = res.data['data']['types'] ?? [];
        notificationTypes.value = types.map((type) => type.toString()).toList();
      }
    } catch (e) {
      logger.e('Error fetching notification types: $e');
    } finally {
      isLoadingTypes.value = false;
    }
  }

  Future<void> updateNotificationReadStatus(List<int> ids) async {
    try {
      await apiProvider.request(
          url: ApiUrls.updateReadNotification,
          method: Method.PUT,
          params: {"ids": ids});
      loadStoredNotifications(); // Refresh notifications after update
    } catch (e) {
      logger.e('Error updating notification status: $e');
    }
  }

  void deleteNotification(String id) async {

    notifications.removeWhere((n) => n.id == id);
    try{
    await apiProvider.request(url: "${ApiUrls.deleteNotification}$id", method: Method.DELETE, );
    }catch(e){
     logger.e('Error updating notification status: $e'); 
    }
    _saveNotifications();
  }

  bool hasUnreadNotifications() {
    return notifications.any((notification) => !notification.read);
  }

  void clearAllNotifications() {
    notifications.clear();
    _saveNotifications();
  }

  void markAllAsRead() {
    List<int> ids = [];
    for (var i = 0; i < notifications.length; i++) {
      if (!notifications[i].read) {        
       ids.add(int.tryParse(notifications[i].id) ?? 0);
        notifications[i] = notifications[i].copyWith(read: true);
      }
    }
     updateNotificationReadStatus(ids);
    _saveNotifications();
  }

  void setSelectedFilter(String filter) {
    selectedFilter.value = filter;
  }

  void setDateRange(DateTime? start, DateTime? end) {
    startDate.value = start;
    endDate.value = end;
  }

  void setReadFilter(String? filter) {
    readFilter.value = filter;
  }

  void clearFilters() {
    selectedFilter.value = 'all';
    startDate.value = null;
    endDate.value = null;
    readFilter.value = null;
  }

  bool get hasActiveFilters {
    return selectedFilter.value != 'all' ||
        startDate.value != null ||
        endDate.value != null ||
        readFilter.value != null;
  }

  Future<void> applyFilters() async {
    isApplyingFilters.value = true;
    await loadStoredNotifications(refresh: true);
  }

  @override
  void onClose() {
    _loadedIds.clear();
    super.onClose();
  }
}
