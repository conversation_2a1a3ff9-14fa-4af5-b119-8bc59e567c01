// notification_model.dart
import 'package:flutter/material.dart';
// notification_importance.dart
enum ImportanceLevel { low, medium, high, critical }

enum NotificationActionType {
  view_transactions,
  view_kitty
}

enum NotificationType {
  transaction_out,
  delegate_added
}

class NotificationConfig {
  final List<NotificationActionType> actions;
  final List<NotificationType> types;

  NotificationConfig({
    required this.actions,
    required this.types,
  });

  factory NotificationConfig.fromJson(Map<String, dynamic> json) {
    return NotificationConfig(
      actions: (json['actions'] as List)
          .map((action) => NotificationActionType.values.firstWhere(
              (e) => e.toString() == 'NotificationActionType.$action'))
          .toList(),
      types: (json['types'] as List)
          .map((type) => NotificationType.values.firstWhere(
              (e) => e.toString() == 'NotificationType.$type'))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'actions': actions.map((e) => e.toString().split('.').last).toList(),
      'types': types.map((e) => e.toString().split('.').last).toList(),
    };
  }
}

class NotificationImportance {
  final ImportanceLevel level;
  final int priority;

  NotificationImportance({
    required this.level,
    required this.priority,
  });
}

class NotificationAction {
  final String title;
  final VoidCallback onPressed;

  NotificationAction({required this.title, required this.onPressed});
}

class AppNotification {
  final String id;
  final DateTime created;
  final DateTime received;
  final String title;
  final String subtitle;
  final List<NotificationAction> actions;
  final bool read;
  final IconData? leadingIcon;
  final NotificationImportance importance;
  // New fields
  final String? phoneNumber;
  final int? userId;
  final bool? isAdminUser;
  final String? description;
  final String? image;
  final String? action;
  final String? type;
  final int? kittyId;

  AppNotification({
    required this.id,
    required this.created,
    required this.received,
    required this.title,
    required this.subtitle,
    required this.actions,
    required this.read,
    this.leadingIcon,
    required this.importance,
    this.phoneNumber,
    this.userId,
    this.isAdminUser,
    this.description,
    this.image,
    this.action,
    this.type,
    this.kittyId,
  });

  AppNotification copyWith({
    String? id,
    DateTime? created,
    DateTime? received,
    String? title,
    String? subtitle,
    List<NotificationAction>? actions,
    bool? read,
    // IconData? leadingIcon,
    NotificationImportance? importance,
    String? phoneNumber,
    int? userId,
    bool? isAdminUser,
    String? description,
    String? image,
    String? action,
    String? type,
    int? kittyId,
  }) {
    return AppNotification(
      id: id ?? this.id,
      created: created ?? this.created,
      received: received ?? this.received,
      title: title ?? this.title,
      subtitle: subtitle ?? this.subtitle,
      actions: actions ?? this.actions,
      read: read ?? this.read,
      // // // leadingIcon: leadingIcon ?? this.leadingIcon,
      importance: importance ?? this.importance,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      userId: userId ?? this.userId,
      isAdminUser: isAdminUser ?? this.isAdminUser,
      description: description ?? this.description,
      image: image ?? this.image,
      action: action ?? this.action,
      type: type ?? this.type,
      kittyId: kittyId ?? this.kittyId,
    );
  }

  factory AppNotification.fromJson(Map<String, dynamic> json) {
    return AppNotification(
      id: json['ID']?.toString() ?? json['id'],
      created: DateTime.parse(json['CreatedAt'] ?? json['created']),
      received: DateTime.parse(json['UpdatedAt'] ?? json['received']),
      title: json['title'],
      subtitle: json['description'] ?? json['subtitle'],
      actions: json['actions'] != null 
        ? (json['actions'] as List).map((action) => 
            NotificationAction(title: action['title'], onPressed: () {})).toList()
        : [],
      read: json['is_read'] ?? json['read'] ?? false,
      importance: json['importance'] != null 
        ? NotificationImportance(
            level: ImportanceLevel.values.byName(json['importance']['level']),
            priority: json['importance']['priority'],
          )
        : NotificationImportance(level: ImportanceLevel.low, priority: 0),
      phoneNumber: json['phone_number'],
      userId: json['user_id'],
      isAdminUser: json['is_admin_user'],
      description: json['description'],
      image: json['image'],
      action: json['action'],
      type: json['type'],
      kittyId: json['kitty_id'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'ID': int.tryParse(id),
      'CreatedAt': created.toIso8601String(),
      'UpdatedAt': received.toIso8601String(),
      'title': title,
      'description': description ?? subtitle,
      'actions': actions.map((action) => {'title': action.title}).toList(),
      'is_read': read,
      'importance': {
        'level': importance.level.name,
        'priority': importance.priority,
      },
      'phone_number': phoneNumber,
      'user_id': userId,
      'is_admin_user': isAdminUser,
      'image': image,
      'action': action,
      'type': type,
      'kitty_id': kittyId,
    };
  }
}