import 'package:flutter/material.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:onekitty_admin/configs/country_specifics.dart';
import 'package:onekitty_admin/configs/is_phone_valid.dart';
import 'package:onekitty_admin/utils/themes_colors.dart';

class CustomInternationalPhoneInput extends StatefulWidget {
  final String? label;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final Function(PhoneNumber)? onInputChanged;
  final Function(String)? onCountryCodeChanged;
  final String? errorText;
  final String? initialPhoneNumber;
  final Function(String)? onPhoneNumberSubmitted;
  final Widget? suffix;
  final Widget? prefix;
  const CustomInternationalPhoneInput({
    super.key,
    this.label,
    this.controller,
    this.validator,
    this.onInputChanged,
    this.onCountryCodeChanged,
    this.errorText,
    this.initialPhoneNumber,
    this.onPhoneNumberSubmitted,
    this.suffix,
    this.prefix,
  });

  @override
  State<CustomInternationalPhoneInput> createState() =>
      _CustomInternationalPhoneInputState();
}

class _CustomInternationalPhoneInputState
    extends State<CustomInternationalPhoneInput> {
  PhoneNumber? _currentPhoneNumber;
  late PhoneNumber _initialPhoneNumber;

  void _handlePhoneNumberSubmitted() {
    if (_currentPhoneNumber != null && widget.onPhoneNumberSubmitted != null) {
      // Format the phone number: remove + and any spaces/dashes
      String formattedNumber = _currentPhoneNumber!.phoneNumber ?? '';
      formattedNumber = formattedNumber.replaceAll(RegExp(r'[^\d]'), '');

      // Call the callback with the formatted number
      widget.onPhoneNumberSubmitted!(formattedNumber);
    }
  }

  late ValidationResult result;

  @override
  void initState() {
    super.initState();
    result = PhoneNumberValidator.isValidPhone(
      widget.initialPhoneNumber ?? '',
    );

    // Set up initial phone number
    if (result.isValid) {
      _initialPhoneNumber = PhoneNumber(
        phoneNumber: result.formattedNumber,
        isoCode: result.countryName,
        dialCode: '+${result.countryCode}',
      );
    } else {
      _initialPhoneNumber = PhoneNumber(
        isoCode: CountryConfig.isoCode,
        dialCode: CountryConfig.dialCode,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 600;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.label ?? "Input Phone Number",
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: isSmallScreen ? 6 : 8),
        Container(
          alignment: Alignment.centerLeft,
          padding: const EdgeInsets.only(left: 4, top: 4, bottom: 10,right: 4),
          decoration: ThemeHelper.inputBoxDecoration(),
          child: Stack(
            children: [
              InternationalPhoneNumberInput(
                onInputChanged: (PhoneNumber number) {
                  _currentPhoneNumber = number;
                  widget.controller?.text = number.phoneNumber?.replaceAll("+", "") ?? '';
                  widget.onInputChanged?.call(number);
                  widget.onCountryCodeChanged?.call(number.isoCode ?? '');
                },
                onInputValidated: (bool isValid) {
                  if (isValid) {
                    _handlePhoneNumberSubmitted();
                  }
                },
                selectorConfig: const SelectorConfig(
                  selectorType: PhoneInputSelectorType.BOTTOM_SHEET,
                ),
                ignoreBlank: false,
                validator: widget.validator,
                autoValidateMode: AutovalidateMode.disabled,
                selectorTextStyle: theme.textTheme.bodyLarge?.copyWith(
                  color: theme.colorScheme.onSurface,
                ),
                initialValue: _initialPhoneNumber,
                formatInput: true,
                keyboardType: const TextInputType.numberWithOptions(
                  signed: true,
                  decimal: true,
                ),
                inputBorder: InputBorder.none,
                inputDecoration: InputDecoration(
                  border: InputBorder.none,
                  enabledBorder: InputBorder.none,
                  focusedBorder: InputBorder.none,
                  hintText: 'Enter phone number',
                  contentPadding: EdgeInsets.only(
                    left: isSmallScreen ? 12 : 16,
                    right: widget.suffix != null ? 48 : (isSmallScreen ? 12 : 16),
                    top: isSmallScreen ? 4 : 8,
                    bottom: isSmallScreen ? 4 : 8,
                  ),
                  hintStyle: theme.textTheme.bodyLarge?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                  ),
                ),
                textStyle: theme.textTheme.bodyLarge?.copyWith(
                  color: theme.colorScheme.onSurface,
                ),
                textAlign: TextAlign.start,
                searchBoxDecoration: InputDecoration(
                  labelText: 'Search by country name or code',
                  hintText: 'Search...',
                  prefixIcon: Icon(
                    Icons.search,
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                  border: InputBorder.none,
                  enabledBorder: InputBorder.none,
                  focusedBorder: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: isSmallScreen ? 12 : 16,
                    vertical: isSmallScreen ? 12 : 16,
                  ),
                ),
              ),
              if (widget.suffix != null)
                Positioned(
                  right: 8,
                  top: 0,
                  bottom: 0,
                  child: Center(child: widget.suffix!),
                ),
              if (widget.prefix != null)
                Positioned(
                  left: 8,
                  top: 0,
                  bottom: 0,
                  child: Center(child: widget.prefix!),
                ),
            ],
          ),
        ),
        if (widget.errorText != null || widget.validator != null)
          Padding(
            padding: EdgeInsets.only(
              top: isSmallScreen ? 4 : 6,
              left: isSmallScreen ? 4 : 6,
            ),
            child: Text(
              widget.errorText ?? '',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.error,
                fontSize: isSmallScreen ? 11 : 12,
              ),
            ),
          ),
      ],
    );
  }
}
