import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get_utils/get_utils.dart';
import 'package:onekitty_admin/utils/my_text_field.dart';

class Socials extends StatelessWidget {
  final TextEditingController website, facebook, xlink, instagram, tiktok;

  const Socials({
    super.key,
    required this.website,
    required this.facebook,
    required this.xlink,
    required this.instagram,
    required this.tiktok,
  });

  /// Validates URL format
  String? _validateUrl(String? value, String platform) {
    if (value == null || value.trim().isEmpty) {
      return null; // Optional field
    }
    
    final url = value.trim();
  
    
    // Platform-specific validation
    final lowerUrl = url.toLowerCase();
    switch (platform.toLowerCase()) {
      case 'facebook':
        if (!lowerUrl.contains('facebook.com') && !lowerUrl.contains('fb.com')) {
          return 'enter_valid_facebook_url'.tr;
        }
        break;
      case 'x':
        if (!lowerUrl.contains('twitter.com') && !lowerUrl.contains('x.com')) {
          return 'enter_valid_x_url'.tr;
        }
        break;
      case 'instagram':
        if (!lowerUrl.contains('instagram.com')) {
          return 'enter_valid_instagram_url'.tr;
        }
        break;
      case 'tiktok':
        if (!lowerUrl.contains('tiktok.com')) {
          return 'enter_valid_tiktok_url'.tr;
        }
        break;
    }
    
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        MyTextFieldwValidator(
            controller: website,
            validator: (value) => _validateUrl(value, 'website'),
            titleStyle:
                TextStyle(fontSize: 14.spMin, fontWeight: FontWeight.w500),
            title: 'website_optional'.tr,
            hint: 'website_hint'.tr,
            keyboardType: TextInputType.url),
        SizedBox(height: 8.h),
        MyTextFieldwValidator(
            controller: facebook,
            validator: (value) => _validateUrl(value, 'facebook'),
            titleStyle:
                TextStyle(fontSize: 14.spMin, fontWeight: FontWeight.w500),
            title: 'facebook_link_optional'.tr,
            hint: 'facebook_hint'.tr,
            keyboardType: TextInputType.url),
        SizedBox(height: 8.h),
        MyTextFieldwValidator(
            controller: xlink,
            validator: (value) => _validateUrl(value, 'x'),
            titleStyle:
                TextStyle(fontSize: 14.spMin, fontWeight: FontWeight.w500),
            title: 'x_link_optional'.tr,
            hint: 'x_hint'.tr,
            keyboardType: TextInputType.url),
        SizedBox(height: 8.h),
        MyTextFieldwValidator(
            controller: instagram,
            validator: (value) => _validateUrl(value, 'instagram'),
            titleStyle:
                TextStyle(fontSize: 14.spMin, fontWeight: FontWeight.w500),
            title: 'instagram_link_optional'.tr,
            hint: 'instagram_hint'.tr,
            keyboardType: TextInputType.url),
        SizedBox(height: 8.h),
        MyTextFieldwValidator(
            controller: tiktok,
            validator: (value) => _validateUrl(value, 'tiktok'),
            titleStyle:
                TextStyle(fontSize: 14.spMin, fontWeight: FontWeight.w500),
            title: 'tiktok_link_optional'.tr,
            hint: 'tiktok_hint'.tr,
            keyboardType: TextInputType.url),
        SizedBox(height: 8.h),
      ]),
    );
  }
}
