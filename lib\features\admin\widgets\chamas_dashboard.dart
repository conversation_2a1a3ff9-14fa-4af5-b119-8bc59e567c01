import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onekitty_admin/features/admin/controllers/chamas_dashboard_controller.dart';
import 'package:onekitty_admin/features/admin/core/my_pluto_grid/my_pluto_grid.dart';
import 'package:onekitty_admin/features/admin/core/widgets/kitty_loading_widget.dart';
import 'package:onekitty_admin/features/admin/widgets/chamas_filter_widget.dart';

class ChamasDashboard extends StatefulWidget {
  const ChamasDashboard({super.key});

  @override
  State<ChamasDashboard> createState() => _ChamasDashboardState();
}

class _ChamasDashboardState extends State<ChamasDashboard> {
  late final ChamasDashboardController controller;

  @override
  void initState() {
    super.initState();
    controller = Get.put(ChamasDashboardController());
  }

  @override
  void dispose() {
    Get.delete<ChamasDashboardController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() {
        if (controller.isLoading.value && controller.chamas.isEmpty) {
          return const KittyLoadingWidget(
            message: 'Loading chamas...',
            size: 70.0,
          );
        }

        if (controller.hasError.value && controller.chamas.isEmpty) {
          return _buildErrorState(context);
        }

        return _buildTable(context);
      }),
    );
  }



  Widget _buildTable(BuildContext context) {
    return Obx(() => AppTable(
          title: 'Chamas Dashboard',
          actions: [
            ActionButton(
              icon: Icons.filter_list,
              label: 'Filters & Actions',
              onTap: () => _showFilterDialog(context),
            ),
            ActionButton(
              icon: Icons.refresh,
              label: 'Refresh',
              onTap: controller.refreshData,
            ),
            ActionButton(
              icon: Icons.download,
              label: 'Export',
              onTap: () => _showExportDialog(context),
            ),
            ActionButton(
              icon: Icons.admin_panel_settings,
              label: 'Admin Actions',
              onTap: () => _showAdminActionsDialog(context),
            ),
          ],
          columns: controller.plutoColumns,
          rows: controller.plutoRows,
          totalPages: controller.totalPages.value,
          totalSizePerPage: controller.pageSize.value,
          onPageNavigated: controller.handlePageChange,
          onItemClicked: controller.onChamaTapped,
          onRefresh: controller.refreshData,
          onSearch: controller.handleSearch,
          cacheKey: 'chamas_dashboard',
          enableSorting: true,
          enableExport: true,
          enableMultiSelect: false,
          emptyStateWidget: _buildEmptyState(context),
          loadingWidget: const KittyLoadingWidget(
            message: 'Loading chamas...',
            size: 60.0,
          ),
          isExternalLoading: controller.isLoading.value && controller.chamas.isNotEmpty,
        ));
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.savings_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No chamas found',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.grey[600],
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try adjusting your search or filters',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[500],
                ),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: controller.clearFilters,
            icon: const Icon(Icons.clear_all),
            label: const Text('Clear Filters'),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Failed to load chamas',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.red[600],
                ),
          ),
          const SizedBox(height: 8),
          Obx(() => Text(
                controller.apiMessage.value,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.red[500],
                    ),
                textAlign: TextAlign.center,
              )),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: controller.refreshData,
            icon: const Icon(Icons.refresh),
            label: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: 500,
          constraints: const BoxConstraints(maxHeight: 600),
          padding: const EdgeInsets.all(24),
          child: SingleChildScrollView(
            child: ChamasFilterWidget(controller: controller),
          ),
        ),
      ),
    );
  }

  void _showExportDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.file_download),
              title: const Text('Export as CSV'),
              onTap: () {
                Navigator.pop(context);
                controller.exportData('csv');
              },
            ),
            ListTile(
              leading: const Icon(Icons.file_download),
              title: const Text('Export as Excel'),
              onTap: () {
                Navigator.pop(context);
                controller.exportData('excel');
              },
            ),
            ListTile(
              leading: const Icon(Icons.file_download),
              title: const Text('Export as PDF'),
              onTap: () {
                Navigator.pop(context);
                controller.exportData('pdf');
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showAdminActionsDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.send),
              title: const Text('Send Funds to Beneficiaries'),
              onTap: () {
                Navigator.pop(context);
                _showSendFundsDialog(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.warning),
              title: const Text('Add General Penalty'),
              onTap: () {
                Navigator.pop(context);
                _showAddPenaltyDialog(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.block),
              title: const Text('Block Selected Chama'),
              onTap: () {
                Navigator.pop(context);
                _showBlockChamaDialog(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.check_circle),
              title: const Text('Unblock Selected Chama'),
              onTap: () {
                Navigator.pop(context);
                _showUnblockChamaDialog(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.receipt),
              title: const Text('View Transactions'),
              onTap: () {
                Navigator.pop(context);
                Get.snackbar(
                  'Coming Soon',
                  'Transaction view feature will be available soon',
                  snackPosition: SnackPosition.bottom,
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.settings),
              title: const Text('View Settings'),
              onTap: () {
                Navigator.pop(context);
                Get.snackbar(
                  'Coming Soon',
                  'Settings view feature will be available soon',
                  snackPosition: SnackPosition.bottom,
                );
              },
            ),
          ],
        ),
      ),
    );
  }



  void _showSendFundsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Send Funds to Beneficiaries'),
        content: const Text('Are you sure you want to send funds to the current beneficiaries?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              // For demo purposes, we'll use the first chama
              if (controller.chamas.isNotEmpty) {
                controller.sendFundsToBeneficiaries(controller.chamas.first.id ?? 0);
              }
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: const Text('Send Funds'),
          ),
        ],
      ),
    );
  }

  void _showAddPenaltyDialog(BuildContext context) {
    final amountController = TextEditingController();
    final reasonController = TextEditingController();
    final descriptionController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add General Penalty'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: amountController,
              decoration: const InputDecoration(
                labelText: 'Penalty Amount (KES)',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: reasonController,
              decoration: const InputDecoration(
                labelText: 'Reason',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description (Optional)',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (amountController.text.isNotEmpty && reasonController.text.isNotEmpty) {
                // For demo purposes, we'll use the first chama
                if (controller.chamas.isNotEmpty) {
                  controller.addGeneralPenalty(
                    chamaId: controller.chamas.first.id ?? 0,
                    amount: double.tryParse(amountController.text) ?? 0.0,
                    reason: reasonController.text,
                    description: descriptionController.text.isNotEmpty ? descriptionController.text : null,
                  );
                }
                Navigator.of(context).pop();
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: const Text('Add Penalty'),
          ),
        ],
      ),
    );
  }

  void _showBlockChamaDialog(BuildContext context) {
    final reasonController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Block Chama'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Please provide a reason for blocking this chama:'),
            const SizedBox(height: 16),
            TextField(
              controller: reasonController,
              decoration: const InputDecoration(
                labelText: 'Reason',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (reasonController.text.isNotEmpty) {
                // For demo purposes, we'll block the first chama
                if (controller.chamas.isNotEmpty) {
                  controller.blockChama(
                    controller.chamas.first.id ?? 0,
                    reasonController.text,
                  );
                }
                Navigator.of(context).pop();
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Block Chama'),
          ),
        ],
      ),
    );
  }

  void _showUnblockChamaDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Unblock Chama'),
        content: const Text('Are you sure you want to unblock this chama?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              // For demo purposes, we'll unblock the first chama
              if (controller.chamas.isNotEmpty) {
                controller.unblockChama(controller.chamas.first.id ?? 0);
              }
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: const Text('Unblock Chama'),
          ),
        ],
      ),
    );
  }
}
