import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

// import 'package:flutter_tawk/flutter_tawk.dart';
import 'package:flutter_tawkto/flutter_tawk.dart';
import 'package:get/get.dart';
import 'package:onekitty_admin/features/onekitty/controllers/config.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/controllers/user_ktty_controller.dart';

class TawkWidget extends StatefulWidget {
  const TawkWidget({super.key});

  @override
  State<TawkWidget> createState() => _TawkWidgetState();
}

class _TawkWidgetState extends State<TawkWidget> {
  final ConfigDataController configDataController =
      Get.put(ConfigDataController());
  UserKittyController userController = Get.put(UserKittyController());
  ConfigController configController = Get.put(ConfigController());

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Tawk(
        directChatLink: configController.tawk.value.toString(),
        visitor: TawkVisitor(
          name:
              '${userController.getLocalUser()!.firstName} ${userController.getLocalUser()!.secondName}',
          email: '${userController.getLocalUser()!.phoneNumber}',
        ),
        onLoad: () {
        },
        onLinkTap: (String url) {
        },
        placeholder: SpinKitDualRing(
          color: Theme.of(context).primaryColor,
          size: 70.0,
        ),
      ),
    );
  }

  // Future<String> getTawkLink() async {
  //   var res = await configController.getConfig();
  //   print(res);
  //   if (res) {
  //     configLink = configController.tawk.value;
  //   } else {
  //     return configController.apiMessage.string;
  //   }
  //   return configLink;
  // }
}

//{data: {kitty: {ID: 1323, CreatedAt: 2024-03-27T23:34:43.102+03:00, UpdatedAt: 2024-03-27T23:34:43.102+03:00, DeletedAt: null, title: Second Contribution, description: My Contribution, beneficiary_account: ************, beneficiary_channel: MPESA, beneficiary_phone_number: ************, end_date: 2024-03-30T02:03:00Z, balance: 0, limit: null, referer_merchant_code: null, phone_number: ************, settlement_type: 0}, merchant: null, url: https://www.onekitty.co.ke/kitty/1323/}, message: successfuly created kitty, social_mesages: {ID: 0, CreatedAt: 0001-01-01T00:00:00Z, UpdatedAt: 0001-01-01T00:00:00Z, DeletedAt: null, facebook: @onekitty.co.ke Pooling funds for Second Contribution with OneKitty! Every bit helps. Contribute here: https://www.onekitty.co.ke/kitty/1323/ #OnekittyKE #Onekitty_KE #onekitty, tiktok: @onekitty.co.ke OneKitty: Making dreams a reality, one contribution at a time! Help us fund Second Contribution https://www.onekitty.co.ke/kitty/1323/ #Oneki