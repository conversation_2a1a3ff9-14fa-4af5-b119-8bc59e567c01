import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty_admin/features/onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty_admin/helpers/colors.dart';
import 'package:onekitty_admin/helpers/extensions/text_styles.dart';
import 'package:onekitty_admin/helpers/show_snack_bar.dart';
import 'package:onekitty_admin/models/chama/chama_settings.dart';
import 'package:onekitty_admin/models/chama/edit_chama_settings_request.dart';
import 'package:onekitty_admin/nav_routes/nav_routes.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/widgets/row_widget.dart';
import 'package:onekitty_admin/features/onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty_admin/utils/common_strings.dart';
import 'package:onekitty_admin/utils/utils_exports.dart';

class EditChamaSettings extends StatefulWidget {
  final ChamaSetting chamaSetting;
  const EditChamaSettings({super.key, required this.chamaSetting});

  @override
  State<EditChamaSettings> createState() => _EditChamaSettingsState();
}

class _EditChamaSettingsState extends State<EditChamaSettings> {
  final ChamaController chamaController = Get.put(ChamaController());
  final ChamaDataController chamaDataController =
      Get.put(ChamaDataController());
  TextEditingController benefPerCycleCtr = TextEditingController();
  TextEditingController sigThreshCtr = TextEditingController();
  final formKey = GlobalKey<FormState>();
  double value = 0.0;

  @override
  void initState() {
    super.initState();
    benefPerCycleCtr.text = chamaController.benefPerCycle.value.toString();
    sigThreshCtr.text = chamaController.signatureThreshold.value.toString();
    value = chamaController.benefPercentage.value * 100;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      //appBar: buildAppBar(context),
      body: Container(
          margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 40),
          child: Form(
              key: formKey,
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    const RowAppBar(),
                    Text("edit_chama_settings".tr,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold, fontSize: 22)),
                    SizedBox(
                      height: 5.h,
                    ),
                    Text(
                      "chama_settings_description".tr,
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(
                      height: 15.h,
                    ),
                    SingleLineRow(
                      text: "beneficiaries_per_cycle".tr,
                      popup: KtStrings.benefPerCycle,
                    ),
                    CustomTextField(
                      controller: benefPerCycleCtr,
                      labelText: "enter_number_of_beneficiaries_per_cycle".tr,
                      showNoKeyboard: true,
                      isRequired: true,
                      validator: (p0) {
                        if (p0!.isEmpty) {
                          return "field_cannot_be_empty".tr;
                        }
                        return null;
                      },
                    ),
                    SingleLineRow(
                      text: "beneficiaries_percentage_value".tr.replaceAll('{value}', value.toString()),
                      popup: KtStrings.benefPercentage,
                    ),
                    Text(
                      "beneficiaries_percentage_value".tr.replaceAll('{value}', value.toString()),
                      style: context.dividerTextSmall?.copyWith(
                        fontStyle: FontStyle.italic,
                        color: AppColors.greyTextColor,
                      ),
                    ),
                    Slider(
                        value: value,
                        min: 0,
                        max: 100,
                        divisions: 10,
                        label: value.round().toString(),
                        onChanged: (value) {
                          setState(() {
                            this.value = value;
                          });
                        }),
                    SingleLineRow(
                      text: "signature_threshhold".tr,
                      popup: KtStrings.sigThreshold,
                    ),
                    CustomTextField(
                      controller: sigThreshCtr,
                      labelText: "enter_number_of_sign_signatories".tr,
                      showNoKeyboard: true,
                      isRequired: true,
                      validator: (p0) {
                        if (p0!.isEmpty) {
                          return "field_cannot_be_empty".tr;
                        }
                        return null;
                      },
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    Obx(() => CustomKtButton(
                        isLoading: chamaController.isEditChamaSettings.isTrue,
                        onPress: () async {
                          if (formKey.currentState!.validate()) {
                            EditChamaSettingsRequest edit =
                                EditChamaSettingsRequest(
                              id: chamaController.settingId.value,
                              chamaId:
                                  chamaDataController.chama.value.chama?.id,
                              beneficiariesPerCycle:
                                  int.tryParse(benefPerCycleCtr.text.trim()),
                              beneficiaryPercentage: double.parse(
                                  (value / 100).toStringAsFixed(1)),
                              signatureThreshold:
                                  int.parse(sigThreshCtr.text.trim()),
                            );
                            bool res = await chamaController.editChamaSettings(
                                request: edit);
                            if (res) {
                              // Refresh current chama data
                              await chamaController.getAllChamaDetails(
                                  chamaId: chamaDataController.chama.value.chama?.id ?? 0);
                              // Refresh user chamas list
                              await chamaController.getUserChamas();
                              if (!mounted) return;
                              Snack.show(
                                  res, chamaController.apiMessage.string);
                              Get.offAndToNamed(NavRoutes.chamaSettings);
                            } else {
                              if (!mounted) return;
                              Snack.show(
                                  res, chamaController.apiMessage.string);
                            }
                          }
                        },
                        btnText: "edit".tr))
                  ],
                ),
              ))),
    );
  }
}
