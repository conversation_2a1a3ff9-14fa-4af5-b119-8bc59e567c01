// Payment Method Selector Widget
// Reusable widget for selecting payment methods

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty_admin/configs/payment_channels.dart';
import 'package:onekitty_admin/models/auth/payments_channels.dart';
import 'package:onekitty_admin/utils/payment_kensbuns.dart';

class PaymentMethodSelector extends StatelessWidget {
  final RxInt selectedProvider;
  final Function(int) onProviderChanged;

    PaymentMethodSelector({
    super.key,
    required this.selectedProvider,
    required this.onProviderChanged,
  });
  final PaymentChannel paymentChannelsControllers = (Get.isRegistered() ? Get.find() : Get.put(PaymentChannel()));

  @override
  Widget build(BuildContext context) {
    final channels = paymentChannelsControllers.paymentChannels.where((e) => e.typeIn == true).toList();
    return Obx(() => Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: List.generate(channels.length, (index) {
        final channel = channels[index];
        return _buildProviderOption(channel);
      }),
      /*children: [
        _buildProviderOption('PayPal', 'assets/images/paypal.png'), 
        _buildProviderOption('M-PESA', 'assets/images/mpesa.png'),
        _buildProviderOption('SasaPay', 'assets/images/sasapay.png'),
        _buildProviderOption('AirtelMoney', 'assets/images/airtelmoney.png'),
      ],*/
    ));
  }

  Widget _buildProviderOption(PaymentChannels provider) {
    return GestureDetector(
      onTap: () => onProviderChanged(provider.channelCode),
      child: Column(
        children: [
          if(provider.name.toLowerCase() == "card")...[
            const PaymentKensbuns()
          ]else...[          
          Image.asset(
            provider.imageUrl,
            width: 50.w,
            height: 50.h,
          ),
          ],
          Radio<int>(
            value: provider.channelCode,
            groupValue: selectedProvider.value,
            onChanged: (value) => onProviderChanged(value!),
          ),
        ],
      ),
    );
  }
}