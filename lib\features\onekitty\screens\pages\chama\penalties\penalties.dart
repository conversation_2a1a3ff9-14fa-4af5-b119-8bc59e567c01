import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:onekitty_admin/features/onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty_admin/helpers/extensions/text_styles.dart';
import 'package:onekitty_admin/helpers/show_snack_bar.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/chama/penalties/add_penalty.dart';
import 'package:onekitty_admin/utils/asset_urls.dart';
import 'package:onekitty_admin/utils/custom_button.dart';
import 'package:onekitty_admin/utils/custom_image_view.dart';
import 'package:onekitty_admin/utils/delete_dialog.dart';
import 'package:onekitty_admin/utils/formatted_currency.dart';
import 'package:onekitty_admin/utils/size_config.dart';
import 'package:onekitty_admin/utils/themes_colors.dart';

class PenaltiesPage extends StatefulWidget {
  const PenaltiesPage({super.key});

  @override
  State<PenaltiesPage> createState() => _PenaltiesPageState();
}

class _PenaltiesPageState extends State<PenaltiesPage> {
  final ChamaDataController chamaDataController =
      Get.put(ChamaDataController());
  final ChamaController chamaController = Get.put(ChamaController());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
        child: Column(
          children: [
            Row(
              children: [
                IconButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    icon: const Icon(Icons.arrow_back)),
                Text('back'.tr)
              ],
            ),
            Text(
              'penalties_title'.tr,
              style: context.titleText
                  ?.copyWith(fontSize: 22, fontWeight: FontWeight.bold),
            ),
            const SizedBox(
              height: 7,
            ),
            Text(
              'penalties_description'.tr,
              textAlign: TextAlign.center,
              style: TextStyle(
                  color: appTheme.gray600, fontWeight: FontWeight.w600),
            ),
            Expanded(
                child: GetX(
                    init: ChamaController(),
                    initState: (state) {
                      Future.delayed(Duration.zero, () async {
                        try {
                          await state.controller?.getChamaPenalties(
                              chamaId:
                                  chamaDataController.chama.value.chama?.id ??
                                      0);
                        } catch (e) {
                          throw e;
                        }
                      });
                    },
                    builder: (ChamaController chamaController) {
                      if (chamaController.isGetChamaPenaltyLoading.isTrue) {
                        return SizedBox(
                          height: SizeConfig.screenHeight * .33,
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SpinKitDualRing(
                                  color: ColorUtil.blueColor,
                                  lineWidth: 4.sp,
                                  size: 40.0.sp,
                                ),
                                Text(
                                  'loading'.tr,
                                  style: const TextStyle(
                                    color: Colors.white,
                                  ),
                                )
                              ],
                            ),
                          ),
                        );
                      } else if (chamaController.penalties.isEmpty) {
                        return Center(
                            child: Text('no_penalties_added'.tr));
                      } else if (chamaController.penalties.isNotEmpty) {
                        return ListView.separated(
                            shrinkWrap: true,
                            itemBuilder: (context, index) {
                              final penalty = chamaController.penalties[index];
                              return Card(
                                child: ListTile(
                                    onTap: () {
                                      if (chamaDataController
                                                  .chama.value.member?.role ==
                                              "CHAIRPERSON" ||
                                          chamaDataController
                                                  .chama.value.member?.role ==
                                              "TREASURER") {
                                        Get.off(() => AddPenalty(
                                              isUpdating: true,
                                              penaltyModel: penalty,
                                            ));
                                      }
                                    },
                                    leading: const CustomImageView(
                                      imagePath: AssetUrl.stamp,
                                    ),
                                    title: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          penalty.title ?? "",
                                          maxLines: 2,
                                          overflow: TextOverflow.ellipsis,
                                          style: context.titleText,
                                        ),
                                        Text(
                                          penalty.description ?? "",
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                        Text(
                                          FormattedCurrency
                                              .getFormattedCurrency(
                                                  penalty.amount),
                                          style: context.titleMedium?.copyWith(
                                              color: Colors.red,
                                              fontWeight: FontWeight.bold),
                                        ),
                                      ],
                                    ),
                                    trailing: IconButton(
                                        onPressed: () {
                                          if (chamaDataController.chama.value
                                                      .member?.role ==
                                                  "CHAIRPERSON" ||
                                              chamaDataController.chama.value
                                                      .member?.role ==
                                                  "TREASURER") {
                                            showDeleteDialog(
                                                index,
                                                context,
                                                chamaController
                                                    .isDeletePenaltyLoading
                                                    .isTrue, () {
                                              deletePenalty(index);
                                              Navigator.pop(context);
                                            }, 'penalty'.tr);
                                          }
                                        },
                                        icon: const Icon(Icons.close))),
                              );
                            },
                            separatorBuilder: (context, index) {
                              return const SizedBox(
                                height: 15,
                              );
                            },
                            itemCount: chamaController.penalties.length);
                      }
                      return Center(
                          child: Text('no_penalties_added_yet'.tr));
                    })),
            SizedBox(
              height: 40,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  OutlinedButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      child: Padding(
                        padding:  const EdgeInsets.symmetric(horizontal: 8.0),
                        child: Text('back'.tr),
                      )),
                  if (chamaDataController.chama.value.member?.role ==
                          "CHAIRPERSON" ||
                      chamaDataController.chama.value.member?.role ==
                          "TREASURER")
                    CustomKtButton(
                      onPress: () {
                        Get.off(() => const AddPenalty(
                              isUpdating: false,
                            ));
                      },
                      btnText: 'add_penalty'.tr,
                      width: 150.w,
                    )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  deletePenalty(index) async {
    final penalty = chamaController.penalties[index];
    bool res = await chamaController.deletePenalty(
        chamaId: chamaDataController.chama.value.chama?.id ?? 0,
        penaltyId: penalty.id ?? 0);
    if (res) {
      Snack.show(res, chamaController.apiMessage.string);
      setState(() {
        chamaController.penalties.removeAt(index);
      });
    }
  }
}
