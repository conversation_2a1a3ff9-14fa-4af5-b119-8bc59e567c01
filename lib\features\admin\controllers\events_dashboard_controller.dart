import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onekitty_admin/features/admin/services/events_admin_service.dart';
import 'package:onekitty_admin/models/events/events_model.dart';
import 'package:pluto_grid/pluto_grid.dart';
import 'package:intl/intl.dart';

import '../../../models/events/tickets_model.dart';
import '../../../features/onekitty/screens/pages/events/controllers/view_single_event.dart';
import '../../../features/onekitty/screens/pages/events/views/screens/view_single_event_organizer.dart';

class EventsDashboardController extends GetxController {
  final EventsAdminService _eventsService = EventsAdminService();
  final Logger _logger = Logger();

  // Reactive variables
  final RxList<Event> events = <Event>[].obs;
  final RxBool isLoading = false.obs;
  final RxString apiMessage = ''.obs;
  final RxBool hasError = false.obs;

  // Pagination
  final RxInt currentPage = 0.obs;
  final RxInt pageSize = 15.obs;
  final RxInt totalPages = 1.obs;
  final RxInt totalItems = 0.obs;
  final RxBool hasNext = false.obs;
  final RxBool hasPrevious = false.obs;

  // Filters
  final RxString search = ''.obs;
  final RxString phoneNumber = ''.obs;
  final RxString kittyId = ''.obs;
  final RxString frequency = ''.obs;
  final RxString startDate = ''.obs;
  final RxString endDate = ''.obs;

  // PlutoGrid
  final RxList<PlutoRow> plutoRows = <PlutoRow>[].obs;
  final RxList<PlutoColumn> plutoColumns = <PlutoColumn>[].obs;

  Timer? _debounce;

  @override
  void onInit() {
    super.onInit();
    _initializeColumns();
    fetchEvents(0);
  }

  @override
  void onClose() {
    _debounce?.cancel();
    super.onClose();
  }

  void _initializeColumns() {
    plutoColumns.value = [
      PlutoColumn(
        title: 'ID',
        field: 'id',
        type: PlutoColumnType.text(),
        width: 80,
        enableSorting: true,
        enableColumnDrag: false,
        frozen: PlutoColumnFrozen.start,
      ),
      PlutoColumn(
        title: 'Created At',
        field: 'created_at',
        type: PlutoColumnType.text(),
        width: 120,
        enableSorting: true,
      ),
      PlutoColumn(
        title: 'Title',
        field: 'title',
        type: PlutoColumnType.text(),
        width: 150,
        enableSorting: true,
      ),
      PlutoColumn(
        title: 'Username',
        field: 'username',
        type: PlutoColumnType.text(),
        width: 120,
        enableSorting: true,
      ),
      PlutoColumn(
        title: 'Kitty ID',
        field: 'kitty_id',
        type: PlutoColumnType.text(),
        width: 100,
        enableSorting: false,
      ),
      PlutoColumn(
        title: 'Email',
        field: 'email',
        type: PlutoColumnType.text(),
        width: 150,
        enableSorting: false,
      ),
      PlutoColumn(
        title: 'Phone Number',
        field: 'phone_number',
        type: PlutoColumnType.text(),
        width: 130,
        enableSorting: false,
      ),
      PlutoColumn(
        title: 'Venue',
        field: 'venue',
        type: PlutoColumnType.text(),
        width: 150,
        enableSorting: false,
      ),
      PlutoColumn(
        title: 'Start Date',
        field: 'start_date',
        type: PlutoColumnType.text(),
        width: 120,
        enableSorting: true,
      ),
      PlutoColumn(
        title: 'End Date',
        field: 'end_date',
        type: PlutoColumnType.text(),
        width: 120,
        enableSorting: true,
      ),
      PlutoColumn(
        title: 'Status',
        field: 'status',
        type: PlutoColumnType.text(),
        width: 100,
        enableSorting: true,
      ),
    ];
  }

  Future<void> fetchEvents(int page) async {
    try {
      isLoading(true);
      hasError(false);
      
      final result = await _eventsService.fetchEvents(
        page: page,
        size: pageSize.value,
        search: search.value.isNotEmpty ? search.value : null,
        phoneNumber: phoneNumber.value.isNotEmpty ? phoneNumber.value : null,
        kittyId: kittyId.value.isNotEmpty ? kittyId.value : null,
        frequency: frequency.value.isNotEmpty ? frequency.value : null,
        startDate: startDate.value.isNotEmpty ? startDate.value : null,
        endDate: endDate.value.isNotEmpty ? endDate.value : null,
      );

      if (result['success']) {
        events.value = result['events'];
        _updatePlutoRows();
        
        final pagination = result['pagination'];
        currentPage.value = pagination['page'];
        totalPages.value = pagination['total_pages'];
        totalItems.value = pagination['total_items'];
        hasNext.value = pagination['has_next'];
        hasPrevious.value = pagination['has_previous'];
        
        apiMessage.value = result['message'];
      } else {
        hasError(true);
        apiMessage.value = result['message'];
        events.clear();
        plutoRows.clear();
      }
    } catch (e) {
      _logger.e('Error in fetchEvents: $e');
      hasError(true);
      apiMessage.value = 'An unexpected error occurred';
      events.clear();
      plutoRows.clear();
    } finally {
      isLoading(false);
    }
  }

  void _updatePlutoRows() {
    plutoRows.value = events.map((event) {
      return PlutoRow(cells: {
        'id': PlutoCell(value: event.id?.toString() ?? '-'),
        'created_at': PlutoCell(
          value: event.createdAt != null
              ? DateFormat('dd/MM/yy HH:mm').format(event.createdAt!)
              : '-',
        ),
        'title': PlutoCell(value: event.title),
        'username': PlutoCell(value: event.username),
        'kitty_id': PlutoCell(value: event.kittyId?.toString() ?? '-'),
        'email': PlutoCell(value: event.email),
        'phone_number': PlutoCell(value: event.phoneNumber),
        'venue': PlutoCell(value: event.venue),
        'start_date': PlutoCell(
          value: event.startDate != null
              ? DateFormat('dd/MM/yy HH:mm').format(event.startDate!)
              : '-',
        ),
        'end_date': PlutoCell(
          value: event.endDate != null
              ? DateFormat('dd/MM/yy HH:mm').format(event.endDate!)
              : '-',
        ),
        'status': PlutoCell(value: _getStatusDisplayText(event.status)),
      });
    }).toList();
  }

  String _getStatusDisplayText(Status? status) {
    if (status == null) return '-';
    switch (status) {
      case Status.ACTIVE:
        return 'Active';
      case Status.DELETED:
        return 'Deleted';
      default:
        return status.toString().split('.').last;
    }
  }

  Color getEventStatusColor(String status) {
    switch (status.toLowerCase()) {
      case "active":
        return const Color(0xFF56AF57);
      case "ended":
        return Colors.grey;
      case "pending review":
        return Colors.amber;
      case "blocked":
        return const Color(0xFFEE5B60);
      default:
        return const Color(0xFFEE5B60);
    }
  }

  void handleSearch(String value) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    
    _debounce = Timer(const Duration(seconds: 1), () {
      search.value = value;
      fetchEvents(0);
    });
  }

  void handlePageChange(int page) {
    fetchEvents(page);
  }

  void clearFilters() {
    search.value = '';
    phoneNumber.value = '';
    kittyId.value = '';
    frequency.value = '';
    startDate.value = '';
    endDate.value = '';
    fetchEvents(0);
  }

  void refreshData() {
    fetchEvents(currentPage.value);
  }

  Future<void> selectStartDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );

    if (picked != null) {
      startDate.value = picked.toUtc().toIso8601String();
      fetchEvents(0);
    }
  }

  Future<void> selectEndDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null) {
      endDate.value = picked.toUtc().toIso8601String();
      fetchEvents(0);
    }
  }

  void clearStartDate() {
    startDate.value = '';
    fetchEvents(0);
  }

  void clearEndDate() {
    endDate.value = '';
    fetchEvents(0);
  }

  void onEventTapped(PlutoRow row) {
    final index = plutoRows.indexOf(row);
    if (index >= 0 && index < events.length) {
      final selectedEvent = events[index];
      _logger.i('Event tapped: ${selectedEvent.title}');

      // Set the selected event in ViewSingleEventController
      Get.put(ViewSingleEventController()).event(selectedEvent);

      // Navigate to ViewSingleEventOrganizer with right-to-left transition
      Get.to(
        () => ViewSingleEventOrganizer(
          eventmodel: MyEventsModel(
            event: selectedEvent,
            count: 0,
            hasSignatoryTransactions: false,
          ),
        ),
        transition: Transition.rightToLeft,
      );
    }
  }

  Future<void> blockEvent(int eventId, String reason) async {
    try {
      isLoading(true);
      
      final result = await _eventsService.blockEvent(
        eventId: eventId,
        reason: reason,
      );

      if (result['success']) {
        Get.snackbar(
          'Success',
          'Event blocked successfully',
          snackPosition: SnackPosition.bottom,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
        refreshData();
      } else {
        Get.snackbar(
          'Error',
          result['message'],
          snackPosition: SnackPosition.bottom,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      _logger.e('Error blocking event: $e');
      Get.snackbar(
        'Error',
        'An error occurred while blocking event',
        snackPosition: SnackPosition.bottom,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading(false);
    }
  }

  Future<void> unblockEvent(int eventId) async {
    try {
      isLoading(true);
      
      final result = await _eventsService.unblockEvent(eventId);

      if (result['success']) {
        Get.snackbar(
          'Success',
          'Event unblocked successfully',
          snackPosition: SnackPosition.bottom,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
        refreshData();
      } else {
        Get.snackbar(
          'Error',
          result['message'],
          snackPosition: SnackPosition.bottom,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      _logger.e('Error unblocking event: $e');
      Get.snackbar(
        'Error',
        'An error occurred while unblocking event',
        snackPosition: SnackPosition.bottom,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading(false);
    }
  }

  Future<void> exportData(String format) async {
    try {
      isLoading(true);

      final filters = <String, dynamic>{
        'search': search.value,
        'phone_number': phoneNumber.value,
        'kitty_id': kittyId.value,
        'frequency': frequency.value,
        'start_date': startDate.value,
        'end_date': endDate.value,
      };

      final result = await _eventsService.exportEventsData(
        format: format,
        filters: filters,
      );

      if (result['success']) {
        Get.snackbar(
          'Export Successful',
          'Data exported successfully. Download will start shortly.',
          snackPosition: SnackPosition.bottom,
        );
      } else {
        Get.snackbar(
          'Export Failed',
          result['message'],
          snackPosition: SnackPosition.bottom,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      _logger.e('Error exporting data: $e');
      Get.snackbar(
        'Export Error',
        'An error occurred while exporting data',
        snackPosition: SnackPosition.bottom,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading(false);
    }
  }

  // Dialog methods moved from widget
  void showAdminActionsDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.block),
              title: const Text('Block Selected Event'),
              onTap: () {
                Navigator.pop(context);
                showBlockEventDialog(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.check_circle),
              title: const Text('Unblock Selected Event'),
              onTap: () {
                Navigator.pop(context);
                showUnblockEventDialog(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.receipt),
              title: const Text('View Transactions'),
              onTap: () {
                Navigator.pop(context);
                Get.snackbar(
                  'Coming Soon',
                  'Transaction view feature will be available soon',
                  snackPosition: SnackPosition.bottom,
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  void showBlockEventDialog(BuildContext context) {
    final reasonController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Block Event'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Please provide a reason for blocking this event:'),
            const SizedBox(height: 16),
            TextField(
              controller: reasonController,
              decoration: const InputDecoration(
                labelText: 'Reason',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (reasonController.text.isNotEmpty) {
                // For demo purposes, we'll block the first event
                if (events.isNotEmpty) {
                  blockEvent(
                    events.first.id ?? 0,
                    reasonController.text,
                  );
                }
                Navigator.of(context).pop();
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Block Event'),
          ),
        ],
      ),
    );
  }

  void showUnblockEventDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Unblock Event'),
        content: const Text('Are you sure you want to unblock this event?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              // For demo purposes, we'll unblock the first event
              if (events.isNotEmpty) {
                unblockEvent(events.first.id ?? 0);
              }
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: const Text('Unblock Event'),
          ),
        ],
      ),
    );
  }
}
