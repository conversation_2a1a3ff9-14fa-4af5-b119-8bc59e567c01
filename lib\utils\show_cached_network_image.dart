import 'package:fast_cached_network_image/fast_cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:onekitty_admin/utils/asset_urls.dart';
import 'package:shimmer/shimmer.dart';

class ShowCachedNetworkImage extends StatefulWidget {
  const ShowCachedNetworkImage({
    super.key,
    required this.imageurl,
    this.fit,
    this.height,
    this.width,
    this.borderRadius,
    this.errorWidget,
  });

  final String imageurl;
  final double? height;
  final BoxFit? fit;
  final double? width;
  final BorderRadius? borderRadius;
  final Widget? errorWidget;

  @override
  _ShowCachedNetworkImageState createState() => _ShowCachedNetworkImageState();
}

class _ShowCachedNetworkImageState extends State<ShowCachedNetworkImage> {
  late String currentImageUrl;

  @override
  void initState() {
    super.initState();
    currentImageUrl = widget.imageurl;
  }

  void reloadImage() {
    setState(() {
      // Append a timestamp to force reload
      currentImageUrl =
          '${widget.imageurl}?timestamp=${DateTime.now().millisecondsSinceEpoch}';
    });
  }

  @override
  Widget build(BuildContext context) {
    if (currentImageUrl.isEmpty || !Uri.parse(currentImageUrl).isAbsolute) {
      return widget.errorWidget ??
          Container(
            color: Colors.grey,
            child: const Center(
              child: Text(
                'Invalid image URL',
                style: TextStyle(color: Colors.white),
              ),
            ),
          );
    }

    return ClipRRect(
      borderRadius: widget.borderRadius ?? BorderRadius.circular(4.5),
      child: FastCachedImage(
        url: currentImageUrl,
        fit: widget.fit ?? BoxFit.cover,
        height: widget.height ?? 25,
        width: widget.width ?? 35,
        fadeInDuration: const Duration(seconds: 1),
        errorBuilder: (context, url, error) {
          return widget.errorWidget ??
              Container(
                color: Colors.grey,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text(
                      'Image couldn\'t be loaded',
                      style: TextStyle(color: Colors.white),
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: reloadImage,
                      child: const Text('Try Again'),
                    ),
                  ],
                ),
              );
        },
        loadingBuilder: (context, url) {
          return Container(
            width: widget.width ?? 200,
            height: widget.height ?? 120,
            color: Colors.transparent,
            child: Shimmer.fromColors(
              baseColor: Colors.grey[300]!,
              highlightColor: Colors.grey[100]!,
              child: Container(
                color: Colors.white,
              ),
            ),
          );
        },
      ),
    );
  }
}

class AdaptiveCachedNetworkImage extends StatefulWidget {
  const AdaptiveCachedNetworkImage({
    super.key,
    required this.imageUrl,
    this.initialHeight,
    this.initialWidth,
    this.borderRadius = 4.5,
  });

  final String imageUrl;
  final double? initialHeight;
  final double? initialWidth;
  final double borderRadius;

  @override
  _AdaptiveCachedNetworkImageState createState() =>
      _AdaptiveCachedNetworkImageState();
}

class _AdaptiveCachedNetworkImageState
    extends State<AdaptiveCachedNetworkImage> {
  // Make _imageStream nullable but not late to avoid LateInitializationError
  ImageStream? _imageStream;
  ImageInfo? _imageInfo;
  bool _isLoading = true;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _getImage();
  }

  void _getImage() {
    if (widget.imageUrl.isEmpty || !Uri.parse(widget.imageUrl).isAbsolute) {
      setState(() {
        _isLoading = false;
      });
      return;
    }

    final NetworkImage image = NetworkImage(widget.imageUrl);
    _imageStream = image.resolve(createLocalImageConfiguration(context));
    _imageStream?.addListener(ImageStreamListener(_updateImage));
  }

  void _updateImage(ImageInfo imageInfo, bool synchronousCall) {
    setState(() {
      _imageInfo = imageInfo;
      _isLoading = false;
    });
  }

  @override
  void dispose() {
    _imageStream?.removeListener(ImageStreamListener(_updateImage));
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        double? height = widget.initialHeight;
        double? width = widget.initialWidth;
        BoxFit fit = BoxFit.contain;

        if (_imageInfo != null) {
          final double aspectRatio =
              _imageInfo!.image.width / _imageInfo!.image.height;
          if (aspectRatio > 1) {
            // Landscape
            width = constraints.maxWidth;
            height = width / aspectRatio;
            fit = BoxFit.fitWidth;
          } else {
            // Portrait
            height = constraints.maxHeight.isFinite
                ? constraints.maxHeight
                : 300; // Default height if constraint is infinite
            width = height * aspectRatio;
            fit = BoxFit.fitHeight;
          }
        } else {
          // If image info is not available, use default or constrained sizes
          width = width ?? constraints.maxWidth;
          height = height ??
              (constraints.maxHeight.isFinite
                  ? constraints.maxHeight
                  : 300); // Default height if constraint is infinite
        }

        // Ensure height and width are finite and within constraints
        height = height.clamp(
            0.0,
            constraints.maxHeight.isFinite
                ? constraints.maxHeight
                : double.infinity);
        width = width.clamp(0.0, constraints.maxWidth);

        return SizedBox(
          width: width,
          height: height,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(widget.borderRadius),
            child: FastCachedImage(
              url: widget.imageUrl,
              fit: fit,
              fadeInDuration: const Duration(milliseconds: 300),
              errorBuilder: (context, url, error) {
                return Stack(
                  alignment: Alignment.center,
                  children: [
                    Image.network(
                      height: 200.h,
                      width: 390.w,
                      AssetUrl.onekittyBannnerUrl,
                    ),
                    const Positioned(
                      top: 8,
                      right: 8,
                      child: Tooltip(
                          triggerMode: TooltipTriggerMode.tap,
                          message: 'Error loading event image',
                          child: Icon(Icons.error)),
                    )
                  ],
                );
              },
              loadingBuilder: (context, url) {
                return Container(
                  decoration: BoxDecoration(
                    color: Colors.transparent,
                    borderRadius: BorderRadius.circular(widget.borderRadius),
                  ),
                  child: Shimmer.fromColors(
                    baseColor: Colors.grey[300]!,
                    highlightColor: Colors.grey[100]!,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius:
                            BorderRadius.circular(widget.borderRadius),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }
}
