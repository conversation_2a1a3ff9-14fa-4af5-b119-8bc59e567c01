// Transfer Confirmation Widget
// Widget for displaying transfer confirmation details

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:onekitty_admin/features/onekitty/widgets/transfer_charges_dialog.dart';

class TransferConfirmationWidget extends StatelessWidget {
  final TransferChargeData chargeData;
  final String transferMode;
  final String reason;
  final bool isLoading;
  final VoidCallback onCancel;
  final VoidCallback onConfirm;

  const TransferConfirmationWidget({
    super.key,
    required this.chargeData,
    required this.transferMode,
    required this.reason,
    required this.isLoading,
    required this.onCancel,
    required this.onConfirm,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      child: TransferChargesDialog(
        amount: chargeData.amount,
        platformFee: chargeData.platformFee,
        transactionCharge: chargeData.transactionCharge,
        thirdPartyFee: chargeData.thirdPartyFee,
        totalCharges: chargeData.totalCharges,
        finalAmount: chargeData.finalAmount,
        remainingBalance: chargeData.remainingBalance,
        recipientName: chargeData.recipientName,
        recipientAccount: chargeData.recipientAccount,
        recipientAccountRef: chargeData.recipientAccountRef,
        transferMode: transferMode,
        reason: reason,
        isLoading: isLoading,
        onCancel: onCancel,
        onConfirm: onConfirm,
      ),
    );
  }
}