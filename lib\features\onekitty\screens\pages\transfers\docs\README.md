# Unified Transfer System

## Overview
The unified transfer system consolidates event transfers, chama transfers, and penalty transfers into a single, cohesive architecture following the same pattern as the transactions system.

## Architecture

```
transfers/
├── controllers/
│   └── transfer_controller.dart      # Unified state management
├── models/
│   └── transfer_type.dart           # Transfer types and configurations
├── services/
│   └── transfer_service.dart        # API service layer
├── utils/
│   └── transfer_navigation.dart     # Navigation utilities
├── views/
│   ├── screens/
│   │   └── transfer_page.dart       # Main transfer screen
│   └── widgets/
│       ├── payment_method_selector.dart
│       ├── transfer_form_widget.dart
│       └── transfer_confirmation_widget.dart
└── transfers_exports.dart           # Single import file
```

## Usage

### Basic Navigation
```dart
import 'package:onekitty_admin/features/onekitty/screens/pages/transfers/transfers_exports.dart';

// Event transfer
Get.toEventTransfer(eventId: 123);

// Chama transfer
Get.toChamaTransfer(chamaId: 456);

// Penalty transfer
Get.toPenaltyTransfer(chamaId: 456);
```

### Custom Configuration
```dart
TransferNavigation.toTransferWithConfig(
  TransferPageConfig(
    transferType: TransferType.chama,
    entityId: 123,
    title: 'Custom Transfer',
    isPenaltyTransfer: true,
  ),
);
```

## Features

### Transfer Types
- **Event Transfer**: Direct transfers for events (no signatory approval)
- **Chama Transfer**: Transfers requiring signatory approval
- **Penalty Transfer**: Transfers from penalty kitty balance

### Payment Methods
- Mobile Money (M-PESA, SasaPay, AirtelMoney)
- Paybill
- Till Number
- Bank Transfer

### Security
- Password authentication
- Device tracking
- Location tracking
- Form validation

## Key Differences from Original Implementation

### Consolidated Architecture
- Single controller handles all transfer types
- Unified service layer for API calls
- Consistent navigation patterns
- Reusable UI components

### Enhanced Features
- Type-safe transfer configurations
- Penalty transfer support
- Improved error handling
- Better state management

### Chama-Specific Features
- Signatory approval workflow
- Penalty kitty transfers
- Enhanced validation for chama context

## Migration Guide

### From Event Transfers
```dart
// Old
Get.to(() => TransferScreen(kittyId: eventId));

// New
Get.toEventTransfer(eventId: eventId);
```

### From Chama Transfers
```dart
// Old
// Complex chama transfer logic scattered across multiple files

// New
Get.toChamaTransfer(chamaId: chamaId);
Get.toPenaltyTransfer(chamaId: chamaId); // For penalty transfers
```