// ignore_for_file: control_flow_in_finally
import 'dart:io';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:logger/logger.dart';
import 'package:mime/mime.dart';
import 'package:image_picker/image_picker.dart';
import 'package:onekitty_admin/models/kitty/kitty_categories_model.dart';
import 'package:onekitty_admin/models/kitty_payload.dart';
import 'package:onekitty_admin/models/auth/regDTO_model.dart';
import 'package:onekitty_admin/services/api_urls.dart';
import 'package:onekitty_admin/services/http_service.dart';
import 'package:onekitty_admin/utils/cache_keys.dart';

/// Service class responsible for all Kitty-related API operations
class KittyService extends GetxService {
  final HttpService apiProvider = Get.find();
  final box = Get.find<GetStorage>();
  final logger = Get.find<Logger>();
  final ImagePicker _picker = ImagePicker();

  /// Get user data from local storage
  NewUser? getLocalUser() {
    final usr = box.read(CacheKeys.user);
    if (usr != null) {
      return NewUser.fromJson(usr);
    }
    return null;
  }

  /// Fetch balance and charges for a transaction
  Future<Map<String, dynamic>?> fetchBalance(
    double amount, {
    required Map<String, dynamic> data,
  }) async {
    try {
      var resp = await apiProvider.request(
        url: ApiUrls.fetchBalance,
        method: Method.POST,
        params: data,
      );
      if (resp.data['status'] ?? false) {
        return {
          'balance': double.tryParse(resp.data['data']['balance_charges'].toString()),
          'charges': double.tryParse(resp.data['data']['charges'].toString()),
          'totalAmount': double.tryParse(resp.data['data']['kitty_balance'].toString()),
          'thirdPartyCharges': double.tryParse(resp.data['data']['third_party_charges'].toString()),
        };
      }
      return null;
    } catch (e) {
      logger.e('Error fetching balance: $e');
      rethrow;
    }
  }

  /// Create a new kitty
  Future<Map<String, dynamic>> createKitty({required CreateKitPayload payload}) async {
    var resp = await apiProvider.request(
      url: ApiUrls.create_kitty,
      method: Method.POST,
      params: payload.toJson(),
    );
    return resp.data;
  }

  /// Upload image for kitty
  Future<Map<String, dynamic>?> uploadKittyImage({
    required String url,
    required String title,
    required int kittyId,
  }) async {
    try {
      var resp = await apiProvider.request(
        url: ApiUrls.create_kitty_media,
        method: Method.POST,
        params: {
          "url": url,
          "title": title,
          "type": "IMAGE",
          "category": "KITTY",
          "kitty_id": kittyId,
        },
      );
      return resp.data;
    } catch (e) {
      logger.e('Error uploading kitty image: $e');
      rethrow;
    }
  }

  /// Pick image from gallery
  Future<XFile?> pickImageFromGallery() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 2048,
        maxHeight: 2048,
      );
      return image;
    } catch (e) {
      logger.e('Error picking image: $e');
      rethrow;
    }
  }

  /// Validate image file
  Map<String, dynamic> validateImageFile(String filePath, double maxSizeMB) {
    final String mimeType = lookupMimeType(filePath) ?? '';
    if (!mimeType.startsWith('image/')) {
      return {'valid': false, 'error': 'Please select an image file'};
    }
    return {'valid': true};
  }

  /// Get file size in MB
  Future<double> getFileSizeInMB(String filePath) async {
    final fileSize = await File(filePath).length();
    return fileSize / (1024 * 1024);
  }

  /// Delete media
  Future<bool> deleteMedia(int mediaId) async {
    try {
      var res = await apiProvider.request(
        url: "${ApiUrls.create_kitty_media}$mediaId",
        method: Method.DELETE,
      );
      return res.data["status"] ?? false;
    } catch (e) {
      logger.e('Error deleting media: $e');
      return false;
    }
  }

  /// Update kitty
  Future<Map<String, dynamic>> updateKitty({required CreateKitPayload request}) async {
    try {
      var resp = await apiProvider.request(
        url: ApiUrls.update_kitty,
        method: Method.POST,
        params: request.toJson(),
      );
      return resp.data;
    } catch (e) {
      logger.e('Error updating kitty: $e');
      rethrow;
    }
  }

  /// Update end date
  Future<Map<String, dynamic>> updateEndDate({
    required DateTime newDate,
    required int kittyId,
  }) async {
    try {
      var resp = await apiProvider.request(
        url: ApiUrls.updateEndDate,
        method: Method.POST,
        params: {
          "kitty_id": kittyId,
          "end_date": newDate.toIso8601String().removeAllWhitespace
        },
      );
      return resp.data;
    } catch (e) {
      logger.e('Error updating end date: $e');
      rethrow;
    }
  }

  /// Withdraw request
  Future<Map<String, dynamic>> withdrawRequest({
    required String amount,
    required int kittyId,
    required bool isconfirm,
    required int benficiaryId,
    required String remarks,
  }) async {
    try {
      var resp = await apiProvider.request(
        url: isconfirm ? ApiUrls.withdrawConfirm : ApiUrls.withdrawRequest,
        method: Method.POST,
        params: {
          "beneficiary_id": benficiaryId,
          "phone_number": getLocalUser()?.phoneNumber ?? "",
          "kitty_id": kittyId.toString(),
          "amount": amount,
          "reason": remarks
        },
      );
      return resp.data;
    } catch (e) {
      logger.e('Error processing withdraw request: $e');
      rethrow;
    }
  }

  /// Join group
  Future<Map<String, dynamic>> joinGroup({
    required int id,
    required String link,
  }) async {
    try {
      var res = await apiProvider.request(
        url: ApiUrls.join_group,
        method: Method.POST,
        params: {"kitty_id": id, "group_link": link},
      );
      return res.data;
    } catch (e) {
      logger.e('Error joining group: $e');
      rethrow;
    }
  }

  /// Get kitty contributions
  Future<Map<String, dynamic>> getKittyContributions({
    required int kittyId,
    int? page = 0,
    int? size = 20,
    int? eventId,
  }) async {
    try {
      var resp = await apiProvider.request(
        url: eventId != null
            ? "${ApiUrls.EVENTTRANSACTIONS}?event_id=$eventId&size=$size&page=$page"
            : "${ApiUrls.contribsTransactions}?kitty_id=$kittyId&page=$page&size=$size",
        method: Method.GET,
      );
      return resp.data;
    } catch (e) {
      logger.e('Error fetching contributions: $e');
      rethrow;
    }
  }

  /// Share kitty transactions
  Future<Map<String, dynamic>> shareKittyTrans({required int id}) async {
    try {
      var res = await apiProvider.request(
        url: '${ApiUrls.getKittyText}/$id',
        method: Method.GET,
      );
      return res.data;
    } catch (e) {
      logger.e('Error sharing kitty transactions: $e');
      rethrow;
    }
  }

  /// Get kitty categories
  Future<List<KittyCategoriesModel>> getKittyCategories() async {
    try {
      final response = await apiProvider.request(
        url: ApiUrls.kitty_categories,
        method: Method.GET,
      );
      if (response.data['status'] ?? false) {
        final returnedData = response.data['data']['categories'] as List;
        return returnedData
            .map((item) => KittyCategoriesModel.fromJson(item))
            .toList();
      }
      return [];
    } catch (e) {
      logger.e('Error fetching kitty categories: $e');
      rethrow;
    }
  }

  /// Get network code
  int? getNetworkCode({required String networkTitle}) {
    String netw = networkTitle
        .replaceAll(" ", "")
        .replaceAll("-", "")
        .replaceAll(".", "")
        .trim()
        .toLowerCase();

    switch (netw) {
      case "sasapay":
        return 0;
      case "mpesa":
        return 63902;
      case "airtelmoney":
        return 63903;
      case "visa":
        return 55;
      default:
        return 63902;
    }
  }

  /// Get network name
  String getNetworkName({required String code}) {
    switch (code) {
      case "0":
        return "Sasapay";
      case "63902":
        return "M-Pesa";
      case "63903":
        return "Airtel Money";
      case "63907":
        return "T-kash";
      default:
        return "UNKNOWN";
    }
  }
}