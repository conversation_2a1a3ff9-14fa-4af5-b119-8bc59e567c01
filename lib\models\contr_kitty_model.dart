import 'dart:convert';
import 'package:onekitty_admin/models/kitty_model.dart';
class KittModel {
  bool? status;
  String? message;
  KittyData? data;

  KittModel({
    this.status,
    this.message,
    this.data,
  });

  factory KittModel.fromJson(Map<String, dynamic>? json) {
    if (json == null) return KittModel();
    return KittModel(
      status: json["status"],
      message: json["message"],
      data: KittyData.fromJson(json["data"]),
    );
  }

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data?.toJson(),
      };
}

class KittyData {
  Kitty? kitty;
  String? kittyStatus;
  ReferredBy? referredBy;
  num? volumes;

  KittyData({
    this.kitty,
    this.kittyStatus,
    this.referredBy,
    this.volumes,
  });

  factory KittyData.fromJson(Map<String, dynamic>? json) {
    if (json == null) return KittyData();
    return KittyData(
      kitty: Kitty.fromJson(json["kitty"]),
      kittyStatus: json["kitty_status"],
      referredBy: ReferredBy.fromJson(json["referred_by"]),
      volumes: json["volumes"],
    );
  }

  Map<String, dynamic> toJson() => {
        "kitty": kitty?.toJson(),
        "kitty_status": kittyStatus,
        "referred_by": referredBy?.toJson(),
        "volumes": volumes,
      };
}
class ReferredBy {
  int? id;
  DateTime? createdAt;
  DateTime? updatedAt;
  dynamic deletedAt;
  int? userId;
  String? merchantName;
  int? merchantCode;
  double? merchantPercent;

  ReferredBy({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.userId,
    this.merchantName,
    this.merchantCode,
    this.merchantPercent,
  });

  factory ReferredBy.fromJson(Map<String, dynamic>? json) {
    if (json == null) return ReferredBy();
    return ReferredBy(
      id: json["ID"],
      createdAt:
          json["CreatedAt"] != null ? DateTime.parse(json["CreatedAt"]) : null,
      updatedAt:
          json["UpdatedAt"] != null ? DateTime.parse(json["UpdatedAt"]) : null,
      deletedAt: json["DeletedAt"],
      userId: json["UserID"],
      merchantName: json["merchant_name"],
      merchantCode: json["merchant_code"],
      merchantPercent: json["merchant_percent"]?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt?.toIso8601String(),
        "UpdatedAt": updatedAt?.toIso8601String(),
        "DeletedAt": deletedAt,
        "UserID": userId,
        "merchant_name": merchantName,
        "merchant_code": merchantCode,
        "merchant_percent": merchantPercent,
      };
}

class Notifications {
  int? id;
  String? whatsappLink;
  String? whatsappGroupName;
  String? email;
  String? whatsappStatus;
  String? whatsAppProfile;

  Notifications({
    this.id,
    this.whatsappLink,
    this.whatsappGroupName,
    this.email,
    this.whatsappStatus,
     this.whatsAppProfile,
  });

  factory Notifications.fromJson(Map<String, dynamic> json) => Notifications(
        id: json["Id"],
        whatsappLink: json["whatsapp_link"],
        whatsappGroupName: json["whatsapp_group_name"],
        email: json["email"],
        whatsappStatus: json["whatsapp_status"],
        whatsAppProfile:json["whatsapp_profile"] 
      );

  Map<String, dynamic> toJson() => {
        "Id": id,
        "whatsapp_link": whatsappLink,
        "whatsapp_group_name": whatsappGroupName,
        "email": email,
        "whatsapp_status": whatsappStatus,
        "whatsapp_profile":whatsAppProfile 
      };
}

PaymentModel paymentModelFromJson(String str) =>
    PaymentModel.fromJson(json.decode(str));

String paymentModelToJson(PaymentModel data) => json.encode(data.toJson());

class PaymentModel {
  bool? status;
  String? message;
  Data? data;

  PaymentModel({
    this.status,
    this.message,
    this.data,
  });

  factory PaymentModel.fromJson(Map<String, dynamic> json) => PaymentModel(
        status: json["status"],
        message: json["message"],
        data: json["data"] != null ? Data.fromJson(json["data"]) : null,
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data?.toJson(),
      };
}

class Data {
  String? checkoutRequestId;
  String? customerMessage;
  String? detail;
  String? paymentGateway;
  String? responseDescription;
  SocialMessages? socialMessages;

  Data({
    this.checkoutRequestId,
    this.customerMessage,
    this.detail,
    this.paymentGateway,
    this.responseDescription,
    this.socialMessages,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        checkoutRequestId: json["checkout_request_id"],
        customerMessage: json["customer_message"],
        detail: json["detail"],
        paymentGateway: json["payment_gateway"],
        responseDescription: json["response_description"],
        socialMessages: json["social_messages"] != null
            ? SocialMessages.fromJson(json["social_messages"])
            : null,
      );

  Map<String, dynamic> toJson() => {
        "checkout_request_id": checkoutRequestId,
        "customer_message": customerMessage,
        "detail": detail,
        "payment_gateway": paymentGateway,
        "response_description": responseDescription,
        "social_messages": socialMessages?.toJson(),
      };
}

class SocialMessages {
  int? id;
  DateTime? createdAt;
  DateTime? updatedAt;
  dynamic deletedAt;
  String? facebook;
  String? tiktok;
  String? instagram;
  String? youtube;
  String? twitter;

  SocialMessages({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.facebook,
    this.tiktok,
    this.instagram,
    this.youtube,
    this.twitter,
  });

  factory SocialMessages.fromJson(Map<String, dynamic> json) => SocialMessages(
        id: json["ID"],
        createdAt: json["CreatedAt"] != null
            ? DateTime.parse(json["CreatedAt"])
            : null,
        updatedAt: json["UpdatedAt"] != null
            ? DateTime.parse(json["UpdatedAt"])
            : null,
        deletedAt: json["DeletedAt"],
        facebook: json["facebook"],
        tiktok: json["tiktok"],
        instagram: json["instagram"],
        youtube: json["youtube"],
        twitter: json["twitter"],
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt?.toIso8601String(),
        "UpdatedAt": updatedAt?.toIso8601String(),
        "DeletedAt": deletedAt,
        "facebook": facebook,
        "tiktok": tiktok,
        "instagram": instagram,
        "youtube": youtube,
        "twitter": twitter,
      };
}
