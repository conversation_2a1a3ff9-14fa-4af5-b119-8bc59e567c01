
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onekitty_admin/features/onekitty/controllers/config.dart';
import 'package:onekitty_admin/helpers/colors.dart';
import 'package:onekitty_admin/helpers/extensions/text_styles.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/home/<USER>/message.dart';
import 'package:chat_gpt_sdk/chat_gpt_sdk.dart';

class ChatGptPage extends StatefulWidget {
  const ChatGptPage({super.key});

  @override
  State<ChatGptPage> createState() => _ChatGptPageState();
}

class _ChatGptPageState extends State<ChatGptPage> {
  final TextEditingController _textController = TextEditingController();
  final FocusNode _textFieldFocus = FocusNode();
  ConfigController configController = Get.put(ConfigController());
  late OpenAI openAI;
  final List<Message> _messages = [];
  bool _isLoading = false;
  
  @override
  void initState() {
    openAI = OpenAI.instance.build(
      token: configController.gpt.value,
      baseOption: HttpSetup(receiveTimeout: const Duration(seconds: 30)),
    );
    super.initState();
  }

  void sendMessage() async {
    var text = _textController.text.trim();
    if (text.isEmpty || _isLoading) {
      return;
    }
    
    _textController.clear();
    setState(() {
      _messages.add(Message(isUser: true, text: text));
      _isLoading = true;
    });

    try {
      final request = ChatCompleteText(
        messages: [
          {"role": Role.user, "content": text},
        ],
        maxToken: 200,
        model: GptTurbo0301ChatModel(),
      );

      final response = await openAI.onChatCompletion(request: request);
      
      if (response != null && response.choices.isNotEmpty) {
        final botMessage = response.choices.first.message?.content ?? 'No response';
        setState(() {
          _messages.add(Message(isUser: false, text: botMessage));
        });
      }
    } catch (e) {
      setState(() {
        _messages.add(Message(isUser: false, text: 'Error: ${e.toString()}'));
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final textFieldDecoration = InputDecoration(
      contentPadding: const EdgeInsets.all(15),
      hintText: 'Enter a prompt...',
      border: OutlineInputBorder(
        borderRadius: const BorderRadius.all(
          Radius.circular(14),
        ),
        borderSide: BorderSide(
          color: Theme.of(context).colorScheme.secondary,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: const BorderRadius.all(
          Radius.circular(14),
        ),
        borderSide: BorderSide(
          color: Theme.of(context).colorScheme.secondary,
        ),
      ),
    );
    return Column(
      children: [
        Expanded(
            child: ListView.builder(
                itemCount: _messages.length + (_isLoading ? 1 : 0),
                itemBuilder: (context, index) {
                  if (index == _messages.length && _isLoading) {
                    return Container(
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surfaceVariant,
                        borderRadius: const BorderRadius.only(
                          topRight: Radius.circular(15),
                          bottomLeft: Radius.circular(15),
                          bottomRight: Radius.circular(15),
                        ),
                      ),
                      padding: const EdgeInsets.symmetric(
                        vertical: 15,
                        horizontal: 20,
                      ),
                      margin: const EdgeInsets.only(bottom: 8),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                AppColors.slate,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Thinking...',
                            style: context.dividerTextLarge
                                ?.copyWith(color: AppColors.slate),
                          ),
                        ],
                      ),
                    );
                  }
                  
                  var message = _messages[index];
                  return Container(
                      decoration: BoxDecoration(
                        color: message.isUser
                            ? AppColors.blueButtonColor
                            : Theme.of(context).colorScheme.surfaceVariant,
                        borderRadius: BorderRadius.only(
                            topLeft: message.isUser
                                ? const Radius.circular(15)
                                : Radius.zero,
                            bottomLeft: const Radius.circular(15),
                            topRight: message.isUser
                                ? Radius.zero
                                : const Radius.circular(15),
                            bottomRight: const Radius.circular(15)),
                      ),
                      padding: const EdgeInsets.symmetric(
                        vertical: 15,
                        horizontal: 20,
                      ),
                      margin: const EdgeInsets.only(bottom: 8),
                      child: Text(
                        message.text,
                        style: context.dividerTextLarge
                            ?.copyWith(color: AppColors.slate),
                      ));
                })),
        Padding(
          padding: const EdgeInsets.symmetric(
            vertical: 25,
            horizontal: 15,
          ),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  autofocus: true,
                  focusNode: _textFieldFocus,
                  decoration: textFieldDecoration,
                  controller: _textController,
                  onSubmitted: (_) => sendMessage(),
                  enabled: !_isLoading,
                ),
              ),
              const SizedBox.square(dimension: 15),
              IconButton(
                onPressed: () {
                  sendMessage();
                },
                icon: const Icon(
                  Icons.send,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
