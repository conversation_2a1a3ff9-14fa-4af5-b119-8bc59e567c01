import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty_admin/features/login/screens/login_screen.dart';
import 'package:onekitty_admin/features/updateKYC/views/kyc_home.dart';
import 'package:onekitty_admin/main.dart' as main;
import 'package:get_storage/get_storage.dart';
import 'package:insta_image_viewer/insta_image_viewer.dart';
import 'package:onekitty_admin/features/onekitty/controllers/config.dart';
import 'package:onekitty_admin/features/onekitty/controllers/user_controller.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/controllers/user_ktty_controller.dart';
import 'package:onekitty_admin/helpers/colors.dart';
import 'package:onekitty_admin/helpers/extensions/text_styles.dart';
import 'package:onekitty_admin/helpers/show_toast.dart';
import 'package:onekitty_admin/models/auth/user_model.dart';
import 'package:onekitty_admin/nav_routes/nav_routes.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/home/<USER>/in_app_browser.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/profile/edit_profile.dart';
import 'package:onekitty_admin/services/auth_manager.dart';
import 'package:onekitty_admin/services/language_service.dart';
import 'package:onekitty_admin/utils/alert_dialog.dart';
import 'package:onekitty_admin/utils/cache_keys.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:image_picker/image_picker.dart';

import '../../../../../../../../utils/utils_exports.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  final UserKittyController userModel = Get.put(UserKittyController());
  final UserController _userController = Get.put(UserController());
  final ConfigController configController = Get.put(ConfigController());
  GetStorage box = Get.find();

  late bool allowsBiometrics;

  String? fileName;
  Uint8List? _image;
  pickImage() async {
    try {
      final ImagePicker _imagePicker = ImagePicker();
      final XFile? _file = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 2048,
        maxHeight: 2048,
      );

      if (_file != null) {
        Uint8List bytes = await _file.readAsBytes();
        setState(() {
          _image = bytes;
          fileName = '${_userController.getLocalUser()!.id}__${_file.name}';
        });
        String photoUrl =
            await _userController.uploadMediaprofile(_image!, fileName ?? "");
        bool result = await updateProfile(photoUrl: photoUrl);
        print("Update Profile Result: $result");
        print("Photo URL: $photoUrl");
        await userModel.getUser();
        return bytes;
      } else {
        return "No image picked";
      }
    } catch (e) {
      print("Error picking image: $e");
      return "Error picking image";
    }
  }

  UserModelLatest? get user => userModel.getLocalUser();
  String _version = "Loading...";

  @override
  void initState() {
    super.initState();
    _getAppVersion();
    allowsBiometrics = box.read(CacheKeys.allowsBiometric) ?? false;

    configController.getConfig2();
  }

  void showHelpSupportNumbers() {
    showModalBottomSheet(
        context: context,
        builder: (context) {
          return const ConfigKeysBottomSheet();
        });
  }

  Future<void> _getAppVersion() async {
    final packageInfo = await PackageInfo.fromPlatform();
    setState(() {
      _version = packageInfo.version;
    });
  }

  void _rateApp(String url) async {
    if (Platform.isIOS) {
      // iOS App Store URL
      url;
    } else if (Platform.isAndroid) {
      // Google Play Store URL
      url;
    }

    if (!await launchUrl(Uri.parse(url))) {
      ToastUtils.showErrorToast(context, 'could_not_launch_url'.tr, 'error'.tr);
    } else {
      // Handle the error
      print('could_not_launch_url_with_param'.tr.replaceAll('{url}', url));
    }
  }

  @override
  dispose() {
    _image = null;
    super.dispose();
  }

  onInviteFriends(String storeUrl) async {
    await Share.share(
        "Hey, are you worried about your financial contributions? Have a look at Onekitty $storeUrl easy and secure platform to manage your contributions.",
        //"Hey, have a look at this awesome app Onekitty, that automates contributions $storeUrl",
        subject: 'app_name'.tr);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      //appBar: buildAppBar(context),
      body: Container(
        margin: const EdgeInsets.symmetric(horizontal: 30, vertical: 15),
        child: SingleChildScrollView(
          child: Column(
            children: [
              const RowAppBar(),
              Stack(
                children: [
                  _image != null
                      ? CircleAvatar(
                          radius: 60,
                          backgroundImage: MemoryImage(_image!),
                          backgroundColor: AppColors.primary.withOpacity(0.4),
                        )
                      : GetBuilder<UserKittyController>(
                          builder: (UserKittyController user) {
                            if (user.isloadingUser.isTrue ||
                                _userController.isUpdateProfile.isTrue) {
                              return const Center(
                                child: CircularProgressIndicator(),
                              );
                            }
                            return InstaImageViewer(
                              child: CircleAvatar(
                                radius: 60,
                                backgroundColor:
                                    AppColors.primary.withOpacity(0.4),
                                backgroundImage: _userController
                                                .getLocalUser()!
                                                .profileUrl !=
                                            null &&
                                        _userController
                                            .getLocalUser()!
                                            .profileUrl!
                                            .isNotEmpty
                                    ? NetworkImage(_userController
                                        .getLocalUser()!
                                        .profileUrl!)
                                    : null,
                                child: (_userController
                                                .getLocalUser()
                                                ?.profileUrl !=
                                            null &&
                                        _userController
                                            .getLocalUser()!
                                            .profileUrl!
                                            .isNotEmpty)
                                    ? null
                                    : Padding(
                                        padding: const EdgeInsets.all(16.0),
                                        child: Text(
                                          "${_userController.getLocalUser()?.firstName ?? ' '} ${_userController.getLocalUser()?.secondName ?? ''}",
                                          style: context.dividerTextLarge
                                              ?.copyWith(
                                                  color: AppColors.primary,
                                                  fontSize: 30),
                                        ),
                                      ),
                              ),
                            );
                          },
                        ),
                  Positioned(
                    bottom: -7,
                    right: 1,
                    child: IconButton(
                      onPressed: pickImage,
                      icon: Container(
                        //padding: EdgeInsets.all(8),
                        decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8)),
                        child: const Center(
                          child: Icon(
                            Icons.photo_camera_outlined,
                            color: AppColors.mainPurple,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              GetBuilder<UserKittyController>(
                builder: (UserKittyController usercontroller) {
                  if (usercontroller.isloadingUser.isTrue) {
                    return Text('loading'.tr);
                  }
                  return Column(
                    children: [
                      Text(
                        "${_userController.getLocalUser()?.firstName} ${_userController.getLocalUser()?.secondName}",
                        style: context.dividerTextLarge?.copyWith(
                            color: AppColors.primary,
                            fontSize: 16,
                            fontWeight: FontWeight.bold),
                      ),
                      Text("${_userController.getLocalUser()?.email}"),
                      Text("${_userController.getLocalUser()?.phoneNumber}"),
                    ],
                  );
                },
              ),
              ValueListenableBuilder(
                valueListenable: main.isLight,
                builder: (context, isLight, _) => Container(
                  margin: const EdgeInsets.only(bottom: 10, top: 12),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                      color: isLight ? Colors.white : Colors.grey[900],
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                            color:
                                isLight ? Colors.grey.shade200 : Colors.black54,
                            offset: const Offset(1, 2),
                            blurRadius: 2,
                            spreadRadius: 2)
                      ]),
                  child: Column(
                    children: [
                      ProfileCard(
                        text: 'edit_profile'.tr,
                        imgPath: AssetUrl.editProfile,
                        onPressed: () {
                          Get.toNamed(NavRoutes.updateProfile);
                        },
                      ),

                      ProfileCard(
                        text: 'referrals'.tr,
                        imgPath: AssetUrl.invite,
                        onPressed: () {
                          Get.toNamed(NavRoutes.referrals);
                        },
                      ),
                      ProfileCard(
                        text: 'help_support'.tr,
                        imgPath: AssetUrl.helpSupport,
                        onPressed: showHelpSupportNumbers,
                      ),
                      Obx(() {
                        final storeUrl =
                            "https://${configController.storeUrl.value}";
                        return Padding(
                          padding: const EdgeInsets.all(4.0),
                          child: InkWell(
                            onTap: () {
                              onInviteFriends(storeUrl);
                            },
                            child: Row(
                              children: [
                                const CustomImageView(
                                  imagePath: AssetUrl.invite,
                                ),
                                const SizedBox(
                                  width: 12,
                                ),
                                Text('invite_friends'.tr),
                                const Spacer(),
                                IconButton(
                                    onPressed: () {
                                      onInviteFriends(storeUrl);
                                    },
                                    icon: const Icon(
                                        Icons.keyboard_arrow_right_rounded))
                              ],
                            ),
                          ),
                        );
                      }),
                      // if(kDebugMode)
                      Padding(
                        padding: const EdgeInsets.all(4.0),
                        child: InkWell(
                          onTap: () {
                            Get.to(() => const KYCHomePage());
                          },
                          child: Row(
                            children: [
                              const CustomImageView(
                                imagePath: AssetUrl.verified,
                              ),
                              const SizedBox(
                                width: 12,
                              ),
                              Text('get_verified'.tr),
                              const Spacer(),
                              IconButton(
                                  onPressed: () {
                                    Get.to(() => const KYCHomePage());
                                  },
                                  icon: const Icon(
                                      Icons.keyboard_arrow_right_rounded))
                            ],
                          ),
                        ),
                      ),

                      Padding(
                        padding: const EdgeInsets.only(left: 4.0),
                        child: Row(
                          children: [
                            const CustomImageView(
                              imagePath: AssetUrl.logout2,
                            ),
                            Expanded(
                              child: SwitchListTile(
                                title: Text('allow_biometric_login'.tr),
                                value: allowsBiometrics,
                                onChanged: (bool value) {
                                  setState(() {
                                    allowsBiometrics = value;
                                  });
                                  AuthenticationManager authenticationManager =
                                      Get.find();
                                  authenticationManager.bioPrefs(value);
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(left: 4.0),
                        child: Row(
                          children: [
                            Container(
                              padding: EdgeInsets.all(6.spMin),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8.spMin),
                                  color: const Color(0xffe1e7f5)),
                              child: const Icon(
                                Icons.language,
                                color: Color(0xff415ab8),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('language'.tr),
                                  DropdownButton<String>(
                                    value: box.read(CacheKeys.language) ?? 'en',
                                    isExpanded: true,
                                    underline: Container(),
                                    items: const [
                                      DropdownMenuItem(value: 'en', child: Text('English')),
                                      DropdownMenuItem(value: 'sw', child: Text('Kiswahili')),
                                      DropdownMenuItem(value: 'fr', child: Text('Français')),
                                      DropdownMenuItem(value: 'es', child: Text('Español')),
                                      DropdownMenuItem(value: 'de', child: Text('Deutsch')),
                                    ],
                                    onChanged: (String? value) async {
                                      if (value != null) {
                                        final mainState = context.findAncestorStateOfType<main.MyMaterialAppSuperState>();
                                        if (mainState != null) {
                                          Get.find<LanguageService>().changeLanguage(value);
                                          
                                          setState(() {});
                                        }
                                      }
                                    },
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(left: 4.0),
                        child: Row(
                          children: [
                            Container(
                              padding: EdgeInsets.all(6.spMin),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8.spMin),
                                  color: const Color(0xffe1e7f5)),
                              child: const Icon(
                                Icons.dark_mode_outlined,
                                color: Color(0xff415ab8),
                              ),
                            ),
                            Expanded(
                              child: SwitchListTile(
                                title: Text('dark_mode'.tr),
                                value: isLight ? false : true,
                                onChanged: (bool value) {
                                  setState(() {
                                    main.isLight.value = !value;
                                    Get.changeThemeMode(!value
                                        ? ThemeMode.light
                                        : ThemeMode.dark);
                                  });
                                  box.write(CacheKeys.isLight, !value);

                                  // main.MyMaterialAppSuper.restartApp(context);
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                      Obx(() {
                        final storeUrl =
                            "https://${configController.storeUrl.value}";
                        return Padding(
                          padding: const EdgeInsets.all(4.0),
                          child: InkWell(
                            onTap: () {
                              _rateApp(storeUrl);
                            },
                            child: Row(
                              children: [
                                const CustomImageView(
                                  imagePath: AssetUrl.rate,
                                ),
                                const SizedBox(
                                  width: 12,
                                ),
                                Text('rate_our_app'.tr),
                                const Spacer(),
                                IconButton(
                                    onPressed: () {
                                      _rateApp(storeUrl);
                                    },
                                    icon: const Icon(
                                        Icons.keyboard_arrow_right_rounded))
                              ],
                            ),
                          ),
                        );
                      })
                    ],
                  ),
                ),
              ),
              Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    'more'.tr,
                    style: context.titleText,
                  )),
              ProfileCard(
                  text: 'terms_and_conditions'.tr,
                  imgPath: AssetUrl.termsAndConditions,
                  onPressed: () {
                    Get.to(() => WebViewCustom(
                          url: "https://www.onekitty.co.ke/terms",
                          isBackDrop: false,
                        ));
                  }),
              ProfileCard(
                  text: 'privacy_policy'.tr,
                  imgPath: AssetUrl.privacyPolicy,
                  onPressed: () {
                    Get.to(() => WebViewCustom(
                          url: "https://www.onekitty.co.ke/privacy-policy",
                          isBackDrop: false,
                        ));
                    //Get.to(() => PrivacyPolicy());
                  }),
              ProfileCard(
                  text: 'log_out'.tr,
                  imgPath: AssetUrl.logout2,
                  onPressed: () {
                    showConfirmDialog(
                      context,
                      function: () {
                        AuthenticationManager authenticationManager =
                            Get.find();
                        authenticationManager.logOut();
                        Get.offAll(
                          () => const LoginScreen(),
                          transition: Transition.leftToRightWithFade,
                        );
                      },
                      title: 'are_you_certain_exit_app'.tr,
                    );
                  }),
              Text(
                "v$_version",
                style: context.dividerTextLarge?.copyWith(
                    fontStyle: FontStyle.italic, fontWeight: FontWeight.bold),
              )
            ],
          ),
        ),
      ),
    );
  }
}

class ProfileCard extends StatelessWidget {
  final String text;
  final String imgPath;
  final Function()? onPressed;
  const ProfileCard(
      {super.key,
      required this.text,
      required this.imgPath,
      required this.onPressed});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(4.0),
      child: InkWell(
        onTap: onPressed,
        child: Row(
          children: [
            CustomImageView(
              imagePath: imgPath,
            ),
            const SizedBox(
              width: 12,
            ),
            Text(text),
            const Spacer(),
            IconButton(
                onPressed: onPressed,
                icon: const Icon(Icons.keyboard_arrow_right_rounded))
          ],
        ),
      ),
    );
  }
}

class ConfigKeysBottomSheet extends StatefulWidget {
  const ConfigKeysBottomSheet({super.key});

  @override
  State<ConfigKeysBottomSheet> createState() => _ConfigKeysBottomSheetState();
}

class _ConfigKeysBottomSheetState extends State<ConfigKeysBottomSheet> {
  final ConfigController configController = Get.put(ConfigController());
  final UserKittyController userController = Get.put(UserKittyController());

  void _launchEmail() async {
    final Uri emailUri =
        Uri(scheme: 'mailto', path: 'support_email'.tr, queryParameters: {
      'subject': 'email_subject_onekitty'.tr,
      'body':
          'Hello,I am${userController.user.value.firstName ?? ''} ${userController.user.value.secondName ?? ''}.'
              .replaceAll("+", '')
    });

    if (!await launchUrl(emailUri)) {
      ToastUtils.showErrorToast(context, 'could_not_launch_url'.tr, 'error'.tr);
    } else {
      // Handle the error
      print('could_not_launch_email'.tr.replaceAll('{email}', emailUri.toString()));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
          margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 20),
          child: SingleChildScrollView(
            child: Column(
              children: [
                Text(
                  'get_in_touch'.tr,
                  style: context.titleText?.copyWith(fontSize: 20),
                ),
                const SizedBox(
                  height: 12,
                ),
                  Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 12.0),
                  child: Text(
                    'inquiries_get_in_touch_assist'.tr,
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
                InkWell(
                  onTap: () {
                    configController.getConfig2();
                    Get.toNamed(NavRoutes.tawk);
                  },
                  child: Container(
                    width: 102.w,
                    height: 60.h,
                    margin:
                        const EdgeInsets.symmetric(horizontal: 2, vertical: 3),
                    padding: EdgeInsets.symmetric(
                      horizontal: 6.w,
                      vertical: 4.h,
                    ),
                    decoration: AppDecoration.outlineIndigo.copyWith(
                      borderRadius: BorderRadiusStyle.roundedBorder8,
                    ),
                    child: Center(
                      child: FittedBox(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Image.asset(
                              AssetUrl.chat,
                              height: 50,
                            )
                                .animate(
                                  onComplete: (controller) => controller.repeat(
                                      period:
                                          const Duration(milliseconds: 3000)),
                                )
                                .shake(
                                  duration: const Duration(milliseconds: 1000),
                                  delay: const Duration(milliseconds: 1000),
                                  curve: Curves.bounceInOut,
                                )
                                .shimmer(
                                    duration: const Duration(milliseconds: 500),
                                    curve: Curves.bounceInOut,
                                    color: Colors.white),
                            Text(
                              'chat_with_us'.tr,
                              style: context.dividerTextLarge,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
                Obx(() {
                  List<String> phoneNumberList =
                      configController.phoneNumbers.split(',');
                  List<String> whatsappNumberList =
                      configController.whatsappNumbers.split(',');
                  final phoneNumber = phoneNumberList[0];
                  final phoneNumber2 = phoneNumberList[1];
                  final whatsappNo = whatsappNumberList[0];
                  final whatsapp2 = whatsappNumberList[1];
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'phone_numbers_you_can_call'.tr,
                        style: context.dividerTextLarge,
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      Row(
                        children: [
                          Expanded(
                              child: OutlinedButton.icon(
                            onPressed: () async {
                              if (Platform.isIOS) {
                                phoneNumber;
                              } else if (Platform.isAndroid) {
                                phoneNumber;
                              }
                              if (!await launchUrl(
                                  Uri.parse("tel://+$phoneNumber"))) {
                                ToastUtils.showErrorToast(context,
                                    "Could not launch the url", "Error");
                              } else {
                                throw 'could_not_launch_phone'.tr.replaceAll('{phone}', phoneNumber);
                              }
                            },
                            icon: const CustomImageView(
                              height: 50,
                              imagePath: AssetUrl.call,
                            ),
                            label: Text(
                              phoneNumber,
                              style: context.dividerTextSmall
                                  ?.copyWith(color: AppColors.blueButtonColor),
                            ),
                          )
                              // child: InkWell(
                              //   onTap: () async {
                              //     if (Platform.isIOS) {
                              //       phoneNumber;
                              //     } else if (Platform.isAndroid) {
                              //       phoneNumber;
                              //     }
                              //     if (!await launchUrl(
                              //         Uri.parse("tel://+${phoneNumber}"))) {
                              //       ToastUtils.showErrorToast(context,
                              //           "Could not launch the url", "Error");
                              //     } else {
                              //       throw 'Could not launch $phoneNumber';
                              //     }
                              //   },
                              //   child: Container(
                              //     decoration: BoxDecoration(
                              //         border: Border.all(
                              //             color: AppColors.greyTextColor),
                              //         borderRadius: BorderRadius.circular(20)),
                              //     child: Row(
                              //       mainAxisSize: MainAxisSize.min,
                              //       children: [
                              //         CustomImageView(
                              //           height: 50,
                              //           imagePath: AssetUrl.call,
                              //         ),
                              //         SizedBox(
                              //           width: 5,
                              //         ),
                              //         SelectableText(
                              //           phoneNumber,
                              //           showCursor: true,
                              //           style: context.dividerTextLarge?.copyWith(
                              //               color: AppColors.blueButtonColor),
                              //         ),
                              //       ],
                              //     ),
                              //   ),
                              // ),
                              ),
                          const SizedBox(
                            width: 5,
                          ),
                          Expanded(
                              child: OutlinedButton.icon(
                            onPressed: () async {
                              if (Platform.isIOS) {
                                phoneNumber;
                              } else if (Platform.isAndroid) {
                                phoneNumber;
                              }
                              final Uri uri =
                                  Uri(scheme: "tel", path: "+$phoneNumber2");
                              if (!await launchUrl(uri
                                  //Uri.parse("tell://${phoneNumber2}")
                                  )) {
                                ToastUtils.showErrorToast(context,
                                    "Could not launch the url", "Error");
                              } else {
                                throw 'could_not_launch_phone'.tr.replaceAll('{phone}', phoneNumber2);
                              }
                            },
                            icon: const CustomImageView(
                              height: 50,
                              imagePath: AssetUrl.call,
                            ),
                            label: Text(
                              phoneNumber2,
                              style: context.dividerTextSmall
                                  ?.copyWith(color: AppColors.blueButtonColor),
                            ),
                          )
                              // child: InkWell(
                              //   onTap: () async {
                              //     if (Platform.isIOS) {
                              //       phoneNumber;
                              //     } else if (Platform.isAndroid) {
                              //       phoneNumber;
                              //     }
                              //     final Uri uri = Uri(
                              //         scheme: "tel", path: "+${phoneNumber2}");
                              //     if (!await launchUrl(uri
                              //         //Uri.parse("tell://${phoneNumber2}")
                              //         )) {
                              //       ToastUtils.showErrorToast(context,
                              //           "Could not launch the url", "Error");
                              //     } else {
                              //       throw 'Could not launch $phoneNumber2';
                              //     }
                              //   },
                              //   child: Container(
                              //     decoration: BoxDecoration(
                              //         border: Border.all(
                              //             color: AppColors.greyTextColor),
                              //         borderRadius: BorderRadius.circular(20)),
                              //     child: Row(
                              //       mainAxisSize: MainAxisSize.min,
                              //       children: [
                              //         CustomImageView(
                              //           height: 50,
                              //           imagePath: AssetUrl.call,
                              //         ),
                              //         SizedBox(
                              //           width: 5,
                              //         ),
                              //         SelectableText(
                              //           phoneNumber2,
                              //           showCursor: true,
                              //           style: context.dividerTextLarge?.copyWith(
                              //               color: AppColors.blueButtonColor),
                              //         ),
                              //       ],
                              //     ),
                              //   ),
                              // ),
                              ),
                        ],
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      Text(
                        'whatsapp_numbers_reach_out'.tr,
                        style: context.dividerTextLarge,
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      Row(
                        children: [
                          Expanded(
                              child: OutlinedButton.icon(
                                  onPressed: () async {
                                    final Uri url = Uri.parse(
                                        "https://wa.me/$whatsappNo?text=Hello, my name is ${userController.user.value.firstName} ${userController.user.value.secondName}");
                                    if (!await launchUrl(url)) {
                                      ToastUtils.showErrorToast(context,
                                          "Could not launch the url", "Error");
                                    } else {
                                      throw 'could_not_launch_url_with_param'.tr.replaceAll('{url}', whatsappNo);
                                    }
                                  },
                                  icon: Image.asset(
                                    AssetUrl.whatsapp,
                                    height: 50,
                                  ),
                                  label: Text(
                                    whatsappNo,
                                    style: context.dividerTextSmall?.copyWith(
                                        color: AppColors.blueButtonColor),
                                  ))
                              // child: InkWell(
                              //   onTap: () async {
                              //     final Uri url = Uri.parse(
                              //         "https://wa.me/${whatsappNo}?text=Hello, my name is ${userController.user.value.firstName} ${userController.user.value.secondName}");
                              //     if (!await launchUrl(url)) {
                              //       ToastUtils.showErrorToast(context,
                              //           "Could not launch the url", "Error");
                              //     } else {
                              //       throw 'Could not launch $whatsappNo';
                              //     }
                              //   },
                              //   child: Container(
                              //     padding: EdgeInsets.symmetric(horizontal: 7),
                              //     decoration: BoxDecoration(
                              //         border: Border.all(
                              //             color: AppColors.greyTextColor),
                              //         borderRadius: BorderRadius.circular(20)),
                              //     child: Row(
                              //       children: [
                              //         Image.asset(
                              //           AssetUrl.whatsapp,
                              //           height: 50,
                              //         ),
                              //         SizedBox(
                              //           width: 5,
                              //         ),
                              //         SelectableText(
                              //           whatsappNo,
                              //           showCursor: true,
                              //           style: context.dividerTextLarge?.copyWith(
                              //               color: AppColors.blueButtonColor),
                              //         ),
                              //       ],
                              //     ),
                              //   ),
                              // ),
                              ),
                          const SizedBox(
                            width: 5,
                          ),
                          Expanded(
                              child: OutlinedButton.icon(
                                  onPressed: () async {
                                    final Uri url = Uri.parse(
                                        "https://wa.me/$whatsapp2?text=Hello, my name is ${userController.user.value.firstName} ${userController.user.value.secondName}");
                                    if (!await launchUrl(url)) {
                                      ToastUtils.showErrorToast(context,
                                          "Could not launch the url", "Error");
                                    } else {
                                      throw 'could_not_launch_url_with_param'.tr.replaceAll('{url}', whatsapp2);
                                    }
                                  },
                                  icon: Image.asset(
                                    AssetUrl.whatsapp,
                                    height: 50,
                                  ),
                                  label: Text(
                                    whatsapp2,
                                    style: context.dividerTextSmall?.copyWith(
                                        color: AppColors.blueButtonColor),
                                  ))
                              // child: InkWell(
                              //   onTap: () async {
                              //     final Uri url = Uri.parse(
                              //         "https://wa.me/${whatsapp2}?text=Hello, my name is ${userController.user.value.firstName} ${userController.user.value.secondName}");
                              //     if (!await launchUrl(url)) {
                              //       ToastUtils.showErrorToast(context,
                              //           "Could not launch the url", "Error");
                              //     } else {
                              //       throw 'Could not launch $whatsapp2';
                              //     }
                              //   },
                              //   child: Container(
                              //     padding: EdgeInsets.symmetric(horizontal: 7),
                              //     decoration: BoxDecoration(
                              //         border: Border.all(
                              //             color: AppColors.greyTextColor),
                              //         borderRadius: BorderRadius.circular(20)),
                              //     child: Row(
                              //       children: [
                              //         Image.asset(
                              //           AssetUrl.whatsapp,
                              //           height: 50,
                              //         ),
                              //         SizedBox(
                              //           width: 5,
                              //         ),
                              //         SelectableText(
                              //           whatsapp2,
                              //           showCursor: true,
                              //           style: context.dividerTextLarge?.copyWith(
                              //               color: AppColors.blueButtonColor),
                              //         ),
                              //       ],
                              //     ),
                              //   ),
                              // ),
                              ),
                        ],
                      ),
                    ],
                  );
                }),
                const SizedBox(
                  height: 12,
                ),
                Align(
                  alignment: Alignment.centerLeft,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      customText('email'.tr, context),
                      OutlinedButton.icon(
                          onPressed: _launchEmail,
                          icon: const Icon(Icons.email_rounded),
                          label: Text('support_email'.tr)),
                    ],
                  ),
                )
                //TextButton(onPressed: _launchEmail, child: Text("<EMAIL>"))
              ],
            ),
          )),
    );
  }
}
