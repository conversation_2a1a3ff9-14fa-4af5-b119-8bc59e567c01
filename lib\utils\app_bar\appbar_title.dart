import 'package:flutter/material.dart';
import 'package:onekitty_admin/main.dart' show isLight;
import '../utils_exports.dart';

// ignore: must_be_immutable
class AppbarTitle extends StatelessWidget {
  AppbarTitle({
    super.key,
    required this.text,
    this.margin,
    this.onTap,
    this.textColor,
    this.textSize,
  });

  String text;

  EdgeInsetsGeometry? margin;

  Function? onTap;

  final Color? textColor;
  final double? textSize;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onTap?.call();
      },
      child: Padding(
        padding: margin ?? EdgeInsets.zero,
        child: Text(
          text,
          style: theme.textTheme.titleMedium!.copyWith(
            color: isLight.value ? textColor ?? appTheme.black900 : appTheme.whiteA700,
            fontSize: textSize ?? 18.0,
          ),
        ),
      ),
    );
  }
}
