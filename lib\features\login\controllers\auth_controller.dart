import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_messaging/firebase_messaging.dart'; 
import 'package:logger/logger.dart';
import 'package:onekitty_admin/models/auth/register_request.dart';

import 'package:onekitty_admin/models/auth/user_model.dart';
import 'package:onekitty_admin/services/api_urls.dart';
import 'package:onekitty_admin/services/auth_manager.dart';
import 'package:onekitty_admin/services/http_service.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:onekitty_admin/utils/cache_keys.dart';

class AuthenticationController extends GetxController implements GetxService {
  final HttpService apiProvider = Get.put(HttpService());
  final box = Get.put<GetStorage>(GetStorage());
  final logger = Get.put<Logger>(Logger());
  RxString apiMessage = ''.obs;
  RxString checkoutId = ''.obs;
  final authManager = AuthenticationManager();

  Rx<UserModelLatest> usermodel = UserModelLatest().obs;

  RxBool isloading = false.obs;
  RxBool status = false.obs;
  RxBool isLoginloading = false.obs;

  RxString kittStatus = ''.obs;
  RxBool isPinloading = false.obs;

  RxBool isOtploading = false.obs;

  Future<bool> login(String phone, String pass) async {
    isLoginloading(true);
    update();
    try {
      String? fcmToken;
      final firebaseUser = FirebaseAuth.instance.currentUser;
      if (firebaseUser != null) {
        fcmToken = await FirebaseMessaging.instance.getToken();
        await FirebaseMessaging.instance.setAutoInitEnabled(true);
        logger.f(fcmToken);
      }
      var res = await apiProvider
          .request(url: ApiUrls.login, method: Method.POST, params: {
        "phone_number": phone,
        "password": pass,
        "device_token": fcmToken ?? "",
      });
      logger.log(Level.debug, res.data);
      if (res.data["status"]) {
        final u = res.data["data"]["user"];
        usermodel(UserModelLatest.fromJson(u));
       
        box.write(CacheKeys.user, u);
        //save token
        final token = res.data["data"]["token"];
        await box.write(CacheKeys.token, token);
        // Make sure the login status is updated
        authManager.login(token);
        firebaseLogin(token);
      }
      apiMessage(res.data["message"]);
      isLoginloading(false);
      update();
      return res.data["status"];
    } catch (e) {
      logger.e(e);
      apiMessage("Error,Please try again");
      isLoginloading(false);
      update();
    }

    return false;
  }

  firebaseLogin(String token) async {
    try {
      await FirebaseAuth.instance.signInWithCustomToken(token);
      logger.w("Signed in with temporary account.");
    } on FirebaseAuthException catch (e) {
      switch (e.code) {
        case "operation-not-allowed":
          logger.e("Anonymous auth hasn't been enabled for this project.");
          break;
        default:
          logger.e("Unknown error.");
      }
    }
  }

  Future<bool> register({required RegisterRequest request}) async {
    isloading(true);
    update();

    try {
      var res = await apiProvider.request(
          url: ApiUrls.register, method: Method.POST, params: request.toJson());

      if (res.data["status"]) {
        checkoutId(res.data["data"]["checkout_id"]);
        final u = res.data["data"]["user"];

        usermodel(UserModelLatest.fromJson(u));
        box.write(CacheKeys.user, u);
      }
      apiMessage(res.data["message"]);
      isloading(false);
      update();

      return res.data["status"];
    } catch (e) {
      logger.e(e);
      isloading(false);
      apiMessage("Error,Please try again");
      update();

      return false;
    }
  }

  Future<bool> forgotPassRequest(
    String phone,
  ) async {
    isloading(true);
    update();
    try {
      var res = await apiProvider
          .request(url: ApiUrls.forgotPass, method: Method.POST, params: {
        "phone_number": phone,
      });

      if (res.data["status"]) {
        checkoutId(res.data["data"]["checkout_id"]);
        final u = res.data["data"]["user"];
        logger.d(u);

        usermodel(UserModelLatest.fromJson(u));
        box.write(CacheKeys.user, u);
      }
      apiMessage(res.data["message"]);
      isloading(false);
      update();
      return res.data["status"];
    } catch (e) {
      logger.e(e);
      isloading(false);
      apiMessage("Error,Please try again");
      update();
      return false;
    }
  }

  Future<bool> confirmOtp(
    String otp,
  ) async {
    isOtploading(true);
    update();
    try {
      var res = await apiProvider
          .request(url: ApiUrls.otpConfirm, method: Method.POST, params: {
        "phone_number": usermodel.value.phoneNumber ?? "",
        "user_id": usermodel.value.id ?? 0,
        "otp": int.parse(otp),
        "checkout_id": checkoutId.string
      });

      if (res.data["status"]) {
        final u = res.data["data"]["user"];
        usermodel(UserModelLatest.fromJson(u));
        box.write(CacheKeys.user, u);
      }
      apiMessage(res.data["message"]);
      isOtploading(false);
      update();
      return res.data["status"];
    } catch (e) {
      logger.e(e);
      isOtploading(false);
      apiMessage("Error,Please try again");
      update();
      return false;
    }
  }

  Future<bool> setPin(String password) async {
    isPinloading(true);
    try {
      var res = await apiProvider
          .request(url: ApiUrls.setPin, method: Method.POST, params: {

        "password_confirm": password,
        "password": password,
        "phone_number": usermodel.value.phoneNumber ?? "",
        "user_id": usermodel.value.id ?? 0
      });

      if (res.data["status"]) {
        final u = res.data["data"]["user"];
        usermodel(UserModelLatest.fromJson(u));

        box.write(CacheKeys.user, u);
        final token = res.data["data"]["token"];
        box.write(CacheKeys.token, token);
        authManager.login(token);
        firebaseLogin(token);
      }
      apiMessage(res.data["message"]);
      isPinloading(false);
      return res.data["status"];
    } catch (e) {
      logger.e(e);
      isPinloading(false);
      apiMessage("Error,Please try again");

      return false;
    }
  }
}

  




  // Future<bool> login(String phone, String pass) async {
  //   isLoginloading(true);
  //   try {
  //     String? fcmToken;
  //     final firebaseUser = FirebaseAuth.instance.currentUser;
  //     if (firebaseUser != null) {
  //       fcmToken = await FirebaseMessaging.instance.getToken();
  //       await FirebaseMessaging.instance.setAutoInitEnabled(true);
  //       logger.wtf(fcmToken);
  //     }
  //     var res = await apiProvider.request(
  //         url: ApiUrls.login,
  //         method: Method.POST,
  //         params: {
  //           "phone_number": phone,
  //           "password": pass,
  //           "device_token": fcmToken
  //         });

  //     if (res.data["status"]) {
  //       final u = res.data["data"]["user"];
  //       user(UserModel.fromJson(u));
  //       box.write(CacheKeys.user, u);
  //       //save token
  //       final token = res.data["data"]["token"];
  //       box.write(CacheKeys.token, token);
  //       authManager.login(token);
  //       firebaseLogin();
  //     }
  //     apiMessage(res.data["message"]);
  //     isLoginloading(false);
  //     return res.data["status"];
  //   } catch (e) {
  //     logger.e(e);
  //     apiMessage("Error,Please try again");
  //     isLoginloading(false);

  //     return false;
  //   }
  // }

  // firebaseLogin() async {
  //   try {
  //     await FirebaseAuth.instance.signInAnonymously();
  //     logger.w("Signed in with temporary account.");
  //   } on FirebaseAuthException catch (e) {
  //     switch (e.code) {
  //       case "operation-not-allowed":
  //         logger.e("Anonymous auth hasn't been enabled for this project.");
  //         break;
  //       default:
  //         logger.e("Unknown error.");
  //     }
  //   }
  // }
