import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onekitty_admin/helpers/show_snack_bar.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/views/widgets/mpesa_transaction_widget.dart';
import 'package:onekitty_admin/services/api_urls.dart';
import 'package:onekitty_admin/services/http_service.dart';

class PaymentsController extends GetxController implements GetxService {
  RxBool isLoading = false.obs;

  final logger = Get.find<Logger>();
  final HttpService apiProvider = Get.find();
  final cardPayment = {}.obs;
  final transId = "".obs;

  Future<bool> cardPaymentCheckout({
    required int amount,
    required int channel,
    required String phoneNumber,
    required int userId,
    String? email,
  }) async {
    try {
      isLoading(true);
      var res = await apiProvider.request(
        url: ApiUrls.topUp,
        method: Method.POST,
        params: {
          "amount": amount,
          "phone_number": phoneNumber,
          "email": email,
          "channel_code": channel,
          "user_id": userId,
          "payer_email": email,
        },
      );
      if (res.data["status"]) {
        cardPayment(res.data["data"]);
        return true;
      } else {
        Get.snackbar(
          "Error",
          res.data["message"],
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return false;
      }
    } catch (e) {
      logger.e(e);
      return false;
    } finally {
      isLoading(false);
    }
  }

  Future<bool> completePayment(
      {required Map<String, dynamic> data,
      required BuildContext context}) async {
    isLoading(true);
    try {
      final response = await apiProvider.request(
          params: data, method: Method.POST, url: ApiUrls.PURCHASETICKETS);
          print("${(response.data)}");
      if (response.data['status'] ?? false) {
        transId.value = response.data["data"]['checkout_request_id'];

        Navigator.pop(context);
        Get.defaultDialog(
            content: MPESATransactionWidget(
                response: MpesaTransferResponseModel.fromJson(response.data),));
        return true;
      } else {
        Snack.show(false, '${response.data['message'] ?? 'an error occured'}');
      }
      isLoading(false);
      return false;
    } catch (e) {
      logger.e(e);
      isLoading(false);
      Snack.show(false, 'an error occured');
      return false;
    } finally {
      isLoading(false);
    }
  }
}
