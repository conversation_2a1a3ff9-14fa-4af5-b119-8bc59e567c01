import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:onekitty_admin/configs/country_specifics.dart';
import 'package:onekitty_admin/features/onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty_admin/helpers/colors.dart';
import 'package:onekitty_admin/models/chama/chama_memebers_model.dart';
import 'package:onekitty_admin/nav_routes/nav_routes.dart';
import 'package:onekitty_admin/utils/size_config.dart';
import 'package:onekitty_admin/utils/utils_exports.dart';
import 'package:flutter_expandable_fab/flutter_expandable_fab.dart';
import 'members_widget.dart';

class Operations extends StatefulWidget {
   const Operations({super.key});

  @override
  State<Operations> createState() => _MembersState();
}

class _MembersState extends State<Operations> {
  final ChamaDataController chamaDataController =
      Get.put(ChamaDataController());
  final ChamaController chamaController = Get.put(ChamaController());
  TextEditingController phoneController = TextEditingController();
  List<ChamaMembers> filteredmembers = [];
  PhoneNumber num = CountryConfig.phoneNumber;
  List<String> roles = [];

  String invitePhone = "";

  @override
  void dispose() {
    super.dispose();
  }

  @override
  void initState() {
    filteredmembers = chamaController.chamaMembers;
    roles = chamaController.roles.map((role) => role.role).toList();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
          //appBar: buildAppBar(context),
          body: SingleChildScrollView(
            child: Container(
              width: double.maxFinite,
              padding: EdgeInsets.symmetric(horizontal: 10.w),
              child: Column(
                children: [
                   const RowAppBar(),
                  Text(chamaDataController.chama.value.chama?.title ?? "",
                      style: CustomTextStyles.titleMediumBlack900),
                  GetBuilder(builder: (ChamaController chamaController) {
                    if (chamaController.isloadingChama.isTrue) {
                      return Text('checking'.tr);
                    }

                    return Text("${chamaController.OData.value.total} ${'members_count'.tr}",
                        style: theme.textTheme.titleLarge);
                  }),
                  SizedBox(height: 5.h),
                  buildChamaMembers(context),
                  GetBuilder(
                    builder: (ChamaController chamaController) {
                      if (chamaController.loadingMore.isTrue) {
                        return Text('loading_more_members'.tr);
                      } else if (chamaController.scrollEnd.isTrue) {
                        return Text(
                            'reached_end_of_list'.tr);
                      }

                      return  const SizedBox.shrink();
                    },
                  ),
                ],
              ),
            ),
          ),
          floatingActionButtonLocation: ExpandableFab.location,
          floatingActionButton: chamaDataController.chama.value.member?.role ==
                      "CHAIRPERSON" ||
                  chamaDataController.chama.value.member?.role == "TREASURER"
              ? ExpandableFab(
                  type: ExpandableFabType.up,
                  openButtonBuilder: RotateFloatingActionButtonBuilder(
                    child:  const Icon(Icons.account_box),
                    fabSize: ExpandableFabSize.regular,
                    foregroundColor: Colors.white,
                    backgroundColor: AppColors.blueButtonColor,
                    shape:  const CircleBorder(),
                  ),
                  closeButtonBuilder: DefaultFloatingActionButtonBuilder(
                    child:  const Icon(Icons.close),
                    fabSize: ExpandableFabSize.regular,
                    foregroundColor: Colors.white,
                    backgroundColor: AppColors.blueButtonColor,
                    shape:  const CircleBorder(),
                  ),
                  children: [
                    if (chamaDataController.chama.value.member?.role ==
                        "CHAIRPERSON")
                      InkWell(
                        onTap: () {
                          Get.toNamed(NavRoutes.invite);
                        },
                        child: Container(
                          decoration: BoxDecoration(
                            color: AppColors.blueButtonColor,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child:  Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Row(
                              children: [
                                const CustomImageView(
                                  imagePath: AssetUrl.contact,
                                ),
                                Text(
                                  'add_chama_member'.tr,
                                  style: const TextStyle(
                                    fontSize: 15,
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    if (chamaDataController.chama.value.member?.role ==
                        "CHAIRPERSON")
                      InkWell(
                        onTap: () {
                          Get.toNamed(NavRoutes.operation);
                        },
                        child: Container(
                          decoration: BoxDecoration(
                            color: AppColors.blueButtonColor,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child:  Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Row(
                              children: [
                                const CustomImageView(
                                  color: Colors.white,
                                  imagePath: AssetUrl.scroll,
                                ),
                                Text(
                                  'set_receiving_order'.tr,
                                  style: const TextStyle(
                                    fontSize: 15,
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    InkWell(
                      onTap: () {
                        Get.toNamed(NavRoutes.penalizeMultiple);
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          color: AppColors.blueButtonColor,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child:  Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Row(
                            children: [
                              const CustomImageView(
                                color: Colors.white,
                                imagePath: AssetUrl.scroll,
                              ),
                              Text(
                                'issue_penalties'.tr,
                                style: const TextStyle(
                                  fontSize: 15,
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                )
              :  const SizedBox.shrink()),
    );
  }

  Widget buildChamaMembers(BuildContext context) {
    return GetX(
        init: ChamaController(),
        initState: (state) {
          Future.delayed(Duration.zero, () async {
            chamaController.isloadingChama(true);
            try {
              await state.controller?.getChamaMembers(
                chamaId: chamaDataController.chama.value.chama?.id,
                sort: "LEADERS",
              );
              chamaController.reset();
            } catch (e) {
              throw e;
            }
            chamaController.isloading(false);
          });
        },
        builder: (ChamaController chamaController) {
          if (chamaController.isloadingChama.isTrue) {
            return SizedBox(
              height: SizeConfig.screenHeight * .33,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SpinKitDualRing(
                      color: ColorUtil.blueColor,
                      lineWidth: 4.sp,
                      size: 40.0.sp,
                    ),
                    Text(
                      'loading'.tr,
                      style:  const TextStyle(
                        color: Colors.white,
                      ),
                    )
                  ],
                ),
              ),
            );
          }
          if (chamaController.chamaMembers.isEmpty) {
            return SizedBox(
              height: SizeConfig.screenHeight * .33,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SpinKitDualRing(
                      color: ColorUtil.blueColor,
                      lineWidth: 4.sp,
                      size: 40.0.sp,
                    ),
                    Text(
                      'loading'.tr,
                      style:  const TextStyle(
                        color: Colors.white,
                      ),
                    )
                  ],
                ),
              ),
            );
          } else if (chamaController.chamaMembers.isNotEmpty) {
            return Column(
              children: [
                TextFormField(
                  autofocus: false,
                  // controller: searchController,
                  decoration:  InputDecoration(
                    prefixIcon: const Icon(Icons.search),
                    hintText: 'search_by_name'.tr,
                  ),
                  onChanged: (value) {
                    filteredmembers = chamaController.chamaMembers
                        .where((chama) => chama.firstName!
                            .toLowerCase()
                            .contains(value.toLowerCase()))
                        .toList();
                    setState(() {});
                  },
                ),
                SizedBox(
                  height: 10.h,
                ),
                Container(
                  height: SizeConfig.screenHeight * 0.7,
                  margin: EdgeInsets.only(left: 2.h),
                  padding:
                      EdgeInsets.symmetric(horizontal: 2.h, vertical: 17.h),
                  decoration: AppDecoration.outlineGray.copyWith(
                    borderRadius: BorderRadiusStyle.roundedBorder8,
                  ),
                  child: Column(
                    children: [
                      Expanded(
                        child: ListView.builder(
                            controller: chamaController.controller,
                            padding: EdgeInsets.zero,
                            itemCount: filteredmembers.length,
                            itemBuilder: (context, index) => MembersWidget(
                                index: index,
                                roles: roles,
                                filteredmembers: filteredmembers)),
                      ),
                      if (chamaController.loadingMore.isTrue)
                        Padding(
                          padding:  const EdgeInsets.symmetric(vertical: 8.0),
                          child: SpinKitDualRing(
                            color: ColorUtil.blueColor,
                            lineWidth: 4.sp,
                            size: 40.0.sp,
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            );
          }

          return Text('no_members_added_yet'.tr);
        });
  }
}
