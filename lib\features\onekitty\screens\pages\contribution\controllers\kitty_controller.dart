import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/services/kitty_service.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/controllers/controllers.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/controllers/create_event_controller.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/controllers/user_ktty_controller.dart';
import 'package:onekitty_admin/models/contr_kitty_model.dart';
import 'package:onekitty_admin/models/kitty/kitty_categories_model.dart';
import 'package:onekitty_admin/models/transaction_model.dart';
import 'package:onekitty_admin/models/kitty_model.dart';
import 'package:onekitty_admin/features/onekitty/controllers/contribute_controller.dart';
import 'package:onekitty_admin/helpers/show_toast.dart';
import 'package:onekitty_admin/models/kitty_payload.dart';
import 'package:onekitty_admin/models/auth/regDTO_model.dart';
import 'package:onekitty_admin/utils/common_strings.dart';
import 'package:onekitty_admin/utils/whatsapp_error_dialog.dart'; 

/// Controller for managing Kitty UI state and orchestrating service calls
class KittyController extends GetxController {
  final KittyService _kittyService = Get.isRegistered() ? Get.find() : Get.put(KittyService());
  final logger = Get.find<Logger>();
  
  // Observable variables
  RxString apiMessage = ''.obs;
  RxString whatsappApiMessage = ''.obs;
  RxString errorMessage = ''.obs;
  RxString reasons = "".obs;
  RxInt currentPage = 0.obs;
  RxString whatsappnumber = KtStrings.phoneNumberOnekitty.obs;
  Rx<NewUser> user = NewUser().obs;
  
  final balance = 0.0.obs;
  final thirdPartyCharges = 0.0.obs;
  final charges = 0.0.obs;
  final totalAmount = 0.0.obs;
  final isFetchingBalance = false.obs;
  
  RxString urlKit = ''.obs;
  RxString insta = ''.obs;
  RxBool isloading = false.obs;
  RxBool isLinkloading = false.obs;
  RxBool isNameloading = false.obs;
  RxBool isEditEndDateloading = false.obs;
  RxBool isEditloading = false.obs;
  RxBool loadingTransactions = false.obs;
  
  RxList<TransactionModel> transactionsKitty = <TransactionModel>[].obs;
  RxList bannerList = [].obs;
  RxList<TransactionModel> filtrtransactions = <TransactionModel>[].obs;
  RxBool loadingfiltrTransactions = false.obs;
  Rx<Results?> results = Results().obs;
  
  RxBool status = false.obs;
  RxBool whtsappStatus = false.obs;
  RxBool loadingWithdraw = false.obs;
  
  RxString apiMessageProcess = ''.obs;
  RxString beneficiaryName = ''.obs;
  RxString beneficiaryNumber = ''.obs;
  RxString reciveChannel = ''.obs;
  RxString benefAccRef = ''.obs;
  RxString reciveAmount = ''.obs;
  RxString whatsappStatus = ''.obs;
  RxMap withdrawData = <String, dynamic>{}.obs;
  
  RxString textmessage = ''.obs;
  final isUploadingImage = false.obs;
  Rx<Kitty> kittCreated = Kitty().obs;
  final media = <KittyMediaModel>[].obs;
  RxMap socials = {}.obs;
  ContributeController singleKitty = Get.put(ContributeController());
  
  // Kitty categories
  RxList<KittyCategoriesModel> kittyCategories = <KittyCategoriesModel>[].obs;
  RxBool isLoadingKittyCategories = false.obs;
  final RxList<int> selectedKittyCategories = <int>[].obs;
  final RxList<KittyCategoriesModel> selectedKittyCategoryModels = <KittyCategoriesModel>[].obs;
  final RxList<KittyCategoriesModel> filteredKittyCategories = <KittyCategoriesModel>[].obs;
  final RxString categorySearchQuery = ''.obs;

  @override
  void onInit() {
    getLocalUser();
    super.onInit();
  }

  NewUser? getLocalUser() {
    user((_kittyService.getLocalUser() ?? NewUser()) as NewUser?);
    return user.value;
  }

  /// Fetch balance and charges
  Future fetchBalance(double amount, {required Map<String, dynamic> data}) async {
    try {
      isFetchingBalance(true);
      balance.value = 0.0;
      charges.value = 0.0;
      totalAmount.value = 0.0;
      
      final result = await _kittyService.fetchBalance(amount, data: data);
      
      if (result != null) {
        balance(result['balance']);
        charges(result['charges']);
        totalAmount(result['totalAmount']);
        thirdPartyCharges(result['thirdPartyCharges']);
      }
    } catch (e) {
      logger.e(e);
      apiMessage('An error occured');
      return false;
    } finally {
      isFetchingBalance(false);
    }
  }

  /// Create a new kitty
  Future<bool> createKitty({required CreateKitPayload payload}) async {
    isloading(true);
    update();
    
    try {
      final resp = await _kittyService.createKitty(payload: payload);
      
      apiMessage(resp["message"]);
      whatsappApiMessage(resp["whatsapp_message"]);
      whtsappStatus(resp["whatsapp_status"]);
      logger.log(Level.debug, resp);
      
      if (resp["whatsapp_data"] != null) {
        if (resp["whatsapp_data"]['whatsapp_number'] != null) {
          whatsappnumber(resp["whatsapp_data"]['whatsapp_number']);
        }
        if (resp["whatsapp_data"]['reasons'] != null) {
          reasons(resp["whatsapp_data"]['reasons']);
        }
      }
      
      if (resp['status'] ?? false) {
        kittCreated(Kitty.fromJson(resp["data"]["kitty"]));
        socials(resp["social_mesages"]);
        urlKit(resp["data"]['url']);
        insta(resp["social_mesages"]["instagram"]);
      } else {
        Get.snackbar('Error', resp['message'] ?? 'couldnt create kitty',
            backgroundColor: Colors.red);
      }
      
      return resp['status'];
    } catch (e) {
      logger.e(e);
      apiMessage('An error occured');
      return false;
    } finally {
      isloading(false);
      update();
    }
  }

  /// Pick and upload image
  Future<bool?> pickImage({
    required int kittyId,
    required String name,
    required BuildContext context,
  }) async {
    isUploadingImage(true);
    
    try {
      final image = await _kittyService.pickImageFromGallery();
      
      if (image == null) {
        ToastUtils.showToast('No image selected');
        return false;
      }

      final file = image.path;
      final validation = _kittyService.validateImageFile(file, 0);
      
      if (!validation['valid']) {
        ToastUtils.showToast(validation['error']);
        return false;
      }

      final fileSizeInMB = await _kittyService.getFileSizeInMB(file);
      final maxSize = Get.find<GlobalControllers>().imageSize;

      if (fileSizeInMB > maxSize) {
        ToastUtils.showToast(
          'Image size must be ${maxSize}MB or less (Current size: ${fileSizeInMB.toStringAsFixed(1)}MB)'
        );
        return false;
      }

      final eventsController = Get.find<CreateEventController>();
      final url = await eventsController.uploadFile(
        path: file,
        fileName: "${DateTime.now().millisecondsSinceEpoch}${file.split(RegExp(r'[/\\]')).last}"
      );
      
      final title = "$name-${DateTime.now().millisecondsSinceEpoch}";
      final resp = await _kittyService.uploadKittyImage(
        url: url??'',
        title: title,
        kittyId: kittyId,
      );

      if (resp?['status'] == true) {
        singleKitty.kittyMedia.add(KittyMediaModel(
          url: url,
          type: "image",
          title: title,
          status: 'ACTIVE'
        ));
        apiMessage(resp?["message"]);
        return true;
      } else {
        ToastUtils.showToast(resp?["message"] ?? 'Upload failed');
        return false;
      }
    } catch (e) {
      logger.e('General error in pickImage: $e');
      ToastUtils.showToast('Failed to process image. Please try again.');
      return false;
    } finally {
      isUploadingImage(false);
    }
  }

  /// Delete media
  Future<void> deleteMedia(int mediaId, {int? pos}) async {
    final success = await _kittyService.deleteMedia(mediaId);
    if (success) {
      singleKitty.kittyMedia.removeAt(pos ?? 0);
    }
  }

  /// Update kitty
  Future<bool> updateKitty({required CreateKitPayload request}) async {
    isEditloading(true);
    
    try {
      final resp = await _kittyService.updateKitty(request: request);
      
      apiMessage(resp["message"]);
      
      if (resp['status'] ?? false) {
        kittCreated(Kitty.fromJson(resp["data"]['kitty']));
        urlKit(resp["data"]['url']);
      }
      
      update();
      return resp['status'];
    } catch (e) {
      logger.e(e);
      apiMessage('An error occured');
      return false;
    } finally {
      isEditloading(false);
    }
  }

  /// Update end date
  updateEndDate({required DateTime newDate, required int KittyId}) async {
    isEditEndDateloading(true);
    update();
    
    try {
      final resp = await _kittyService.updateEndDate(
        newDate: newDate,
        kittyId: KittyId,
      );
      
      logger.w(resp);
      apiMessage(resp["message"]);
      status(resp["status"]);
      
      return resp["status"];
    } catch (e) {
      logger.e(e);
      apiMessage("Error, Please try again later");
      return false;
    } finally {
      isEditEndDateloading(false);
      update();
    }
  }

  /// Withdraw request
  withdrawRequest({
    required String amount,
    required int kittyId,
    required bool isconfirm,
    required int benficiaryId,
    required String remarks,
  }) async {
    loadingWithdraw(true);
    update();
    
    try {
      final resp = await _kittyService.withdrawRequest(
        amount: amount,
        kittyId: kittyId,
        isconfirm: isconfirm,
        benficiaryId: benficiaryId,
        remarks: remarks,
      );
      
      apiMessage(resp["message"] ?? "Unknown error");
      status(resp["status"] ?? false);
      
      if (isconfirm && resp["status"]) {
        withdrawData.value = resp["data"] ?? {};
      }
      
      if (resp["status"] && !isconfirm) {
        var dat = resp["data"];
        beneficiaryName(dat["beneficiary_name"] ?? "");
        beneficiaryNumber(dat["beneficiary_account"] ?? "");
        reciveAmount(dat["amount_received"]?.toString() ?? "0");
        reciveChannel(dat["channel"] ?? "");
      }
    } catch (e) {
      logger.e(e);
      apiMessage("Error! Please try again");
      status(false);
    } finally {
      loadingWithdraw(false);
      update();
    }
  }

  /// Join group
  Future<bool> joinGroup({
    required BuildContext context,
    required int id,
    required String link,
  }) async {
    isLinkloading(true);
    update();
    
    try {
      final res = await _kittyService.joinGroup(id: id, link: link);

      if (!context.mounted) return false;
      
      apiMessage(res["message"]);
      
      if (res["data"] != null) {
        if (res["data"]["whatsapp_number"] != null) {
          whatsappnumber(res["data"]["whatsapp_number"]);
        }
        if (res["data"]["reasons"] != null) {
          reasons(res["data"]["reasons"]);
        }
      }
      
      if (res["status"]) {
        await Get.find<ContributeController>().getWhatsapp(
          id: Get.put(DataController()).kitty.value.kitty?.iD ?? 0
        );

        ToastUtils.showInfoToast(context, res["message"], "Success");
        apiMessage(res["message"]);
        Navigator.pop(context);
        return true;
      } else {
        Get.to(() => ErrorPage(
          link: link,
          reasons: reasons.string,
          messages: res['message'],
          whatsAppNo: whatsappnumber.value,
        ));
        ToastUtils.showErrorToast(context, "Error", "${res['message']}");
        return false;
      }
    } catch (e) {
      logger.e(e);
      apiMessage('An error occured');
      return false;
    } finally {
      isLinkloading(false);
      update();
    }
  }

  /// Get network code
  int? getNetworkCode({required String networkTitle}) {
    return _kittyService.getNetworkCode(networkTitle: networkTitle);
  }

  /// Get network name
  String getNetworkName({required String code}) {
    return _kittyService.getNetworkName(code: code);
  }

  /// Get kitty contributions
  getKittyContributions({
    required int kittyId,
    int? page = 0,
    int? size = 20,
    int? eventId,
  }) async {
    loadingTransactions(true);
    update();
    
    try {
      final resp = await _kittyService.getKittyContributions(
        kittyId: kittyId,
        page: page,
        size: size,
        eventId: eventId,
      );
      
      if (resp["status"]) {
        Results fetchedResults = eventId != null
            ? Results.fromJson(resp["data"])
            : Results.fromJson(resp["data"]["results"]);
            
        transactionsKitty([]);
        
        if (eventId != null) {
          for (var element in resp["data"]["items"] ?? []) {
            transactionsKitty.add(TransactionModel.fromJson(element));
          }
        } else {
          for (var element in resp["data"]["results"]["items"] ?? []) {
            transactionsKitty.add(TransactionModel.fromJson(element));
          }
        }
        
        fetchedResults.items = transactionsKitty;
        results.value = fetchedResults;
      } else {
        transactionsKitty([]);
      }
    } catch (e) {
      logger.e(e);
      apiMessage('An error occured');
    } finally {
      loadingTransactions(false);
      update();
    }
  }

  /// Share kitty transactions
  Future<bool> shareKittyTrans({required int id}) async {
    try {
      final res = await _kittyService.shareKittyTrans(id: id);
      
      apiMessage(res["message"]);
      
      if (res["status"]) {
        textmessage(res["data"]["whatsapp_message"]);
        return true;
      }
      return false;
    } catch (e) {
      logger.e(e);
      return false;
    }
  }

  /// Get kitty categories
  Future<void> getKittyCategories() async {
    try {
      kittyCategories.clear();
      kittyCategories([]);
      isLoadingKittyCategories(true);
      
      final categories = await _kittyService.getKittyCategories();
      kittyCategories.assignAll(categories);
      filteredKittyCategories.assignAll(kittyCategories);
    } catch (e) {
      logger.e('Error fetching kitty categories: $e');
    } finally {
      isLoadingKittyCategories(false);
    }
  }

  /// Toggle category selection
  void toggleCategorySelection(KittyCategoriesModel category) {
    final isSelected = selectedKittyCategoryModels.any((c) => c.id == category.id);
    if (isSelected) {
      selectedKittyCategoryModels.removeWhere((c) => c.id == category.id);
      selectedKittyCategories.remove(category.id);
    } else if (selectedKittyCategoryModels.length < 3) {
      selectedKittyCategoryModels.add(category);
      selectedKittyCategories.add(category.id!);
    }
    update();
  }

  /// Filter categories
  void filterCategories(String query) {
    categorySearchQuery.value = query;
    if (query.isEmpty) {
      filteredKittyCategories.assignAll(kittyCategories);
    } else {
      filteredKittyCategories.assignAll(
        kittyCategories.where((category) => 
          category.name?.toLowerCase().contains(query.toLowerCase()) ?? false
        ).toList()
      );
    }
    update();
  }

  /// Check if category is selected
  bool isCategorySelected(KittyCategoriesModel category) {
    return selectedKittyCategoryModels.any((c) => c.id == category.id);
  }

  /// Check if can select more categories
  bool canSelectMoreCategories() {
    return selectedKittyCategoryModels.length < 3;
  }

  /// Clear selected categories
  void clearSelectedCategories() {
    selectedKittyCategoryModels.clear();
    selectedKittyCategories.clear();
    update();
  }
}

/// Controller for managing single kitty data
class KittyDataController extends GetxController {
  Rx<KittyData> singleKitty = KittyData().obs;
}

/// Controller for managing link data
class LinkController extends GetxController {
  Rx<CreateKittyResponse> kittyResponse = CreateKittyResponse().obs;
  var whatsappStatus = RxBool(false);
}