import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty_admin/nav_routes/nav_routes.dart';

import '../../../../../../../utils/utils_exports.dart';

class SmsSentScreen extends StatefulWidget {
  const SmsSentScreen({super.key});

  @override
  State<SmsSentScreen> createState() => _SmsSentScreenState();
}

class _SmsSentScreenState extends State<SmsSentScreen> {
  GlobalKey<NavigatorState> navigatorKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: _buildAppBar(context),
        body: Container(
          width: double.maxFinite,
          padding: EdgeInsets.only(left: 47.w, top: 2.h, right: 47.w),
          child: Column(
            children: [
              Text("send_bulk_sms".tr, style: theme.textTheme.titleLarge),
              SizedBox(height: 13.h),
              Text("conveniently_send_messages".tr,
                  style: CustomTextStyles.bodyMediumBluegray700),
              SizedBox(height: 40.h),
              CustomImageView(
                  imagePath: AssetUrl.tickCircle, height: 185.h, width: 185.w),
              SizedBox(height: 58.h),
              Text("sms_successfully_sent".tr,
                  style: CustomTextStyles.titleMediumff545963),
              SizedBox(height: 22.h),
              CustomOutlinedButton(
                  onPressed: () {
                    Get.offNamed(NavRoutes.mainbulksms);
                  },
                  width: 189.w,
                  text: "okay".tr,
                  buttonStyle: CustomButtonStyles.outlinePrimary,
                  buttonTextStyle: CustomTextStyles.titleSmallDeeppurple500),
              SizedBox(height: 5.h)
            ],
          ),
        ),
      ),
    );
  }

  /// Section Widget
  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return CustomAppBar(
        height: 20.h,
        leadingWidth: 20.w,
        leading: AppbarLeadingImage(
            imagePath: AssetUrl.imgArrowLeft,
            margin: EdgeInsets.only(left: 1.w, top: 1.h, bottom: 1.h),
            onTap: () {
              onTapArrowLeft(context);
            }),
        title: AppbarTitle(text: "back".tr, margin: EdgeInsets.only(left: 8.w)),
        styleType: Style.bgwhite);
  }

  onTapArrowLeft(BuildContext context) {
    Navigator.pop(context);
  }
}
