import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

/// Widget to show upload progress with detailed information
class UploadProgressIndicator extends StatelessWidget {
  final RxBool isUploading;
  final String? currentFileName;
  final double? progress;
  final VoidCallback? onCancel;

  const UploadProgressIndicator({
    super.key,
    required this.isUploading,
    this.currentFileName,
    this.progress,
    this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (!isUploading.value) {
        return const SizedBox.shrink();
      }

      return Container(
        margin: EdgeInsets.symmetric(vertical: 8.h),
        padding: EdgeInsets.all(12.w),
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(
            color: Theme.of(context).primaryColor.withOpacity(0.3),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                SizedBox(
                  width: 16.w,
                  height: 16.h,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      Theme.of(context).primaryColor,
                    ),
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: Text(
                    currentFileName != null ? 'uploading_filename'.tr.replaceAll('{filename}', currentFileName!) : 'uploading'.tr,
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w500,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                if (onCancel != null)
                  IconButton(
                    onPressed: onCancel,
                    icon: Icon(
                      Icons.close,
                      size: 18.sp,
                      color: Colors.grey[600],
                    ),
                    constraints: BoxConstraints(
                      minWidth: 24.w,
                      minHeight: 24.h,
                    ),
                    padding: EdgeInsets.zero,
                  ),
              ],
            ),
            if (progress != null) ...[
              SizedBox(height: 8.h),
              LinearProgressIndicator(
                value: progress,
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(
                  Theme.of(context).primaryColor,
                ),
              ),
              SizedBox(height: 4.h),
              Text(
                'percent_complete'.tr.replaceAll('{percent}', (progress! * 100).toInt().toString()),
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ],
        ),
      );
    });
  }
}

/// Widget to show upload status with success/error states
class UploadStatusIndicator extends StatelessWidget {
  final bool isSuccess;
  final String message;
  final VoidCallback? onRetry;
  final VoidCallback? onDismiss;

  const UploadStatusIndicator({
    super.key,
    required this.isSuccess,
    required this.message,
    this.onRetry,
    this.onDismiss,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 4.h),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: isSuccess 
          ? Colors.green.withOpacity(0.1)
          : Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: isSuccess 
            ? Colors.green.withOpacity(0.3)
            : Colors.red.withOpacity(0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            isSuccess ? Icons.check_circle : Icons.error,
            color: isSuccess ? Colors.green : Colors.red,
            size: 20.sp,
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Text(
              message,
              style: TextStyle(
                fontSize: 14.sp,
                color: isSuccess ? Colors.green[700] : Colors.red[700],
              ),
            ),
          ),
          if (!isSuccess && onRetry != null)
            TextButton(
              onPressed: onRetry,
              child: Text(
                'retry'.tr,
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Colors.red[700],
                ),
              ),
            ),
          if (onDismiss != null)
            IconButton(
              onPressed: onDismiss,
              icon: Icon(
                Icons.close,
                size: 16.sp,
                color: Colors.grey[600],
              ),
              constraints: BoxConstraints(
                minWidth: 20.w,
                minHeight: 20.h,
              ),
              padding: EdgeInsets.zero,
            ),
        ],
      ),
    );
  }
}