import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'package:onekitty_admin/features/onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty_admin/helpers/colors.dart';
import 'package:onekitty_admin/helpers/show_snack_bar.dart';
import 'package:onekitty_admin/models/chama/meeting_request.dart';
import 'package:onekitty_admin/models/chama/meetings.dart';
import 'package:onekitty_admin/nav_routes/nav_routes.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/widgets/date_picker.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/widgets/row_widget.dart';
import 'package:onekitty_admin/features/onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty_admin/utils/cache_keys.dart';
import 'package:onekitty_admin/utils/common_strings.dart';
import 'package:onekitty_admin/utils/datetime/combined_datetime.dart';

import '../../../../../../../utils/utils_exports.dart';

class AddMeetingPage extends StatefulWidget {
  final bool isUpdating;
  final Meeting? meeting;
  const AddMeetingPage({super.key, required this.isUpdating, this.meeting});

  @override
  State<AddMeetingPage> createState() => _AddMeetingPageState();
}

class _AddMeetingPageState extends State<AddMeetingPage> {
  final formKey = GlobalKey<FormState>();
  TextEditingController startDateController = TextEditingController();
  TextEditingController startTimeController = TextEditingController();
  TextEditingController endDateController = TextEditingController();
  TextEditingController endTimeController = TextEditingController();
  TextEditingController venueController = TextEditingController();
  TextEditingController locationTipController = TextEditingController();
  TextEditingController titleController = TextEditingController();
  TextEditingController desController = TextEditingController();
  TextEditingController freqcyController = TextEditingController();
  TextEditingController meetingLinkController = TextEditingController();
  String? _dropdownValue;
  bool _showDropdown = false;

  final box = GetStorage();
  final ChamaController chamaController = Get.put(ChamaController());
  final ChamaDataController chamaDataController =
      Get.put(ChamaDataController());
  List<String> dropdownItems = [];
  String? selectedvalue;

  void toggleDropdownVisibility() {
    setState(() {
      _showDropdown = !_showDropdown;
    });
  }

  void dropdownCallback(String? selectedValue) {
    if (selectedValue is String) {
      setState(() {
        _dropdownValue = selectedValue;
      });
    }
  }

  @override
  void initState() {
    super.initState();
    getfrequency();
    if (widget.isUpdating && widget.meeting != null) {
      venueController.text = widget.meeting?.venue ?? "";
      titleController.text = widget.meeting?.title ?? "";
      desController.text = widget.meeting?.description ?? "";
      startDateController.text = DateFormat("yyyy-MM-dd")
          .format(widget.meeting?.startDate ?? DateTime.now());
      startTimeController.text = DateFormat("HH:ss")
          .format(widget.meeting?.startDate ?? DateTime.now());
      endDateController.text = DateFormat("yyyy-MM-dd")
          .format(widget.meeting?.endDate ?? DateTime.now());
      endTimeController.text = DateFormat("HH:ss")
          .format(widget.meeting?.startDate ?? DateTime.now());
      _dropdownValue = widget.meeting?.eventType;
      locationTipController.text = widget.meeting?.locationTip ?? "";
      if (_dropdownValue == "VIRTUAL") {
        meetingLinkController.text = widget.meeting?.venue ?? "";
      }
      selectedvalue = widget.meeting?.frequency;
    }
  }

  void getfrequency() {
    // _chamaController.getfrequency();
    setState(() {
      dropdownItems = chamaController.frequencies
          .map((frequency) => frequency.frequency)
          .toList();
    });
  }

  @override
  void dispose() {
    startDateController.dispose();
    startTimeController.dispose();
    endDateController.dispose();
    endTimeController.dispose();
    venueController.dispose();
    titleController.dispose();
    desController.dispose();
    locationTipController.dispose();
    meetingLinkController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.isUpdating ? 'update_meeting'.tr : 'add_meeting'.tr),
      ),
      persistentFooterButtons: [
        Obx(
          () => FilledButton( 
                      onPressed: () async {
                        await addMeeting();
                      },
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        mainAxisSize: MainAxisSize.max,
                        children: [widget.isUpdating ? Text('update'.tr) : Text('add_meeting'.tr), const SizedBox(width: 10,),
                      if (chamaController.isAddMeetingLoading.value) const CircularProgressIndicator(strokeWidth: 2, color: Colors.white,),],))
                ),],
      body: Container(
        margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 20),
        child: Form(
          key: formKey,
          child: SingleChildScrollView(
            child: Column(
              children: [ 
                Text(
                  widget.isUpdating
                      ? ""
                      : 'add_meeting_description'.tr,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                      color: appTheme.gray600, fontWeight: FontWeight.w600),
                ),
                const SizedBox(
                  height: 8,
                ),
                SingleLineRow(text: 'meeting_information'.tr),
                CustomTextField(
                  controller: titleController,
                  labelText: 'meeting_title'.tr,
                  hintText: 'meeting_title_hint'.tr,
                  validator: (p0) {
                    if (p0!.isEmpty) {
                      return 'this_field_required'.tr;
                    }
                    return null;
                  },
                ),
                const SizedBox(
                  height: 15,
                ),
                CustomTextField(
                  controller: desController,
                  labelText: 'meeting_description'.tr,
                  hintText: 'meeting_description_hint'.tr,
                  validator: (p0) {
                    if (p0!.isEmpty) {
                      return 'this_field_required'.tr;
                    }
                    return null;
                  },
                ),
                const SizedBox(
                  height: 20,
                ),
                Padding(
                  padding: const EdgeInsets.only(bottom: 5.0),
                  child: SingleLineRow(
                    text: 'start_date'.tr,
                    widget: Text(
                      'start_time'.tr,
                      style: Theme.of(context)
                          .textTheme
                          .titleLarge
                          ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15),
                    ),
                  ),
                ),
                DatePicker(
                  date: startDateController,
                  time: startTimeController,
                  isAllow: true,
                ),
                const SizedBox(
                  height: 25,
                ),
                Padding(
                  padding: const EdgeInsets.only(bottom: 5.0),
                  child: SingleLineRow(
                    text: 'end_date'.tr,
                    widget: Text(
                      'end_time'.tr,
                      style: Theme.of(context)
                          .textTheme
                          .titleLarge
                          ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15),
                    ),
                  ),
                ),
                DatePicker(
                  date: endDateController,
                  time: endTimeController,
                  isAllow: true,
                ),
                const SizedBox(
                  height: 25,
                ),
                SingleLineRow(text: 'event_type'.tr),
                Container(
                  decoration: BoxDecoration(
                      border: Border.all(color: _dropdownValue == null ? Colors.red : AppColors.blueButtonColor),
                      borderRadius: BorderRadius.circular(12)),
                  child: ListTile(
                    onTap: toggleDropdownVisibility,
                    title: Text(_dropdownValue == null ? 'select_event_type_required'.tr : "${'event_type'.tr}: $_dropdownValue"),
                    trailing: DropdownButton(
                        underline: const SizedBox(),
                        items:   [
                          DropdownMenuItem(
                            value: "PHYSICAL",
                            child: Text('physical'.tr.toUpperCase()),
                          ),
                          DropdownMenuItem(
                            value: "VIRTUAL",
                            child: Text('virtual'.tr.toUpperCase()),
                          ),
                        ],
                        value: _dropdownValue,
                        hint: Text('select_event_type'.tr),
                        onChanged: dropdownCallback),
                  ),
                ),
                const SizedBox(
                  height: 20,
                ),
                if (_dropdownValue == "VIRTUAL") ...[
                  SingleLineRow(text: 'meeting_link'.tr),
                  CustomTextField(
                    controller: meetingLinkController,
                    labelText: 'enter_meeting_link'.tr,
                    hintText: 'meeting_link_hint'.tr,
                    validator: (p0) {
                      if (p0!.isEmpty) {
                        return 'this_field_required'.tr;
                      }
                      return null;
                    },
                  ),
                ] else ...[
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Column(
                          children: [
                            SingleLineRow(text: 'meeting_venue'.tr),
                            CustomTextField(
                              controller: venueController,
                              labelText: 'venue'.tr,
                              hintText: 'venue_hint'.tr,
                              validator: (p0) {
                                if (p0!.isEmpty) {
                                  return 'this_field_required'.tr;
                                }
                                return null;
                              },
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Column(
                          children: [
                            SingleLineRow(
                              text: 'location_tip'.tr,
                              popup: KtStrings.locationTip,
                            ),
                            CustomTextField(
                              controller: locationTipController,
                              labelText: 'location_tip'.tr,
                              hintText: 'location_tip_hint'.tr,
                              validator: (p0) {
                                if (p0!.isEmpty) {
                                  return 'this_field_required'.tr;
                                }
                                return null;
                              },
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
                SingleLineRow(
                  text: 'meeting_frequency_question'.tr,
                  popup: KtStrings.meetingFrequency,
                ),
                DropdownButtonFormField<String>(
                  decoration: InputDecoration(
                    labelText: 'select_frequency'.tr,
                    hintText: 'meeting_frequency_hint'.tr,
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(
                        color: selectedvalue == null ? Colors.red : AppColors.blueButtonColor,
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderSide: const BorderSide(
                        color: AppColors.blueButtonColor,
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  isExpanded: true,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'please_select_frequency'.tr;
                    }
                    return null;
                  },
                  items: dropdownItems
                      .map(
                        (String item) => DropdownMenuItem<String>(
                          value: item,
                          child: Text(
                            item,
                            style: const TextStyle(
                              fontSize: 14,
                            ),
                          ),
                        ),
                      )
                      .toList(),
                  value: selectedvalue,
                  onChanged: (String? value) {
                    setState(() {
                      selectedvalue = value;
                      freqcyController.text = value!;
                    });
                  },
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  addMeeting() async {
    // Check if dropdown values are selected
    if (_dropdownValue == null) {
      Snack.showInfo(message1: 'please_select_event_type'.tr);
      return;
    }
    
    if (selectedvalue == null) {
      Snack.showInfo(message1: 'please_select_meeting_frequency'.tr);
      return;
    }

    if (formKey.currentState!.validate()) {
      dynamic request;
      final startDate = startDateController.text;
      final startTime = startTimeController.text;
      final endDate = endDateController.text;
      final endTime = endTimeController.text;

      final combinedStartTime = combineDateTime(startDate, startTime);
      final combinedEndTime = combineDateTime(endDate, endTime);
      final formattedStartTime = formatDateTime(combinedStartTime);
      final formattedEndTime = formatDateTime(combinedEndTime);

      final startDateTime = DateTime.tryParse(formattedStartTime);
      final endDateTime = DateTime.tryParse(formattedEndTime);

      // Set venue and location tip to meeting link if virtual event
      if (_dropdownValue == "VIRTUAL") {
        venueController.text = meetingLinkController.text.trim();
        locationTipController.text = meetingLinkController.text.trim();
      }

      // Also validate start and end dates
      if (startDateTime == null || endDateTime == null) {
        Snack.showInfo(message1: 'please_enter_valid_dates_times'.tr);
        return;
      }

      if (startDateTime.isAfter(endDateTime)) {
        Snack.showInfo(message1: 'start_date_before_end_date'.tr);
        return;
      }

      if (widget.isUpdating) {
        request = UpdateMeeting(
          id: widget.meeting?.id,
          title: titleController.text.trim(),
          description: desController.text.trim(),
          venue: venueController.text.trim(),
          eventType: _dropdownValue,
          startDate: startDateTime,
          endDate: endDateTime,
          frequency: selectedvalue,
          chamaId: chamaDataController.chama.value.chama?.id,
          memberId: chamaDataController.chama.value.member?.id,
          email: chamaDataController.chama.value.chama?.email,
          locationTip: locationTipController.text.trim(),
          latitude: box.read(CacheKeys.lat),
          longitude: box.read(CacheKeys.long),
        );
      } else {
        request = MeetingRequest(
          title: titleController.text.trim(),
          description: desController.text.trim(),
          venue: venueController.text.trim(),
          eventType: _dropdownValue,
          startDate: startDateTime,
          endDate: endDateTime,
          frequency: selectedvalue,
          chamaId: chamaDataController.chama.value.chama?.id,
          memberId: chamaDataController.chama.value.member?.id,
          email: chamaDataController.chama.value.chama?.email,
          locationTip: locationTipController.text.trim(),
          latitude: box.read(CacheKeys.lat),
          longitude: box.read(CacheKeys.long),
        );
      }
      bool res;
      if (widget.isUpdating) {
        res =
            await chamaController.addMeeting(request: request, isUpdate: true);
      } else {
        res = await chamaController.addMeeting(request: request);
      }
      if (res) {
        if (!mounted) return;
        Snack.show(res, chamaController.apiMessage.string);
        Navigator.pop(context);
        Get.offAllNamed(NavRoutes.meetings);
      } else {
        Snack.show(res, chamaController.apiMessage.string);
      }
    }
  }
}
