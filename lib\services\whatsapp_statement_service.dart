import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onekitty_admin/services/api_urls.dart';
import 'package:onekitty_admin/services/http_service.dart';
import 'package:onekitty_admin/helpers/show_toast.dart';

/// Service for requesting transaction statements via WhatsApp
class WhatsAppStatementService extends GetxService {
  final HttpService apiProvider = Get.find();
  final logger = Get.find<Logger>();
  
  final RxBool isRequestingStatement = false.obs;
  final RxString lastRequestStatus = ''.obs;

  /// Request event transaction statement via WhatsApp
  Future<bool> requestEventStatement({
    required BuildContext context,
    required int eventId,
    required String phoneNumber,
    StatementType statementType = StatementType.detailed,
    String? customMessage,
  }) async {
    try {
      isRequestingStatement(true);
      
      final response = await apiProvider.request(
        url: ApiUrls.REQUEST_WHATSAPP_STATEMENT,
        method: Method.POST,
        params: {
          'event_id': eventId,
          'phone_number': phoneNumber,
          'statement_type': statementType.name,
          'custom_message': customMessage,
        },
      );

      if (response.data['status'] ?? false) {
        lastRequestStatus.value = 'success';
        ToastUtils.showSuccessToast(
          context,
          response.data['message'] ?? 'Statement request sent successfully',
          'Success',
        );
        return true;
      } else {
        lastRequestStatus.value = 'failed';
        ToastUtils.showErrorToast(
          context,
          response.data['message'] ?? 'Failed to send statement request',
          'Error',
        );
        return false;
      }
    } catch (e) {
      logger.e('Error requesting WhatsApp statement: $e');
      lastRequestStatus.value = 'error';
      ToastUtils.showErrorToast(
        context,
        'An error occurred while requesting statement',
        'Error',
      );
      return false;
    } finally {
      isRequestingStatement(false);
    }
  }

  /// Request kitty transaction statement via WhatsApp
  Future<bool> requestKittyStatement({
    required BuildContext context,
    required int kittyId,
    required String phoneNumber,
    StatementType statementType = StatementType.detailed,
    String? customMessage,
  }) async {
    try {
      isRequestingStatement(true);
      
      final response = await apiProvider.request(
        url: ApiUrls.REQUEST_WHATSAPP_KITTY_STATEMENT,
        method: Method.POST,
        params: {
          'kitty_id': kittyId,
          'phone_number': phoneNumber,
          'statement_type': statementType.name,
          'custom_message': customMessage,
        },
      );

      if (response.data['status'] ?? false) {
        lastRequestStatus.value = 'success';
        ToastUtils.showSuccessToast(
          context,
          response.data['message'] ?? 'Statement request sent successfully',
          'Success',
        );
        return true;
      } else {
        lastRequestStatus.value = 'failed';
        ToastUtils.showErrorToast(
          context,
          response.data['message'] ?? 'Failed to send statement request',
          'Error',
        );
        return false;
      }
    } catch (e) {
      logger.e('Error requesting WhatsApp kitty statement: $e');
      lastRequestStatus.value = 'error';
      ToastUtils.showErrorToast(
        context,
        'An error occurred while requesting statement',
        'Error',
      );
      return false;
    } finally {
      isRequestingStatement(false);
    }
  }

  /// Request chama transaction statement via WhatsApp
  Future<bool> requestChamaStatement({
    required BuildContext context,
    required int chamaId,
    required String phoneNumber,
    StatementType statementType = StatementType.detailed,
    String? customMessage,
  }) async {
    try {
      isRequestingStatement(true);
      
      final response = await apiProvider.request(
        url: ApiUrls.REQUEST_WHATSAPP_CHAMA_STATEMENT,
        method: Method.POST,
        params: {
          'chama_id': chamaId,
          'phone_number': phoneNumber,
          'statement_type': statementType.name,
          'custom_message': customMessage,
        },
      );

      if (response.data['status'] ?? false) {
        lastRequestStatus.value = 'success';
        ToastUtils.showSuccessToast(
          context,
          response.data['message'] ?? 'Statement request sent successfully',
          'Success',
        );
        return true;
      } else {
        lastRequestStatus.value = 'failed';
        ToastUtils.showErrorToast(
          context,
          response.data['message'] ?? 'Failed to send statement request',
          'Error',
        );
        return false;
      }
    } catch (e) {
      logger.e('Error requesting WhatsApp chama statement: $e');
      lastRequestStatus.value = 'error';
      ToastUtils.showErrorToast(
        context,
        'An error occurred while requesting statement',
        'Error',
      );
      return false;
    } finally {
      isRequestingStatement(false);
    }
  }

  /// Show statement request dialog
  Future<void> showStatementRequestDialog({
    required BuildContext context,
    required String entityType, // 'event', 'kitty', 'chama'
    required int entityId,
    required String defaultPhoneNumber,
  }) async {
    final phoneController = TextEditingController(text: defaultPhoneNumber);
    StatementType selectedType = StatementType.detailed;
    
    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              title: Text('Request $entityType Statement'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextField(
                    controller: phoneController,
                    decoration: const InputDecoration(
                      labelText: 'WhatsApp Number',
                      hintText: '+254712345678',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.phone,
                  ),
                  const SizedBox(height: 16),
                  DropdownButtonFormField<StatementType>(
                    value: selectedType,
                    decoration: const InputDecoration(
                      labelText: 'Statement Type',
                      border: OutlineInputBorder(),
                    ),
                    items: StatementType.values.map((type) {
                      return DropdownMenuItem(
                        value: type,
                        child: Text(type.displayName),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          selectedType = value;
                        });
                      }
                    },
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
                Obx(() => ElevatedButton(
                  onPressed: isRequestingStatement.value
                      ? null
                      : () async {
                          bool success = false;
                          switch (entityType.toLowerCase()) {
                            case 'event':
                              success = await requestEventStatement(
                                context: context,
                                eventId: entityId,
                                phoneNumber: phoneController.text.trim(),
                                statementType: selectedType,
                              );
                              break;
                            case 'kitty':
                              success = await requestKittyStatement(
                                context: context,
                                kittyId: entityId,
                                phoneNumber: phoneController.text.trim(),
                                statementType: selectedType,
                              );
                              break;
                            case 'chama':
                              success = await requestChamaStatement(
                                context: context,
                                chamaId: entityId,
                                phoneNumber: phoneController.text.trim(),
                                statementType: selectedType,
                              );
                              break;
                          }
                          
                          if (success) {
                            Navigator.of(context).pop();
                          }
                        },
                  child: isRequestingStatement.value
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Text('Send Request'),
                )),
              ],
            );
          },
        );
      },
    );
  }
}

/// Types of statements that can be requested
enum StatementType {
  summary('summary', 'Summary'),
  detailed('detailed', 'Detailed'),
  transactions('transactions', 'Transactions Only');

  const StatementType(this.name, this.displayName);
  
  final String name;
  final String displayName;
}
