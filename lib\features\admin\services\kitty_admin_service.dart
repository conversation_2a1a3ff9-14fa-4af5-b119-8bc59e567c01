import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onekitty_admin/features/admin/api_urls.dart';
import 'package:onekitty_admin/models/kitty_model.dart';
import 'package:onekitty_admin/services/http_service.dart';

class KittyAdminService {
  final HttpService _apiProvider = HttpService();
  final Logger _logger = Logger();

  /// Fetch all kitties with pagination and filters
  Future<Map<String, dynamic>> fetchKitties({
    int page = 0,
    int size = 15,
    String? search,
    String? chamaId,
    String? frequency,
    String? kittyId,
    String? startDate,
    String? endDate,
  }) async {
    try {
      // Build query parameters
      final Map<String, dynamic> queryParams = {
        'page': page,
        'size': size,
      };

      if (search != null && search.isNotEmpty) {
        queryParams['search'] = search;
      }
      if (chamaId != null && chamaId.isNotEmpty) {
        queryParams['chama_id'] = chamaId;
      }
      if (frequency != null && frequency.isNotEmpty) {
        queryParams['frequency'] = frequency;
      }
      if (kittyId != null && kittyId.isNotEmpty) {
        queryParams['kitty_id'] = kittyId;
      }
      if (startDate != null && startDate.isNotEmpty) {
        queryParams['start_date'] = startDate;
      }
      if (endDate != null && endDate.isNotEmpty) {
        queryParams['end_date'] = endDate;
      }

      // Build URL with query parameters
      final queryString = queryParams.entries
          .map((e) => '${e.key}=${Uri.encodeComponent(e.value.toString())}')
          .join('&');
      
      final url = '${AdminApiUrls.getAllKitties}?$queryString';

      final response = await _apiProvider.request(
        method: Method.GET,
        url: url,
      );

      if (response.data['status'] ?? false) {
        final data = response.data['data'];
        final results = data['results'];
        final items = results['items'] as List;
        
        final kitties = items
            .map((item) => Kitty.fromJson(item['kitty']))
            .toList();

        return {
          'success': true,
          'kitties': kitties,
          'pagination': {
            'page': results['page'] ?? page,
            'size': results['size'] ?? size,
            'total_pages': results['total_pages'] ?? 1,
            'total_items': results['total_items'] ?? 0,
            'has_next': results['has_next'] ?? false,
            'has_previous': results['has_previous'] ?? false,
          },
          'message': response.data['message'] ?? 'Kitties fetched successfully',
        };
      } else {
        return {
          'success': false,
          'kitties': <Kitty>[],
          'message': response.data['message'] ?? 'Failed to fetch kitties',
        };
      }
    } catch (e) {
      _logger.e('Error fetching kitties: $e');
      return {
        'success': false,
        'kitties': <Kitty>[],
        'message': 'An error occurred while fetching kitties: ${e.toString()}',
      };
    }
  }

  /// Fetch a single kitty by ID
  Future<Map<String, dynamic>> fetchKittyById(int kittyId) async {
    try {
      final response = await _apiProvider.request(
        method: Method.GET,
        url: '${AdminApiUrls.getKittyById}$kittyId',
      );

      if (response.data['status'] ?? false) {
        final kittyData = response.data['data']['kitty'];
        final kitty = Kitty.fromJson(kittyData);

        return {
          'success': true,
          'kitty': kitty,
          'message': response.data['message'] ?? 'Kitty fetched successfully',
        };
      } else {
        return {
          'success': false,
          'kitty': null,
          'message': response.data['message'] ?? 'Failed to fetch kitty',
        };
      }
    } catch (e) {
      _logger.e('Error fetching kitty by ID: $e');
      return {
        'success': false,
        'kitty': null,
        'message': 'An error occurred while fetching kitty: ${e.toString()}',
      };
    }
  }

  /// Update kitty status
  Future<Map<String, dynamic>> updateKittyStatus({
    required int kittyId,
    required String status,
  }) async {
    try {
      final response = await _apiProvider.request(
        method: Method.POST,
        url: AdminApiUrls.updateKittyStatus,
        params: {
          'kitty_id': kittyId,
          'status': status,
        },
      );

      return {
        'success': response.data['status'] ?? false,
        'message': response.data['message'] ?? 'Status update failed',
      };
    } catch (e) {
      _logger.e('Error updating kitty status: $e');
      return {
        'success': false,
        'message': 'An error occurred while updating kitty status: ${e.toString()}',
      };
    }
  }

  /// Delete kitty
  Future<Map<String, dynamic>> deleteKitty(int kittyId) async {
    try {
      final response = await _apiProvider.request(
        method: Method.DELETE,
        url: '${AdminApiUrls.deleteKitty}$kittyId',
      );

      return {
        'success': response.data['status'] ?? false,
        'message': response.data['message'] ?? 'Delete operation failed',
      };
    } catch (e) {
      _logger.e('Error deleting kitty: $e');
      return {
        'success': false,
        'message': 'An error occurred while deleting kitty: ${e.toString()}',
      };
    }
  }

  /// Export kitties data
  Future<Map<String, dynamic>> exportKittiesData({
    required String format, // 'csv', 'excel', 'pdf'
    Map<String, dynamic>? filters,
  }) async {
    try {
      final response = await _apiProvider.request(
        method: Method.POST,
        url: AdminApiUrls.exportData,
        params: {
          'type': 'kitties',
          'format': format,
          'filters': filters ?? {},
        },
      );

      return {
        'success': response.data['status'] ?? false,
        'download_url': response.data['data']?['download_url'],
        'message': response.data['message'] ?? 'Export failed',
      };
    } catch (e) {
      _logger.e('Error exporting kitties data: $e');
      return {
        'success': false,
        'message': 'An error occurred while exporting data: ${e.toString()}',
      };
    }
  }
}
