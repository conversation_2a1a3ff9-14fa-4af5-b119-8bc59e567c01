import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:onekitty_admin/helpers/colors.dart';

/// A cute kitty-themed loading widget that fits the OneKitty project theme
class KittyLoadingWidget extends StatelessWidget {
  final String? message;
  final double size;
  final Color? color;
  final bool showMessage;

  const KittyLoadingWidget({
    super.key,
    this.message,
    this.size = 60.0,
    this.color,
    this.showMessage = true,
  });

  @override
  Widget build(BuildContext context) {
    final loadingColor = color ?? AppColors.primary;
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Cute kitty loading animation
          Stack(
            alignment: Alignment.center,
            children: [
              // Outer spinning circle
              SpinKitRing(
                color: loadingColor.withOpacity(0.3),
                size: size + 20,
                lineWidth: 3.0,
              ),
              // Inner pulsing circle with kitty icon
              SpinKitPulse(
                color: loadingColor.withOpacity(0.1),
                size: size,
              ),
              // Kitty icon in the center
              Icon(
                Icons.pets,
                size: size * 0.4,
                color: loadingColor,
              ),
            ],
          ),
          
          if (showMessage) ...[
            const SizedBox(height: 16),
            // Loading message with cute kitty puns
            Text(
              message ?? _getRandomKittyMessage(),
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  String _getRandomKittyMessage() {
    final messages = [
      'Purr-fectly loading...',
      'Fetching data... meow!',
      'Loading paw-some content...',
      'Cat-ching up with data...',
      'Paws for a moment...',
      'Whiskers are working...',
      'Fur-real loading...',
      'Kitten around with data...',
    ];
    
    return messages[DateTime.now().millisecond % messages.length];
  }
}

/// A compact version of the kitty loading widget for smaller spaces
class CompactKittyLoadingWidget extends StatelessWidget {
  final double size;
  final Color? color;

  const CompactKittyLoadingWidget({
    super.key,
    this.size = 24.0,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    final loadingColor = color ?? AppColors.primary;
    
    return SizedBox(
      width: size,
      height: size,
      child: Stack(
        alignment: Alignment.center,
        children: [
          SpinKitRing(
            color: loadingColor.withOpacity(0.6),
            size: size,
            lineWidth: 2.0,
          ),
          Icon(
            Icons.pets,
            size: size * 0.5,
            color: loadingColor,
          ),
        ],
      ),
    );
  }
}

/// A kitty loading overlay for full-screen loading states
class KittyLoadingOverlay extends StatelessWidget {
  final String? message;
  final bool isVisible;
  final Widget child;

  const KittyLoadingOverlay({
    super.key,
    this.message,
    required this.isVisible,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        if (isVisible)
          Container(
            color: Colors.black.withOpacity(0.3),
            child: KittyLoadingWidget(
              message: message,
              size: 80.0,
            ),
          ),
      ],
    );
  }
}

/// A shimmer-like loading widget with kitty theme for table rows
class KittyShimmerLoading extends StatefulWidget {
  final int itemCount;
  final double height;

  const KittyShimmerLoading({
    super.key,
    this.itemCount = 5,
    this.height = 60.0,
  });

  @override
  State<KittyShimmerLoading> createState() => _KittyShimmerLoadingState();
}

class _KittyShimmerLoadingState extends State<KittyShimmerLoading>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: List.generate(
        widget.itemCount,
        (index) => Container(
          margin: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 16.0),
          height: widget.height,
          child: AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8.0),
                  gradient: LinearGradient(
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                    colors: [
                      Colors.grey[300]!,
                      Colors.grey[100]!,
                      Colors.grey[300]!,
                    ],
                    stops: [
                      0.0,
                      _animation.value,
                      1.0,
                    ],
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      margin: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color: AppColors.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Icon(
                        Icons.pets,
                        size: 20,
                        color: AppColors.primary.withOpacity(0.3),
                      ),
                    ),
                    Expanded(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            height: 12,
                            width: double.infinity,
                            margin: const EdgeInsets.only(right: 40),
                            decoration: BoxDecoration(
                              color: Colors.grey[200],
                              borderRadius: BorderRadius.circular(6),
                            ),
                          ),
                          const SizedBox(height: 8),
                          Container(
                            height: 10,
                            width: 150,
                            decoration: BoxDecoration(
                              color: Colors.grey[200],
                              borderRadius: BorderRadius.circular(5),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
