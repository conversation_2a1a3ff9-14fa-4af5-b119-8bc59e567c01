import 'dart:async';
import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:fl_country_code_picker/fl_country_code_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_contacts/contact.dart';
import 'package:flutter_contacts/properties/phone.dart';
import 'package:flutter_quill/flutter_quill.dart' as q;
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:onekitty_admin/configs/country_specifics.dart';
import 'package:onekitty_admin/features/onekitty/widgets/custom_international_phone_input.dart';
import 'package:onekitty_admin/features/onekitty/widgets/getx_contact_picker.dart';
import 'package:onekitty_admin/features/onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/controllers/controllers.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/controllers/user_ktty_controller.dart';
import 'package:onekitty_admin/helpers/colors.dart';
import 'package:onekitty_admin/helpers/extensions/text_styles.dart';
import 'package:onekitty_admin/helpers/show_toast.dart';
import 'package:onekitty_admin/models/chama/chamaDto.dart';
import 'package:onekitty_admin/models/chama/configs_model.dart';
import 'package:onekitty_admin/features/onekitty/controllers/contact_picker_controller.dart';
import 'package:onekitty_admin/features/onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty_admin/services/analytics.dart';
import 'package:onekitty_admin/utils/cache_keys.dart';
import 'package:onekitty_admin/utils/common_strings.dart';
import 'package:onekitty_admin/utils/iswysiwyg.dart';
import '../../../../../../../../utils/utils_exports.dart';
import '../contribution/widgets/date_picker.dart';
import '../contribution/widgets/success.dart';

enum Role {
  admin,
  treasurer,
  secretary,
  member,
}

// Controller class for ChamaStepper
class ChamaStepperController extends GetxController {
  final RxInt currentStep = 0.obs;
  final RxMap<String, Role> roleStatusMap = <String, Role>{}.obs;
  final Rx<Role> selectedStatus = Role.member.obs;
  final RxString selectedRole = "MEMBER".obs;
  final RxString selectedvalue = "".obs;
  final RxList<String> dropdownItems = <String>[].obs;
  final RxList<String> roleItems = <String>[].obs;
  final RxString deviceModel = "".obs;
  final RxString imeiCode = "".obs;
  final RxString invitePhone = "".obs;
  final RxBool isInvite = true.obs;
  final RxBool isFormValid = false.obs;

  // Text controllers
  final TextEditingController chamaNameController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController descrController = TextEditingController();
  final TextEditingController amountController = TextEditingController();
  final TextEditingController linkController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController freqcyController = TextEditingController();
  final TextEditingController accountrefController = TextEditingController();
  final TextEditingController refController = TextEditingController();
  final TextEditingController firstNameController = TextEditingController();
  final TextEditingController lastNameController = TextEditingController();
  final TextEditingController dateController = TextEditingController();
  final TextEditingController timeController = TextEditingController();

  // Other properties
  final q.QuillController quillController = q.QuillController.basic();
  final Rx<PhoneNumber> number = CountryConfig.phoneNumber.obs;
  final Rx<DateTime> combinedDateTime = DateTime.now().obs;
  final countryPicker = const FlCountryCodePicker();
  Rx<CountryCode?> countryCode = Rx<CountryCode?>(null);
  final box = GetStorage();

  // Stream controller for timer
  final _streamController = StreamController<void>();
  Stream<void> get stream => _streamController.stream;

  @override
  void onInit() {
    super.onInit();
    refController.text = Get.find<GlobalControllers>().getCode();
    initializeData();
  }

  @override
  void onClose() {
    _streamController.close();
    chamaNameController.dispose();
    emailController.dispose();
    descrController.dispose();
    amountController.dispose();
    linkController.dispose();
    phoneController.dispose();
    freqcyController.dispose();
    accountrefController.dispose();
    refController.dispose();
    firstNameController.dispose();
    lastNameController.dispose();
    dateController.dispose();
    timeController.dispose();
    super.onClose();
  }

  void initializeData() {
    final chamaController = Get.find<ChamaController>();
    chamaController.getConfigs();
    dateController.text = DateFormat.yMd().format(DateTime.now());
    timeController.text = DateFormat().add_jm().format(DateTime.now());
    roleItems.value = chamaController.roles.map((role) => role.role).toList();
    getfrequency();
    getDeviceInfo();
    startTimer();
  }

  void startTimer() {
    Timer.periodic(const Duration(seconds: 1), (timer) {
      _streamController.add(null);
    });
  }

  void getfrequency() {
    final chamaController = Get.find<ChamaController>();
    dropdownItems.value = chamaController.frequencies
        .map((frequency) => frequency.frequency)
        .toList();
    if (chamaController.frequencies.isNotEmpty) {
      selectedvalue.value = chamaController.frequencies.first.frequency;
    }
  }

  void getDeviceInfo() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      imeiCode.value = androidInfo.id;
      deviceModel.value = androidInfo.model;
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      imeiCode.value = iosInfo.identifierForVendor!;
      deviceModel.value = iosInfo.model;
    } else {
      WebBrowserInfo webBrowserInfo = await deviceInfo.webBrowserInfo;
      imeiCode.value = webBrowserInfo.userAgent!;
    }
  }

  void updateContact(
      String id, String newFirstName, String newLastName, String role) {
    final contactController = Get.find<ContactPickerController>();
    final selectedContacts = contactController.selectedContacts;

    final contact = selectedContacts
        .firstWhere((contact) => contact.phones.first.normalizedNumber == id);
    contact.name.first = newFirstName;
    contact.name.last = newLastName;
    contact.name.prefix = role;
  }
}

class ChamaStepper extends StatefulWidget {
  const ChamaStepper({super.key});

  @override
  _ChamaStepperState createState() => _ChamaStepperState();
}

class _ChamaStepperState extends State<ChamaStepper> {
  final ChamaStepperController controller = Get.put(ChamaStepperController());
  final ChamaController _chamaController = Get.find<ChamaController>();
  final UserKittyController _userController = Get.find<UserKittyController>();
  final ContactPickerController contactController =
      Get.put(ContactPickerController());

  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final GlobalKey<FormState> formKey1 = GlobalKey<FormState>();
  final GlobalKey<FormState> formKey2 = GlobalKey<FormState>();
  final GlobalKey _tooltipKey1 = GlobalKey();
  final GlobalKey _tooltipKey2 = GlobalKey();
  final GlobalKey _tooltipKey3 = GlobalKey();

  var params = Get.parameters;

  void selectContact(Contact selectedContact, BuildContext context) {
    contactController.selectContact(selectedContact);
  }

  void addMyPhone() {
    String phoneNumber = _userController.getLocalUser()!.phoneNumber!;
    Phone phone = Phone(phoneNumber, normalizedNumber: phoneNumber);
    Contact contact = Contact(phones: [phone]);
    selectContact(contact, context);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        minimum: const EdgeInsets.symmetric(vertical: 8),
        child: Column(
          children: [
            const RowAppBar(),
            Text('create_a_chama_title'.tr,
                style: Theme.of(context)
                    .textTheme
                    .titleLarge
                    ?.copyWith(fontWeight: FontWeight.bold, fontSize: 22)),
            const SizedBox(height: 10),
            Expanded(
              child: Theme(
                data: Theme.of(context).copyWith(
                    colorScheme:
                        const ColorScheme.light(primary: AppColors.primary)),
                child: Obx(() => Stepper(
                      connectorThickness: 2,
                      margin: const EdgeInsets.all(1),
                      elevation: 0,
                      currentStep: controller.currentStep.value,
                      type: StepperType.horizontal,
                      steps: getSteps(),
                      onStepContinue: () {
                        final isLastStep = controller.currentStep.value ==
                            getSteps().length - 1;
                        if (!isLastStep) {
                          controller.currentStep.value += 1;
                        }
                      },
                      onStepCancel: controller.currentStep.value == 0
                          ? null
                          : () {
                              controller.currentStep.value -= 1;
                            },
                      controlsBuilder: (context, details) {
                        return buildControls(context, details);
                      },
                    )),
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<Step> getSteps() => [
        Step(
            state: controller.currentStep.value > 0
                ? StepState.complete
                : StepState.indexed,
            title: const Divider(),
            label: Text('chama_details'.tr),
            content: buildStepOne(context),
            isActive: controller.currentStep.value >= 0),
        Step(
            state: controller.currentStep.value > 1
                ? StepState.complete
                : StepState.indexed,
            title: const Divider(),
            label: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text('details'.tr),
              ],
            ),
            content: buildStepTwo(context),
            isActive: controller.currentStep.value >= 1),
        Step(
            state: controller.currentStep.value == 1
                ? StepState.complete
                : StepState.indexed,
            title: const Divider(),
            label: Center(child: Text('members'.tr)),
            content: buildStepThree(context),
            isActive: controller.currentStep.value >= 2),
      ];

  Widget buildControls(BuildContext context, ControlsDetails details) {
    return SizedBox(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          controller.currentStep.value != 0
              ? OutlinedButton(
                  onPressed: details.onStepCancel, child: Text('back'.tr))
              : Container(),
          if (controller.currentStep.value == 2)
            Obx(
              () => CustomKtButton(
                isLoading: _chamaController.isAdding.isTrue,
                onPress: () async {
                  AnalyticsEngine.userCreatesKitty();
                  final selected = contactController.selectedContacts;
                  try {
                    final List<Member> members = [];
                    for (var index = 0 + 1; index < selected.length; index++) {
                      final contact = selected[index];
                      final phoneNumber = contact.phones.first.normalizedNumber;

                      final member = Member(
                        phoneNumber: phoneNumber,
                        firstName: contact.name.first,
                        secondName: contact.name.last,
                        role: contact.name.prefix,
                        receivingOrder: 1 + index,
                        status: "ACTIVE",
                      );
                      members.add(member);
                    }
                    final chamaMembers = MembersDto(
                        chamaId: _chamaController.createRes["ID"],
                        members: members);

                    final resp = await _chamaController.addMember(
                        memebersDto: chamaMembers);
                    if (resp) {
                      ToastUtils.showSuccessToast(
                        context,
                        _chamaController.apiMessage.string,
                        'success'.tr,
                      );

                      Get.to(() => SucessPage(
                            text: 'created_success'.tr,
                            kittyName: controller.chamaNameController.text,
                          ));
                      contactController.clearSelection();
                    } else {
                      ToastUtils.showErrorToast(
                        context,
                        _chamaController.apiMessage.string,
                        'error'.tr,
                      );
                    }
                  } catch (e) {
                    ToastUtils.showErrorToast(
                      context,
                      'an_error_occurred_adding_member'.tr,
                      "Error",
                    );
                  }
                },
                width: 100,
                height: 40,
                btnText: 'finish'.tr,
              ),
            ),
          if (controller.currentStep.value == 1)
            Obx(
              () => Padding(
                padding: const EdgeInsets.all(8.0),
                child: CustomKtButton(
                  isLoading: _chamaController.isloading.isTrue,
                  onPress: () async {
                    if (formKey1.currentState!.validate()) {
                      int? referCode;
                      if (params.isNotEmpty) {
                        referCode = int.tryParse(params["id"].toString());
                      }
                      DateTime date = DateFormat.yMd()
                          .parse(controller.dateController.text);

                      TimeOfDay time = TimeOfDay.fromDateTime(DateFormat.Hm()
                          .parse(controller.timeController.text));

                      DateTime combinedDateTime = DateTime(
                        date.year,
                        date.month,
                        date.day,
                        time.hour,
                        time.minute,
                      );
                      try {
                        final createDto = CreateDto(
                            title: controller.chamaNameController.text,
                            description:
                                quilltoHtml(controller.quillController),
                            phoneNumber:
                                '+${_userController.getLocalUser()?.phoneNumber ?? ''}',
                            countryCode:
                                _userController.getLocalUser()!.countryCode ??
                                    CountryConfig.isoCode,
                            email: controller.emailController.text,
                            refererCode: int.tryParse(
                                controller.refController.text.trim()),
                            whatsAppLink: controller.linkController.text,
                            frequency: controller.freqcyController.text,
                            nextOccurrence: combinedDateTime.toUtc(),
                            amount: num.parse(
                              controller.amountController.text.trim(),
                            ),
                            imeiCode: controller.imeiCode.value,
                            deviceModel: controller.deviceModel.value,
                            latitude: controller.box.read(CacheKeys.lat),
                            longitude: controller.box.read(CacheKeys.long));

                        final res = await _chamaController.createChama(
                            createDto: createDto);
                        if (res) {
                          Get.find<GlobalControllers>().clearCode();
                          ToastUtils.showSuccessToast(
                            context,
                            _chamaController.apiMessage.string,
                            'success'.tr,
                          );
                          addMyPhone();
                          details.onStepContinue!();
                        } else {
                          ToastUtils.showErrorToast(
                            context,
                            _chamaController.apiMessage.string,
                            'error'.tr,
                          );
                        }
                      } catch (e) {}
                    }
                  },
                  height: 45.h,
                  width: 90.w,
                  btnText: 'next'.tr,
                ),
              ),
            ),
          if (controller.currentStep.value == 0)
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: CustomKtButton(
                onPress: () {
                  if (formKey.currentState!.validate()) {
                    details.onStepContinue!();
                  }
                },
                height: 45.h,
                width: 90.w,
                btnText: 'next'.tr,
              ),
            ),
        ],
      ),
    );
  }

  Widget buildStepOne(BuildContext context) {
    return Form(
      key: formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('chama_name'.tr,
              style: Theme.of(context)
                  .textTheme
                  .titleLarge
                  ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15)),
          CustomTextField(
            controller: controller.chamaNameController,
            hintText: 'chama_name_hint'.tr,
            labelText: 'chama_name_label'.tr,
            isRequired: true,
            validator: (p0) {
              if (p0!.isEmpty) {
                return 'chama_name_required'.tr;
              } else if (p0.length < 5) {
                return 'chama_name_length'.tr;
              }
              return null;
            },
          ),
          Text('chama_description'.tr,
              style: Theme.of(context)
                  .textTheme
                  .titleLarge
                  ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15)),
          SizedBox(height: 5.h),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.blueAccent.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8.0),
                border: Border.all(
                  color: AppColors.blueButtonColor,
                  width: 1.0,
                ),
              ),
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 8),
                  q.QuillSimpleToolbar(
                    config: const q.QuillSimpleToolbarConfig(
                      multiRowsDisplay: false,
                    ),
                    controller: controller.quillController,
                  ),
                  const SizedBox(height: 15),
                  q.QuillEditor.basic(
                    controller: controller.quillController,
                    config: q.QuillEditorConfig(
                      placeholder: 'chama_description_hint'.tr,
                      autoFocus: false,
                      enableInteractiveSelection: true,
                    ),
                  ),
                  const SizedBox(height: 8),
                ],
              ),
            ),
          ),
          SizedBox(height: 10.h),
          Text(
            'whatsapp_group_link'.tr,
            style: Theme.of(context)
                .textTheme
                .titleLarge
                ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15),
          ),
          SizedBox(height: 5.h),
          CustomTextField(
            labelText: 'group_link'.tr,
            controller: controller.linkController,
          ),
          SizedBox(height: 10.h),
          Text('enter_referer_code'.tr,
              style: Theme.of(context)
                  .textTheme
                  .titleLarge
                  ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15)),
          SizedBox(height: 5.h),
          CustomTextField(
            controller: controller.refController,
            hintText: 'referral_code_hint'.tr,
            labelText: 'referral_code'.tr,
          ),
        ],
      ),
    );
  }

  Widget buildStepTwo(BuildContext context) {
    String? description;
    for (Frequency freq in _chamaController.frequencies) {
      if (freq.frequency == controller.freqcyController.text) {
        description = freq.description;
        break;
      }
    }
    return Form(
      key: formKey1,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'enter_chama_email'.tr,
            style: Theme.of(context)
                .textTheme
                .titleLarge
                ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15),
          ),
          SizedBox(height: 5.h),
          CustomTextField(
            controller: controller.emailController,
            hintText: 'email_hint'.tr,
            labelText: "",
            validator: (p0) {
              if (p0!.isEmpty) {
                return 'email_required'.tr;
              } else if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                  .hasMatch(p0)) {
                return 'email_invalid'.tr;
              }
              return null;
            },
          ),
          Row(
            children: [
              Text('how_often_contribute'.tr,
                  style: Theme.of(context)
                      .textTheme
                      .titleLarge
                      ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15)),
              const Spacer(),
              GestureDetector(
                onTap: () {
                  final dynamic tooltip = _tooltipKey1.currentState;
                  tooltip.ensureTooltipVisible();
                },
                child: Tooltip(
                  key: _tooltipKey1,
                  message: KtStrings.often,
                  child: CustomImageView(
                    imagePath: AssetUrl.imgInbox,
                    height: 15.h,
                    width: 15.w,
                    margin: EdgeInsets.only(left: 3.w, top: 2.h, bottom: 3.h),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 5.h),
          Obx(() => DropdownButtonFormField<String>(
                decoration: InputDecoration(
                  labelText: 'select_frequency'.tr,
                  fillColor: Colors.blueAccent.withOpacity(0.1),
                ),
                isExpanded: true,
                items: controller.dropdownItems
                    .map(
                      (String item) => DropdownMenuItem<String>(
                        value: item,
                        child: Text(
                          item,
                          style: const TextStyle(fontSize: 14),
                        ),
                      ),
                    )
                    .toList(),
                value: controller.selectedvalue.value.isEmpty
                    ? null
                    : controller.selectedvalue.value,
                onChanged: (String? value) {
                  controller.selectedvalue.value = value!;
                  controller.freqcyController.text = value;
                },
              )),
          Text(
            description ?? "",
            style: const TextStyle(fontStyle: FontStyle.italic),
          ),
          SizedBox(height: 5.h),
          Row(
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 8.0),
                child: Text('how_much_contribute'.tr,
                    style: Theme.of(context)
                        .textTheme
                        .titleLarge
                        ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15)),
              ),
              const Spacer(),
              GestureDetector(
                onTap: () {
                  final dynamic tooltip = _tooltipKey2.currentState;
                  tooltip.ensureTooltipVisible();
                },
                child: Tooltip(
                  key: _tooltipKey2,
                  message: KtStrings.amount,
                  child: CustomImageView(
                    imagePath: AssetUrl.imgInbox,
                    height: 15.h,
                    width: 15.w,
                    margin: EdgeInsets.only(left: 3.w, top: 2.h, bottom: 3.h),
                  ),
                ),
              ),
            ],
          ),
          CustomTextField(
            inputFormatters: <TextInputFormatter>[
              FilteringTextInputFormatter.allow(RegExp("[0-9]")),
            ],
            controller: controller.amountController,
            hintText: 'amount_hint'.tr,
            isRequired: true,
            labelText: 'amount'.tr,
            validator: (p0) {
              if (p0!.isEmpty) {
                return 'amount_required'.tr;
              }
              return null;
            },
          ),
          const SizedBox(height: 10),
          Row(
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 8.0),
                child: Text(
                    '${'set_deadline'.tr} ${controller.freqcyController.text} ${'contribution'.tr}',
                    style: Theme.of(context)
                        .textTheme
                        .titleLarge
                        ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15)),
              ),
              const Spacer(),
              GestureDetector(
                onTap: () {
                  final dynamic tooltip = _tooltipKey3.currentState;
                  tooltip.ensureTooltipVisible();
                },
                child: Tooltip(
                  key: _tooltipKey3,
                  message: KtStrings.deadline,
                  child: CustomImageView(
                    imagePath: AssetUrl.imgInbox,
                    height: 15.h,
                    width: 15.w,
                    margin: EdgeInsets.only(left: 3.w, top: 2.h, bottom: 3.h),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          DatePick(
            date: controller.dateController,
            time: controller.timeController,
            combinedDateTime: controller.combinedDateTime.value,
          ),
          SizedBox(height: 10.h),
        ],
      ),
    );
  }

  Widget buildStepThree(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        width: double.maxFinite,
        padding: const EdgeInsets.all(2),
        child: Column(
          children: [
            Text('invite_chama_members'.tr, style: theme.textTheme.titleLarge),
            SizedBox(height: 8.h),
            _buildFrame(context),
            SizedBox(height: 8.h),
            StreamBuilder<void>(
              stream: controller.stream,
              builder: (context, snapshot) {
                return buildInviteContacts(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFrame(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: 2.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [SizedBox(height: 20.h), _inputPhoneNumber(context)],
      ),
    );
  }

  Widget _inputPhoneNumber(BuildContext context) {
    return Form(
      key: formKey2,
      child: Column(
        children: [
          CustomInternationalPhoneInput(
            onInputChanged: (num) {
              controller.invitePhone.value = num.phoneNumber!;
            },
            controller: controller.phoneController,
          ),
          SizedBox(height: 5.h),
          CustomElevatedButton(
              leftIcon: Container(
                margin: EdgeInsets.only(right: 1.w),
                child: CustomImageView(
                    imagePath: AssetUrl.imgPlus, height: 15.h, width: 15.w),
              ),
              onPressed: () {
                if (formKey2.currentState!.validate()) {
                  String phoneNumber = controller.invitePhone.value;

                  Phone phone =
                      Phone(phoneNumber, normalizedNumber: phoneNumber);

                  Contact contact = Contact(
                    phones: [phone],
                  );
                  selectContact(contact, context);
                  controller.phoneController.clear();
                }
              },
              height: 45.h,
              width: 300.w,
              text: 'add'.tr,
              buttonStyle: CustomButtonStyles.fillIndigR,
              buttonTextStyle: CustomTextStyles.titleSmallWhiteA700),
        ],
      ),
    );
  }

  Widget _buildAddNumber(BuildContext context) {
    return IconButton(
        icon: const Icon(Icons.contacts_outlined),
        onPressed: () async {
          final List<Contact>? selectedContacts =
              await Get.to(() => GetXContactPicker(
                    mode: ContactPickerMode.multiple,
                    display: ContactPickerDisplay.fullScreen,
                    title: 'select_contacts_title'.tr,
                  ));

          if (selectedContacts != null && selectedContacts.isNotEmpty) {
            for (var contact in selectedContacts) {
              selectContact(contact, context);
            }
          }
        });
  }

  Widget buildInviteContacts(BuildContext context) {
    return Obx(() {
      final selectedContacts = contactController.selectedContacts;

      // Check if selected contacts list is empty
      if (selectedContacts.isEmpty) {
        return CustomImageView(
          imagePath: AssetUrl.imgGroup13,
          height: 150.h,
          width: 254.w,
        );
      } else {
        final myheight = selectedContacts.length * 80.h;
        return Container(
          height: myheight <= 240.h
              ? 240.h
              : myheight >= 500.h
                  ? 500.h
                  : myheight,
          margin: EdgeInsets.only(left: 2.h),
          padding: EdgeInsets.symmetric(horizontal: 2.h, vertical: 17.h),
          decoration: AppDecoration.outlineGray
              .copyWith(borderRadius: BorderRadiusStyle.roundedBorder8),
          child: ListView.builder(
            itemCount: selectedContacts.length,
            itemBuilder: (context, index) {
              final selectedContact = selectedContacts[index];

              Role roleStatus = controller.roleStatusMap[
                      selectedContact.phones.first.normalizedNumber] ??
                  Role.member;

              return Container(
                decoration: BoxDecoration(
                    borderRadius: BorderRadiusStyle.roundedBorder8),
                child: Column(
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomImageView(
                          imagePath: AssetUrl.dotSix,
                          height: 25.h,
                          width: 25.w,
                          margin: EdgeInsets.only(right: 3.h),
                        ),
                        Opacity(
                          opacity: 0.5,
                          child: Padding(
                            padding: EdgeInsets.only(top: 6.h, bottom: 8.h),
                            child: Text(
                              "${index + 1}",
                              style: theme.textTheme.titleSmall!,
                            ),
                          ),
                        ),
                        CustomImageView(
                          imagePath: AssetUrl.imgPerson,
                          height: 25.h,
                          width: 25.w,
                          margin: EdgeInsets.only(left: 3.h),
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: EdgeInsets.only(
                                  left: 6.h, top: 1.h, bottom: 1.h),
                              child: Text(
                                  index == 0
                                      ? _userController
                                          .getLocalUser()!
                                          .firstName!
                                      : "${selectedContact.name.first} ${selectedContact.name.last}",
                                  overflow: TextOverflow.ellipsis,
                                  style: CustomTextStyles.titleSmallGray90001),
                            ),
                            Padding(
                              padding: EdgeInsets.only(
                                  left: 6.h, top: 1.h, bottom: 1.h),
                              child: Text(selectedContact.phones.first.number,
                                  overflow: TextOverflow.ellipsis,
                                  style: CustomTextStyles.titleSmallGray90001),
                            ),
                            selectedContact.name.prefix == "CHAIRPERSON"
                                ? CustomImageView(
                                    imagePath: AssetUrl.crownsv,
                                    height: 18.h,
                                    width: 18.w,
                                    margin: EdgeInsets.symmetric(vertical: 9.h),
                                  )
                                : const SizedBox.shrink(),
                            index == 0
                                ? Text(
                                    'chairperson'.tr,
                                    style: TextStyle(
                                        fontStyle: FontStyle.italic,
                                        color: getRoleColors("CHAIRPERSON"),
                                        fontWeight: FontWeight.bold),
                                  )
                                : Text(
                                    selectedContact.name.prefix,
                                    style: context.dividerTextLarge?.copyWith(
                                        fontWeight: FontWeight.bold,
                                        color: getRoleColors(
                                            selectedContact.name.prefix)),
                                  ),
                          ],
                        ),
                        const Spacer(),
                        InkWell(
                          onTap: () {
                            contactController.removeContact(selectedContact);
                            controller.roleStatusMap.remove(
                                selectedContact.phones.first.normalizedNumber);
                          },
                          child: index == 0
                              ? const SizedBox()
                              : CustomImageView(
                                  imagePath: AssetUrl.imgIconoirCancel,
                                  height: 18.h,
                                  width: 18.w,
                                  margin: EdgeInsets.symmetric(
                                      vertical: 9.h, horizontal: 5.h),
                                ),
                        ),
                        index == 0
                            ? const SizedBox.shrink()
                            : IconButton(
                                icon: const Icon(
                                  Icons.edit,
                                  color: AppColors.blueButtonColor,
                                ),
                                padding: EdgeInsets.symmetric(vertical: 10.h),
                                onPressed: () {
                                  _chamaOptionsDialog(context, roleStatus,
                                      index, selectedContact);
                                },
                              )
                      ],
                    ),
                    SizedBox(height: 2.h),
                    const Divider()
                  ],
                ),
              );
            },
          ),
        );
      }
    });
  }

  void _chamaOptionsDialog(BuildContext context, Role roleStatus, int index,
      Contact selectedContact) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        controller.firstNameController.text = selectedContact.name.first;
        controller.lastNameController.text = selectedContact.name.last;
        controller.selectedRole.value = selectedContact.name.prefix;

        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {
            return Align(
              alignment: Alignment.centerRight,
              child: AlertDialog(
                title: Text(
                  'update_member_details_title'.tr,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                content: SizedBox(
                  height: 250.h,
                  child: Column(
                    children: [
                      CustomTextField(
                        controller: controller.firstNameController,
                        labelText: 'enter_first_name'.tr,
                      ),
                      SizedBox(height: 8.h),
                      CustomTextField(
                        controller: controller.lastNameController,
                        labelText: 'enter_last_name'.tr,
                      ),
                      SizedBox(height: 8.h),
                      Obx(() => DropdownButtonFormField<String>(
                            decoration: InputDecoration(
                              labelText: 'select_role'.tr,
                              fillColor: Colors.blueAccent.withOpacity(0.1),
                            ),
                            isExpanded: true,
                            items: controller.roleItems
                                .map(
                                  (String item) => DropdownMenuItem<String>(
                                    value: item,
                                    child: Text(
                                      item,
                                      style: const TextStyle(fontSize: 14),
                                    ),
                                  ),
                                )
                                .toList(),
                            value: controller.selectedRole.value,
                            onChanged: (String? value) {
                              controller.selectedRole.value = value!;
                            },
                          )),
                    ],
                  ),
                ),
                actions: [
                  CustomElevatedButton(
                    text: 'save'.tr,
                    onPressed: () {
                      try {
                        controller.updateContact(
                            selectedContact.phones.first.normalizedNumber,
                            controller.firstNameController.text,
                            controller.lastNameController.text,
                            controller.selectedRole.value);
                      } catch (e) {}

                      Navigator.of(context).pop(); // Close the dialog
                    },
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }
}
