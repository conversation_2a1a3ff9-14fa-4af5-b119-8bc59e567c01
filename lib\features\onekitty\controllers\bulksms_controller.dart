import 'package:flutter/material.dart';
import 'package:flutter_contacts/contact.dart';
import 'package:get_storage/get_storage.dart';
import 'package:logger/logger.dart';
import 'package:onekitty_admin/helpers/show_toast.dart';
import 'package:onekitty_admin/models/auth/user_model.dart';
import 'package:onekitty_admin/models/bulkSms/messagesDTO.dart';
import 'package:onekitty_admin/models/bulkSms/msg_model.dart';
import 'package:onekitty_admin/models/bulkSms/singleMsg.dart';
import 'package:onekitty_admin/models/bulk_sms/sms_groups.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/home/<USER>/crt_sms_screen.dart';
import 'package:onekitty_admin/services/api_urls.dart';
import 'package:onekitty_admin/services/http_service.dart';
import 'package:get/get.dart';
import 'package:onekitty_admin/utils/cache_keys.dart';

class BulkSMSController extends GetxController implements GetxService {
  final HttpService apiProvider = Get.find();
  final dataController = Get.put(MessageController());

  Rx<UserModelLatest> user = UserModelLatest().obs;
  final box = Get.find<GetStorage>();
  final logger = Get.find<Logger>();
  RxString apiMessage = ''.obs;
  RxString apiMessage2 = ''.obs;

  RxMap resData = {}.obs;
  RxMap sendData = {}.obs;

  RxBool isConfirming = false.obs;
  RxBool isSending = false.obs;
  RxBool isgetloading = false.obs;
  RxBool loadingMore = false.obs;
  int per_page = 10;
  RxBool scrollEnd = false.obs;

  Rx<msgData?> results = msgData().obs;
  RxList<msgItem> messages = <msgItem>[].obs;

  RxList<MsgItem> msg = <MsgItem>[].obs;
  RxBool isMsgloading = false.obs;
  Rx<ResultsData?> msgresults = ResultsData().obs;

  ScrollController chcontroller = ScrollController();
  void _scrollListeners() async {
    loadingMore(true);

    if (chcontroller.position.atEdge && chcontroller.position.pixels != 0) {
      await loadMoreMessages();
    }
    loadingMore(false);
  }

  ScrollController controller = ScrollController();
  void _scrollListener() async {
    loadingMore(true);
    if (controller.position.atEdge && controller.position.pixels != 0) {
      await loadRecipients(
        msId: dataController.msgId.value,
      );
    }
    loadingMore(false);
  }

  @override
  void onInit() {
    controller.addListener(_scrollListener);
    chcontroller.addListener(_scrollListeners);
    super.onInit();
  }

  @override
  void dispose() {
    controller.removeListener(_scrollListener);
    chcontroller.removeListener(_scrollListeners);
    chcontroller.dispose();
    super.dispose();
  }

  void reset() {
    per_page = 10;
    scrollEnd = false.obs;
  }

  Future<bool> ConfirmMessages({required MessagesDto message}) async {
    isConfirming(true);
    try {
      var res = await apiProvider.request(
        url: ApiUrls.confirmSmS,
        method: Method.POST,
        params: message.toJson(),
      );
      if (res.data["status"]) {
        resData(res.data["data"]);
        apiMessage(res.data["message"]);
        isConfirming(false);
        return true;
      } else {
        apiMessage(res.data["message"]);
        isConfirming(false);

        return false;
      }
    } catch (e) {
      logger.e(e);
      isConfirming(false);
      apiMessage('An error occurred');
      isConfirming(false);

      return false;
    }
  }

  Future<bool> sendMessages({required MessagesDto message}) async {
    isSending(true);
    try {
      var res = await apiProvider.request(
        url: ApiUrls.sendSmS,
        method: Method.POST,
        params: message.toJson(),
      );
      if (res.data["status"]) {
        sendData(res.data["data"]);
        apiMessage2(res.data["message"]);
        isSending(false);

        return true;
      } else {
        apiMessage2(res.data["message"]);
        isSending(false);

        return false;
      }
    } catch (e) {
      logger.e(e);
      apiMessage('An error occurred');
      isSending(false);
      return false;
    }
  }

  UserModelLatest? getLocalUser() {
    final usr = box.read(CacheKeys.user);
    if (usr != null) {
      user(UserModelLatest.fromJson(usr));
      return user.value;
    } else {
      return null;
    }
  }

  Future<bool> getMessages(
      {required phoneNumber, int? page = 0, int? size = 10}) async {
    try {
      var res = await apiProvider.request(
        url: "${ApiUrls.getMsgs}?phone_number=$phoneNumber&size=$size",
        method: Method.GET,
      );
      if (res.data["status"]) {
        apiMessage(res.data["message"]);
        msgData fetchedResults = msgData.fromJson(res.data["data"]);
        messages([]);
        for (var element in res.data["data"]["items"]) {
          messages.add(msgItem.fromJson(element));
        }

        fetchedResults.items = messages;
        results.value = fetchedResults;
        return true;
      } else {
        return false;
      }
    } catch (e) {
      logger.e(e);
      return false;
    } finally {
      isgetloading(false);
    }
  }

  Future<void> loadMoreMessages() async {
    if (per_page < results.value!.total!) {
      per_page = (per_page + 10 > results.value!.total!)
          ? results.value!.total!
          : per_page + 10;
      await getMessages(
        size: per_page,
        phoneNumber: getLocalUser()?.phoneNumber,
      );
      if (per_page == results.value!.total!) {
        scrollEnd.value = true;
      }
    }
  }

  Future<bool> getMsg(
      {required msgId,
      int? page = 0,
      int? size = 10,
      String? phoneNumber}) async {
    try {
      var res = await apiProvider.request(
        url: "${ApiUrls.getMsg}?transaction_id=$msgId",
        method: Method.GET,
      );
      if (res.data["status"]) {
        apiMessage(res.data["message"]);
        ResultsData fetchedResults = ResultsData.fromJson(res.data["data"]);
        msg([]);
        for (var element in res.data["data"]["items"]) {
          msg.add(MsgItem.fromJson(element));
        }
        fetchedResults.items = msg;
        msgresults.value = fetchedResults;

        return true;
      } else {
        isMsgloading(false);

        return false;
      }
    } catch (e) {
      isMsgloading(false);

      logger.e(e);
      return false;
    }
  }

  Future<void> loadRecipients({required msId}) async {
    if (per_page < msgresults.value!.total!) {
      per_page = (per_page + 10 > msgresults.value!.total!)
          ? msgresults.value!.total!
          : per_page + 10;
      await getMsg(
        size: per_page,
        phoneNumber: user.value.phoneNumber,
        msgId: msId,
      );
      if (per_page == msgresults.value!.total!) {
        scrollEnd.value = true;
      }
    }
  }

  final selectedContacts = <SelectedContact>[].obs;
  addSelectedContact({contact, required context, required String type, group}) {
    if (type == 'contact') {
      // Check if contact already exists in selectedContacts
      bool contactExists = selectedContacts.any((element) => 
      element.type == 'contact' && 
      element.contact?.phones.first.number == contact?.phones.first.number
      );

      if(contactExists) {
        ToastUtils.showToast('Contact already selected');
        return;
      }
      
      if (!contactExists) {
      CrtSmsScreenState().selectContact(contact, context);
      selectedContacts.add(SelectedContact(type: type, group: group, contact: contact));
      }
    } else {
      selectedContacts.add(SelectedContact(type: type, group: group, contact: contact));
    }
  }
  removeSelectedContact({required SelectedContact contact}) {
    selectedContacts.remove(contact);
  }
  void removeAllSelectedContacts() {
    selectedContacts.clear();
  }
}


class SelectedContact {
  final String type;
  final Contact? contact;
   SmsGroups? group;
  SelectedContact({this.group, required this.type, this.contact});
}

class MessageController extends GetxController {
  var reQmessage = RxString('');
  var msgId = RxString('');
  var reSmessage = RxString('');
}
