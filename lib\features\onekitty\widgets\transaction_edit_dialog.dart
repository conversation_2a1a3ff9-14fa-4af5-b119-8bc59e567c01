import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty_admin/models/transaction_edit_models.dart';
import 'package:onekitty_admin/models/transaction_model.dart';
import 'package:onekitty_admin/features/onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty_admin/utils/my_button.dart';
import 'package:onekitty_admin/utils/formatted_currency.dart';
import 'package:onekitty_admin/helpers/colors.dart';

/// Transaction types supported by the edit dialog
enum TransactionType { kitty, chama, event }

/// Reusable dialog widget for editing transaction details
/// Supports all transaction types with conditional fields based on user permissions
class TransactionEditDialog extends StatefulWidget {
  final TransactionModel transaction;
  final TransactionType transactionType;
  final bool isAdmin;
  final Function(TransactionEditFormData) onSave;
  final VoidCallback? onCancel;

  const TransactionEditDialog({
    super.key,
    required this.transaction,
    required this.transactionType,
    required this.isAdmin,
    required this.onSave,
    this.onCancel,
  });

  @override
  State<TransactionEditDialog> createState() => _TransactionEditDialogState();
}

class _TransactionEditDialogState extends State<TransactionEditDialog> {
  final _formKey = GlobalKey<FormState>();

  // Form controllers
  late TextEditingController _fullNameController;
  late TextEditingController _paymentRefController;

  // Focus nodes for proper form navigation
  late FocusNode _fullNameFocus;
  late FocusNode _paymentRefFocus;

  // Form state
  bool _showNames = false;
  bool _isLoading = false;
  Map<String, String> _fieldErrors = {};

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _initializeFocusNodes();
    _prePopulateFields();
  }

  @override
  void dispose() {
    _disposeControllers();
    _disposeFocusNodes();
    super.dispose();
  }

  /// Initialize text editing controllers
  void _initializeControllers() {
    _fullNameController = TextEditingController();
    _paymentRefController = TextEditingController();
  }

  /// Initialize focus nodes for form navigation
  void _initializeFocusNodes() {
    _fullNameFocus = FocusNode();
    _paymentRefFocus = FocusNode();
  }

  /// Pre-populate form fields with current transaction data
  void _prePopulateFields() {
    final firstName = widget.transaction.firstName ?? '';
    final secondName = widget.transaction.secondName ?? '';
    _fullNameController.text = '$firstName $secondName'.trim();
    _paymentRefController.text = widget.transaction.payment_ref ?? '';

    // Pre-populate show names
    _showNames = widget.transaction.showNames ?? false;
  }

  /// Dispose text editing controllers
  void _disposeControllers() {
    _fullNameController.dispose();
    _paymentRefController.dispose();
  }

  /// Dispose focus nodes
  void _disposeFocusNodes() {
    _fullNameFocus.dispose();
    _paymentRefFocus.dispose();
  }

  /// Validate form fields and return validation errors
  Map<String, String> _validateForm() {
    final errors = <String, String>{};

    // Validate full name
    final fullName = _fullNameController.text.trim();
    if (fullName.isEmpty) {
      errors['fullName'] = 'first_name_required'.tr;
    } else if (fullName.length < 2) {
      errors['fullName'] = 'first_name_min_length'.tr;
    } else if (!RegExp(r'^[a-zA-Z\s]+$').hasMatch(fullName)) {
      errors['fullName'] = 'first_name_letters_only'.tr;
    }

    return errors;
  }

  /// Handle form submission
  void _handleSave() async {
    if (_isLoading) return;

    setState(() {
      _fieldErrors = _validateForm();
    });

    if (_fieldErrors.isNotEmpty) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Split full name into first and second names
      final fullName = _fullNameController.text.trim();
      final nameParts = fullName.split(' ');
      final firstName = nameParts.isNotEmpty ? nameParts.first : '';
      final secondName = nameParts.length > 1 ? nameParts.skip(1).join(' ') : '';
      
      final formData = TransactionEditFormData(
        firstName: firstName,
        secondName: secondName,
        paymentRef: _paymentRefController.text.trim().isNotEmpty
            ? _paymentRefController.text.trim()
            : null,
        showNames: _showNames,
      );

      widget.onSave(formData);
    } catch (error) {
      // Error handling is done by the parent widget
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Handle cancel action
  void _handleCancel() {
    if (widget.onCancel != null) {
      widget.onCancel!();
    } else {
      Navigator.of(context).pop();
    }
  }

  /// Get transaction type display name
  String _getTransactionTypeDisplayName() {
    switch (widget.transactionType) {
      case TransactionType.kitty:
        return 'Kitty';
      case TransactionType.chama:
        return 'Chama';
      case TransactionType.event:
        return 'Event';
    }
  }

  /// Check if transaction is 'type in' (contribution)
  bool _isTypeInTransaction() {
    return widget.transaction.typeInOut?.toLowerCase() == 'in';
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Container(
        padding: EdgeInsets.all(24.w),
        constraints: BoxConstraints(
          maxWidth: 400.w,
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            SizedBox(height: 20.h),
            Flexible(
              child: SingleChildScrollView(
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildTransactionInfo(),
                      SizedBox(height: 20.h),
                      _buildFullNameField(),
                      SizedBox(height: 16.h),
                      _buildShowNamesToggle(),
                      SizedBox(height: 16.h),
                      _buildPaymentRefField(),
                    ],
                  ),
                ),
              ),
            ),
            SizedBox(height: 24.h),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  /// Build dialog header
  Widget _buildHeader() {
    return Row(
      children: [
        Icon(
          Icons.edit,
          color: AppColors.primary,
          size: 24.sp,
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: Text(
            'edit_transaction'
                .tr
                .replaceAll('{type}', _getTransactionTypeDisplayName()),
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
        ),
        IconButton(
          onPressed: _handleCancel,
          icon: Icon(
            Icons.close,
            size: 24.sp,
            color: Colors.grey[600],
          ),
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
        ),
      ],
    );
  }

  /// Build transaction information display
  Widget _buildTransactionInfo() {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'transaction_details'.tr,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
              color: Colors.grey[700],
            ),
          ),
          SizedBox(height: 8.h),
          _buildInfoRow(
              'Amount',
              FormattedCurrency.getFormattedCurrency(
                  widget.transaction.amount)),
          _buildInfoRow('Date', widget.transaction.transactionDate ?? 'N/A'),
          _buildInfoRow(
              'Transaction code', widget.transaction.transactionCode ?? 'N/A'),
        ],
      ),
    );
  }

  /// Build information row
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 2.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80.w,
            child: Text(
              '$label:',
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build full name input field
  Widget _buildFullNameField() {
    return CustomTextFieldText(
      controller: _fullNameController,
      focusNode: _fullNameFocus,
      labelText: 'full_name'.tr,
      hintText: 'enter_full_name'.tr,
      isRequired: true,
      validator: (value) => _fieldErrors['fullName'],
      onChanged: (value) {
        if (_fieldErrors.containsKey('fullName')) {
          setState(() {
            _fieldErrors.remove('fullName');
          });
        }
      },
    );
  }

  /// Build payment reference input field (only for type in transactions)
  Widget _buildPaymentRefField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                'membership_reference'.tr,
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
            ),
            SizedBox(width: 4.w),
            Tooltip(
              triggerMode: TooltipTriggerMode.tap,
              message: 'payment_reference_optional_tooltip'.tr,
              child: Icon(
                Icons.info_outline,
                size: 16.sp,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
        SizedBox(height: 8.h),
        CustomTextFieldText(
          controller: _paymentRefController,
          focusNode: _paymentRefFocus,
          labelText: 'membership_reference'.tr,
          hintText: 'enter_employee_id_reference'.tr,
          isRequired: false,
        ),
      ],
    );
  }

  /// Build show names toggle
  Widget _buildShowNamesToggle() {
    return Container( 
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'show_names'.tr,
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  'display_contributor_names'.tr,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Checkbox(
            value: _showNames,
            onChanged: (value) {
              setState(() {
                _showNames = value ?? false;
              });
            },
            activeColor: AppColors.primary,
          ),
        ],
      ),
    );
  }

  /// Build action buttons
  Widget _buildActionButtons() {
    return Column(
      children: [
        // Error message display
        if (_fieldErrors.isNotEmpty) ...[
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(12.w),
            margin: EdgeInsets.only(bottom: 16.h),
            decoration: BoxDecoration(
              color: Colors.red.shade50,
              borderRadius: BorderRadius.circular(8.r),
              border: Border.all(color: Colors.red.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.error_outline,
                      color: Colors.red.shade700,
                      size: 16.sp,
                    ),
                    SizedBox(width: 8.w),
                    Text(
                      'please_fix_following_errors'.tr,
                      style: TextStyle(
                        color: Colors.red.shade700,
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 8.h),
                ..._fieldErrors.entries.map((entry) => Padding(
                      padding: EdgeInsets.only(bottom: 4.h),
                      child: Text(
                        '• ${entry.value}',
                        style: TextStyle(
                          color: Colors.red.shade600,
                          fontSize: 11.sp,
                        ),
                      ),
                    )),
              ],
            ),
          ),
        ],

        // Action buttons
        Row(
          children: [
            Expanded(
              child: MyButton(
                label: 'Cancel',
                outlined: true,
                onClick: _handleCancel,
                fontSize: 14.sp,
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: MyButton(
                label: 'Save',
                onClick: _handleSave,
                showLoading: _isLoading,
                fontSize: 14.sp,
                color: AppColors.primary,
              ),
            ),
          ],
        ),
      ],
    );
  }
}

/// Helper function to show the transaction edit dialog
/// Only shows for kitty transactions (product == 'kitty')
Future<T?> showTransactionEditDialog<T>({
  required BuildContext context,
  required TransactionModel transaction,
  TransactionType? transactionType,
  required bool isAdmin,
  required Function(TransactionEditFormData) onSave,
  VoidCallback? onCancel,
}) {
  // Check if transaction is editable (only kitty transactions)
  if (!transaction.isEditable) {
    // Show info dialog for non-editable transactions
    return showDialog<T>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('edit_not_available'.tr),
          content: Text('transaction_editing_only_kitty'.tr),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('ok'.tr),
            ),
          ],
        );
      },
    );
  }

  return showDialog<T>(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return TransactionEditDialog(
        transaction: transaction,
        transactionType: transactionType ?? TransactionType.kitty,
        isAdmin: isAdmin,
        onSave: onSave,
        onCancel: onCancel,
      );
    },
  );
}

/// Shows a network error dialog with retry option
Future<bool> showNetworkErrorDialog({
  required BuildContext context,
  required String message,
  required VoidCallback onRetry,
}) async {
  return await showDialog<bool>(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            title: Row(
              children: [
                Icon(
                  Icons.wifi_off,
                  color: Colors.orange.shade700,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text('connection_error'.tr),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(message),
                const SizedBox(height: 16),
                Text(
                  'check_internet_connection'.tr,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: Text('cancel'.tr),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop(true);
                  onRetry();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                ),
                child: Text(
                  'retry'.tr,
                  style: const TextStyle(color: Colors.white),
                ),
              ),
            ],
          );
        },
      ) ??
      false;
}

/// Shows a success message dialog
Future<void> showSuccessDialog({
  required BuildContext context,
  required String message,
  String? title,
}) async {
  return showDialog<void>(
    context: context,
    builder: (BuildContext context) {
      return AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        title: Row(
          children: [
            Icon(
              Icons.check_circle,
              color: Colors.green.shade700,
              size: 24,
            ),
            const SizedBox(width: 8),
            Text(title ?? 'success'.tr),
          ],
        ),
        content: Text(message),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green.shade700,
            ),
            child: Text(
              'ok'.tr,
              style: const TextStyle(color: Colors.white),
            ),
          ),
        ],
      );
    },
  );
}
