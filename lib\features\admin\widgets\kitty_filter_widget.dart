import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onekitty_admin/features/admin/controllers/kitty_dashboard_controller.dart';
import 'package:intl/intl.dart';

class KittyFilterWidget extends StatefulWidget {
  final KittyDashboardController controller;

  const KittyFilterWidget({
    super.key,
    required this.controller,
  });

  @override
  State<KittyFilterWidget> createState() => _KittyFilterWidgetState();
}

class _KittyFilterWidgetState extends State<KittyFilterWidget> {
  Timer? _debounce;
  late TextEditingController _kittyIdController;
  late TextEditingController _chamaIdController;
  late TextEditingController _frequencyController;

  @override
  void initState() {
    super.initState();
    _kittyIdController = TextEditingController(text: widget.controller.kittyId.value);
    _chamaIdController = TextEditingController(text: widget.controller.chamaId.value);
    _frequencyController = TextEditingController(text: widget.controller.frequency.value);
  }

  @override
  void dispose() {
    _debounce?.cancel();
    _kittyIdController.dispose();
    _chamaIdController.dispose();
    _frequencyController.dispose();
    super.dispose();
  }

  void _handleTextChange(String value, Function(String) setter) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    
    _debounce = Timer(const Duration(seconds: 1), () {
      setter(value);
      widget.controller.fetchKitties(0);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Filter Kitties',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(Icons.close),
            ),
          ],
        ),
        const SizedBox(height: 24),
        
        // Date Range Filters
        Text(
          'Date Range',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        const SizedBox(height: 12),
        
        Row(
          children: [
            Expanded(
              child: _buildDateField(
                context,
                label: 'Start Date',
                value: widget.controller.startDate.value,
                onTap: () => widget.controller.selectStartDate(context),
                onClear: widget.controller.clearStartDate,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildDateField(
                context,
                label: 'End Date',
                value: widget.controller.endDate.value,
                onTap: () => widget.controller.selectEndDate(context),
                onClear: widget.controller.clearEndDate,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 24),
        
        // Search Filters
        Text(
          'Search Filters',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        const SizedBox(height: 12),
        
        TextField(
          controller: _kittyIdController,
          decoration: const InputDecoration(
            labelText: 'Kitty ID',
            hintText: 'Enter kitty ID',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.tag),
          ),
          onChanged: (value) => _handleTextChange(
            value,
            (val) => widget.controller.kittyId.value = val,
          ),
        ),
        
        const SizedBox(height: 16),
        
        TextField(
          controller: _chamaIdController,
          decoration: const InputDecoration(
            labelText: 'Chama ID',
            hintText: 'Enter chama ID',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.group),
          ),
          onChanged: (value) => _handleTextChange(
            value,
            (val) => widget.controller.chamaId.value = val,
          ),
        ),
        
        const SizedBox(height: 16),
        
        TextField(
          controller: _frequencyController,
          decoration: const InputDecoration(
            labelText: 'Frequency',
            hintText: 'Enter frequency',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.schedule),
          ),
          onChanged: (value) => _handleTextChange(
            value,
            (val) => widget.controller.frequency.value = val,
          ),
        ),
        
        const SizedBox(height: 24),
        
        // Action Buttons
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () {
                  widget.controller.clearFilters();
                  _kittyIdController.clear();
                  _chamaIdController.clear();
                  _frequencyController.clear();
                },
                icon: const Icon(Icons.clear_all),
                label: const Text('Clear All'),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                icon: const Icon(Icons.check),
                label: const Text('Apply'),
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 16),
        
        // Filter Summary
        Obx(() {
          final activeFilters = _getActiveFilters();
          if (activeFilters.isEmpty) {
            return const SizedBox.shrink();
          }
          
          return Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Theme.of(context).primaryColor.withOpacity(0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Active Filters (${activeFilters.length})',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).primaryColor,
                      ),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 4,
                  children: activeFilters.map((filter) => Chip(
                    label: Text(
                      filter,
                      style: const TextStyle(fontSize: 12),
                    ),
                    backgroundColor: Theme.of(context).primaryColor.withOpacity(0.2),
                    deleteIcon: const Icon(Icons.close, size: 16),
                    onDeleted: () => _removeFilter(filter),
                  )).toList(),
                ),
              ],
            ),
          );
        }),
      ],
    );
  }

  Widget _buildDateField(
    BuildContext context, {
    required String label,
    required String value,
    required VoidCallback onTap,
    required VoidCallback onClear,
  }) {
    final displayValue = value.isNotEmpty
        ? DateFormat('dd/MM/yyyy').format(DateTime.parse(value))
        : '';

    return InkWell(
      onTap: onTap,
      child: InputDecorator(
        decoration: InputDecoration(
          labelText: label,
          border: const OutlineInputBorder(),
          suffixIcon: value.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear, size: 18),
                  onPressed: onClear,
                )
              : const Icon(Icons.calendar_today),
        ),
        child: Text(
          displayValue.isEmpty ? 'Select date' : displayValue,
          style: TextStyle(
            color: displayValue.isEmpty
                ? Theme.of(context).hintColor
                : Theme.of(context).textTheme.bodyLarge?.color,
          ),
        ),
      ),
    );
  }

  List<String> _getActiveFilters() {
    final filters = <String>[];
    
    if (widget.controller.startDate.value.isNotEmpty) {
      final date = DateFormat('dd/MM/yyyy').format(
        DateTime.parse(widget.controller.startDate.value),
      );
      filters.add('Start: $date');
    }
    
    if (widget.controller.endDate.value.isNotEmpty) {
      final date = DateFormat('dd/MM/yyyy').format(
        DateTime.parse(widget.controller.endDate.value),
      );
      filters.add('End: $date');
    }
    
    if (widget.controller.kittyId.value.isNotEmpty) {
      filters.add('Kitty ID: ${widget.controller.kittyId.value}');
    }
    
    if (widget.controller.chamaId.value.isNotEmpty) {
      filters.add('Chama ID: ${widget.controller.chamaId.value}');
    }
    
    if (widget.controller.frequency.value.isNotEmpty) {
      filters.add('Frequency: ${widget.controller.frequency.value}');
    }
    
    return filters;
  }

  void _removeFilter(String filter) {
    if (filter.startsWith('Start:')) {
      widget.controller.clearStartDate();
    } else if (filter.startsWith('End:')) {
      widget.controller.clearEndDate();
    } else if (filter.startsWith('Kitty ID:')) {
      widget.controller.kittyId.value = '';
      _kittyIdController.clear();
    } else if (filter.startsWith('Chama ID:')) {
      widget.controller.chamaId.value = '';
      _chamaIdController.clear();
    } else if (filter.startsWith('Frequency:')) {
      widget.controller.frequency.value = '';
      _frequencyController.clear();
    }
    
    widget.controller.fetchKitties(0);
  }
}
