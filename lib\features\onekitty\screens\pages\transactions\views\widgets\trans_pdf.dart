import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty_admin/configs/country_specifics.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/controllers/user_ktty_controller.dart';
import 'package:onekitty_admin/models/transaction_model.dart';
import 'package:onekitty_admin/utils/formatted_currency.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';

/// Generate and save PDF with modern design
Future<void> generateAndSavePDF(TransactionModel transaction) async {
  try {
    // Load custom font to avoid Unicode issues
    pw.Font? customFont;
    try {
      final fontData = await rootBundle.load('assets/fonts/PoppinsRegular.ttf');
      customFont = pw.Font.ttf(fontData);
    } catch (e) {
      customFont = null; // Will use default font
    }

    final pdf = pw.Document(
      title: 'transaction_receipt'.tr,
      author: 'onekitty.co.ke',
      producer: 'onekitty.co.ke',
      subject: 'transaction_receipt'.tr,
    );
    final DateFormat format = DateFormat('dd MMM yyyy, hh:mm a');
    final DateTime createdAt = transaction.createdAt ?? DateTime.now();

    // Safely get DataController with null check
    DataController? dataController;
    try {
      dataController = Get.find<DataController>();
    } catch (e) {
      dataController = null;
    }

    // Load logo with error handling
    Uint8List? logoBytes;
    try {
      final ByteData logoData = await rootBundle.load('assets/images/launcher.png');
      logoBytes = logoData.buffer.asUint8List();
    } catch (e) {
      logoBytes = null;
    }

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.roll80,
        margin: const pw.EdgeInsets.all(8),
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.center,
            mainAxisSize: pw.MainAxisSize.min,
            children: [
              // Header Section with Logo and Company Info
              _buildPDFHeader(logoBytes, customFont),

              pw.SizedBox(height: 12),

              // Receipt Content Card
              pw.Container(
                width: double.infinity,
                decoration: pw.BoxDecoration(
                  color: PdfColors.white,
                  border: pw.Border.all(color: PdfColors.grey400, width: 1.5),
                  borderRadius: pw.BorderRadius.circular(8),
                ),
                child: pw.Column(
                  mainAxisSize: pw.MainAxisSize.min,
                  children: [
                    // Receipt Header
                    _buildReceiptHeader(transaction, format, createdAt, customFont),

                    // Customer Information Section
                    _buildCustomerSection(transaction, customFont),

                    // Transaction Details Section
                    _buildTransactionSection(transaction, dataController, customFont),

                    // Amount Section (Highlighted)
                    _buildAmountSection(transaction, customFont),

                    pw.SizedBox(height: 12),

                    // Footer inside card
                    _buildReceiptFooter(transaction, customFont),
                  ],
                ),
              ),

              pw.SizedBox(height: 12),

              // Company Footer
              _buildCompanyFooter(customFont),
            ],
          );
        },
      ),
    );

    // Save and share PDF
    await _savePDF(pdf, transaction);
  } catch (e) {
    print('Error generating PDF: $e');
    rethrow;
  }
}

/// Build PDF Header with logo and company info
pw.Widget _buildPDFHeader(Uint8List? logoBytes, pw.Font? customFont) {
  return pw.Column(
    children: [
      // Logo with fallback
      pw.Container(
        padding: const pw.EdgeInsets.all(12),
        decoration: pw.BoxDecoration(
          color: PdfColors.grey100,
          borderRadius: pw.BorderRadius.circular(8),
        ),
        child: logoBytes != null && logoBytes.isNotEmpty
          ? pw.Image(
              pw.MemoryImage(logoBytes),
              height: 50,
              width: 100,
              fit: pw.BoxFit.contain,
            )
          : pw.Container(
              height: 50,
              width: 100,
              child: pw.Center(
                child: pw.Text(
                  'OneKitty',
                  style: pw.TextStyle(
                    fontSize: 14,
                    font: customFont,
                    color: PdfColors.grey700,
                  ),
                ),
              ),
            ),
      ),
      
      pw.SizedBox(height: 8),
      
      // Company Name
      pw.Text(
        'OneKitty',
        style: pw.TextStyle(
          fontSize: 24,
          font: customFont,
          color: PdfColors.grey800,
        ),
      ),
      
      pw.SizedBox(height: 6),
      
      // Transaction Receipt Label
      pw.Container(
        padding: const pw.EdgeInsets.symmetric(horizontal: 16, vertical: 6),
        decoration: pw.BoxDecoration(
          color: PdfColors.green100,
          borderRadius: pw.BorderRadius.circular(15),
          border: pw.Border.all(color: PdfColors.green300),
        ),
        child: pw.Text(
          'TRANSACTION RECEIPT',
          style: pw.TextStyle(
            fontSize: 11,
            font: customFont,
            color: PdfColors.green800,
            letterSpacing: 1.0,
          ),
        ),
      ),
    ],
  );
}

/// Build Receipt Header Section
pw.Widget _buildReceiptHeader(TransactionModel transaction, DateFormat format, DateTime createdAt, pw.Font? customFont) {
  return pw.Container(
    width: double.infinity,
    padding: const pw.EdgeInsets.all(8),
    decoration: const pw.BoxDecoration(
      color: PdfColors.green50,
      borderRadius: pw.BorderRadius.only(
        topLeft: pw.Radius.circular(8),
        topRight: pw.Radius.circular(8),
      ),
    ),
    child: pw.Column(
      children: [
        pw.Text(
          'Receipt Details',
          style: pw.TextStyle(
            fontSize: 14,
            font: customFont,
            color: PdfColors.green800,
          ),
        ),
        
        pw.SizedBox(height: 6),
        
        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          children: [
            pw.Expanded(
              child: _buildInfoItem(
                'Receipt No.',
                transaction.transactionCode ?? 'N/A',
              ),
            ),
            pw.SizedBox(width: 20),
            pw.Expanded(
              child: _buildInfoItem(
                'Date & Time',
                format.format(createdAt),
              ),
            ),
          ],
        ),
        
        pw.SizedBox(height: 6),
        
        _buildStatusBadge(transaction.status ?? 'COMPLETED'),
      ],
    ),
  );
}

/// Build Customer Information Section
pw.Widget _buildCustomerSection(TransactionModel transaction, pw.Font? customFont) {
  final hasCustomerName = (transaction.firstName?.isNotEmpty == true) ||
                         (transaction.secondName?.isNotEmpty == true);
  final hasPhoneNumber = transaction.phoneNumber?.isNotEmpty == true;

  if (!hasCustomerName && !hasPhoneNumber) {
    return pw.SizedBox(height: 0);
  }

  return pw.Container(
    width: double.infinity,
    padding: const pw.EdgeInsets.symmetric(horizontal: 20, vertical: 8),
    child: pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Container(
          padding: const pw.EdgeInsets.symmetric(vertical: 6, horizontal: 10),
          decoration: pw.BoxDecoration(
            color: PdfColors.blue100,
            borderRadius: pw.BorderRadius.circular(4),
          ),
          child: pw.Text(
            'Customer Information',
            style: pw.TextStyle(
              fontSize: 14,
              font: customFont,
              color: PdfColors.blue800,
            ),
          ),
        ),

        pw.SizedBox(height: 12),

        if (hasCustomerName)
          _buildDetailRow(
            'Customer Name',
            '${transaction.firstName ?? ''} ${transaction.secondName ?? ''}'.trim(),
            
          ),

        if (hasPhoneNumber)
          _buildDetailRow(
            'Phone Number',
            transaction.phoneNumber!,
            
          ),

        pw.Container(
          margin: const pw.EdgeInsets.only(top: 8),
          height: 1,
          color: PdfColors.grey300,
        ),
      ],
    ),
  );
}

/// Build Transaction Details Section
pw.Widget _buildTransactionSection(TransactionModel transaction, DataController? dataController, pw.Font? customFont) {
  return pw.Container(
    width: double.infinity,
    padding: const pw.EdgeInsets.symmetric(horizontal: 20, vertical: 8),
    child: pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Container(
          padding: const pw.EdgeInsets.symmetric(vertical: 6, horizontal: 10),
          decoration: pw.BoxDecoration(
            color: PdfColors.orange100,
            borderRadius: pw.BorderRadius.circular(4),
          ),
          child: pw.Text(
            'Transaction Details',
            style: pw.TextStyle(
              fontSize: 14,
              font: customFont,
              color: PdfColors.orange800,
            ),
          ),
        ),

        pw.SizedBox(height: 6),

        // Kitty group
        if (dataController?.kitty.value.kitty?.title?.isNotEmpty == true)
          _buildDetailRow(
            'Kitty Group',
            dataController!.kitty.value.kitty!.title!,
          ),

        // Transaction type
        if (transaction.product?.isNotEmpty == true)
          _buildDetailRow(
            'Transaction Type',
            transaction.product!.toUpperCase(),
          ),

        // Payment method
        _buildDetailRow(
          'Payment Method',
          transaction.channelName ?? 'Mobile Money',
        ),

        // Transaction reference if available
        if (transaction.transactionRef?.isNotEmpty == true)
          _buildDetailRow(
            'Reference',
            transaction.transactionRef!,
          ),

        pw.Container(
          margin: const pw.EdgeInsets.only(top: 12),
          height: 1,
          color: PdfColors.grey300,
        ),
      ],
    ),
  );
}

/// Build Amount Section (Highlighted)
pw.Widget _buildAmountSection(TransactionModel transaction, pw.Font? customFont) {
  final amount = transaction.amount ?? 0;
  final formattedAmount = _safeFormatCurrency(amount);

  return pw.Container(
    width: double.infinity,
    margin: const pw.EdgeInsets.all(8),
    padding: const pw.EdgeInsets.all(20),
    decoration: pw.BoxDecoration(
      color: PdfColors.green50,
      borderRadius: pw.BorderRadius.circular(8),
      border: pw.Border.all(color: PdfColors.green300, width: 2),
    ),
    child: pw.Column(
      children: [
        pw.Text(
          'TOTAL AMOUNT',
          style: pw.TextStyle(
            fontSize: 12,
            font: customFont,
            color: PdfColors.green700,
            letterSpacing: 1.0,
          ),
        ),

        pw.SizedBox(height: 6),

        pw.Text(
          '${CountryConfig.getCurrencyCode} $formattedAmount',
          style: pw.TextStyle(
            fontSize: 16,
            font: customFont,
            color: PdfColors.green800,
          ),
        ),
      ],
    ),
  );
}

/// Safely format currency with null check
String _safeFormatCurrency(dynamic amount) {
  try {
    if (amount == null) return '0.00';
    
    // Try using your FormattedCurrency utility first
    return FormattedCurrency.getFormattedCurrency(amount);
  } catch (e) {
    // Fallback formatting
    final numAmount = double.tryParse(amount.toString()) ?? 0.0;
    final formatter = NumberFormat('#,##0.00');
    return formatter.format(numAmount);
  }
}

/// Build Receipt Footer
pw.Widget _buildReceiptFooter(TransactionModel transaction, pw.Font? customFont) {
  final transactionCode = transaction.transactionCode?.isNotEmpty == true
      ? transaction.transactionCode!
      : 'OK${DateTime.now().millisecondsSinceEpoch}';

  return pw.Container(
    width: double.infinity,
    padding: const pw.EdgeInsets.all(20),
    decoration: const pw.BoxDecoration(
      color: PdfColors.grey50,
      borderRadius: pw.BorderRadius.only(
        bottomLeft: pw.Radius.circular(8),
        bottomRight: pw.Radius.circular(8),
      ),
    ),
    child: pw.Column(
      children: [
        // Barcode section
        pw.Container(
          height: 50,
          width: double.infinity,
          child: _buildSafeBarcode(transactionCode, customFont),
        ),

        pw.SizedBox(height: 12),

        pw.Text(
          'Transaction Code: $transactionCode',
          style: pw.TextStyle(
            fontSize: 10,
            font: customFont,
            color: PdfColors.grey700,
          ),
        ),

        pw.SizedBox(height: 6),

        pw.Text(
          'Keep this receipt for your records',
          style: pw.TextStyle(
            fontSize: 9,
            font: customFont,
            color: PdfColors.grey600,
          ),
        ),
      ],
    ),
  );
}

/// Build barcode with error handling
pw.Widget _buildSafeBarcode(String data, pw.Font? customFont) {
  try {
    return pw.BarcodeWidget(
      barcode: pw.Barcode.code128(),
      data: data,
      height: 50,
      drawText: false,
    );
  } catch (e) {
    // Simple barcode-like pattern as fallback
    return pw.Container(
       );
  }
}

/// Build Company Footer
pw.Widget _buildCompanyFooter(pw.Font? customFont) {
  return pw.Column(
    children: [
      pw.Text(
        'Thank you for choosing OneKitty!',
        style: pw.TextStyle(
          fontSize: 16,
          font: customFont,
          color: PdfColors.grey700,
        ),
      ),

      pw.SizedBox(height: 4),

      pw.Text(
        'Your trusted financial companion',
        style: pw.TextStyle(
          fontSize: 10,
          font: customFont,
          color: PdfColors.grey500,
        ),
      ),
      
      pw.SizedBox(height: 8),
      
      pw.Container(
        padding: const pw.EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        decoration: pw.BoxDecoration(
          color: PdfColors.grey100,
          borderRadius: pw.BorderRadius.circular(12),
        ),
        child: pw.Text(
          'www.onekitty.co.ke',
          style: pw.TextStyle(
            fontSize: 10,
            font: customFont,
            color: PdfColors.grey700,
          ),
        ),
      ),
    ],
  );
}

/// Build info item for header
pw.Widget _buildInfoItem(String label, String value) {
  return pw.Column(
    crossAxisAlignment: pw.CrossAxisAlignment.center,
    children: [
      pw.Text(
        label,
        style: pw.TextStyle(
          fontSize: 9,
          color: PdfColors.green700,
          fontWeight: pw.FontWeight.bold,
        ),
      ),
      pw.SizedBox(height: 3),
      pw.Text(
        value,
        style: pw.TextStyle(
          fontSize: 10,
          fontWeight: pw.FontWeight.bold,
          color: PdfColors.grey800,
        ),
        textAlign: pw.TextAlign.center,
      ),
    ],
  );
}

/// Build status badge
pw.Widget _buildStatusBadge(String status) {
  final isSuccess = status.toUpperCase() == 'COMPLETED' || 
                   status.toUpperCase() == 'SUCCESS' ||
                   status.toUpperCase() == 'PAID';
  
  return pw.Container(
    padding: const pw.EdgeInsets.symmetric(horizontal: 12, vertical: 4),
    decoration: pw.BoxDecoration(
      color: isSuccess ? PdfColors.green200 : PdfColors.orange200,
      borderRadius: pw.BorderRadius.circular(12),
    ),
    child: pw.Text(
      status.toUpperCase(),
      style: pw.TextStyle(
        fontSize: 9,
        fontWeight: pw.FontWeight.bold,
        color: isSuccess ? PdfColors.green800 : PdfColors.orange800,
        letterSpacing: 0.3,
      ),
    ),
  );
}

/// Build detail row
pw.Widget _buildDetailRow(String label, String value) {
  return pw.Padding(
    padding: const pw.EdgeInsets.symmetric(vertical: 4),
    child: pw.Row(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.SizedBox(
          width: 110,
          child: pw.Text(
            '$label:',
            style: pw.TextStyle(
              fontSize: 10,
              color: PdfColors.grey600,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
        ),
        pw.Expanded(
          child: pw.Text(
            value,
            style: pw.TextStyle(
              fontSize: 10,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.grey800,
            ),
          ),
        ),
      ],
    ),
  );
}

/// Save PDF file
Future<void> _savePDF(pw.Document pdf, TransactionModel transaction) async {
  try {
    final output = await getApplicationDocumentsDirectory();
    final fileName = 'receipt_${transaction.transactionCode ?? DateTime.now().millisecondsSinceEpoch}.pdf';
    final file = File('${output.path}/$fileName');
    
    final pdfBytes = await pdf.save();
    await file.writeAsBytes(pdfBytes);

    // Share the PDF file
    await Printing.sharePdf(
      bytes: pdfBytes,
      filename: fileName,
    );
  } catch (e) {
    print('Error saving PDF: $e');
    rethrow;
  }
}