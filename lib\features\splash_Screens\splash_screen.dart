import 'package:easy_splash_screen/easy_splash_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:get/get.dart';
import 'package:onekitty_admin/features/login/screens/passwd_req_screen.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/controllers/controllers.dart';
import 'package:onekitty_admin/features/splash_Screens/ginfo_splash_screen.dart';
import 'package:onekitty_admin/services/auth_manager.dart';
import 'package:onekitty_admin/utils/asset_urls.dart';
import 'package:onekitty_admin/utils/themes_colors.dart';
import 'package:flutter/foundation.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  final _authenticationManager = Get.put(AuthenticationManager());
  // final _deepLinkManager = DeepLinkManager();
  bool isloggedin = false;

  @override
  void initState() {
    super.initState();
    
    // Remove native splash screen when Flutter UI is ready
    WidgetsBinding.instance.addPostFrameCallback((_) {
      FlutterNativeSplash.remove();
    });

    _handleNormalNavigation();
    Get.put(GlobalControllers()).getEnums();
  }

  void _handleNormalNavigation() async {
    // Wait for the login status check to complete
    await _authenticationManager.checkLoginStatus();

    // Use the latest value after the check is complete
    // if (mounted && !_deepLinkManager.isDeepLinkInProgress) {
    if (mounted ) {

      // Delay for splash screen effect
      await Future.delayed(const Duration(seconds: 2));

      if (_authenticationManager.isLogged.value) {
        if (kDebugMode) {
          print("User is logged in, navigating to AuthPasswdScreen");
        }
        Get.off(() => AuthPasswdScreen());
      } else {
        if (kDebugMode) {
          print("User is not logged in, navigating to GeneralInfoSplashScreen");
        }
        Get.off(() => const GeneralInfoSplashScreen());
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(392.72727272727275, 850.9090909090909),
      builder: (context, child) {
        return EasySplashScreen(
          durationInSeconds: 2,
          logo: Image.asset(
            AssetUrl.logo3,
            color: Colors.white,
          ),
          backgroundColor: ColorUtil.blueColor,
          showLoader: true,
          loaderColor: Colors.white,
        );
      },
    );
  }
}
