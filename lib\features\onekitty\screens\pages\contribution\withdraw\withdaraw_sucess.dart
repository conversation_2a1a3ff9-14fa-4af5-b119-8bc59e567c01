import 'package:confetti/confetti.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:onekitty_admin/features/onekitty/controllers/contribute_controller.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/controllers/kitty_controller.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/controllers/user_ktty_controller.dart';
import 'package:onekitty_admin/helpers/colors.dart';
import 'package:onekitty_admin/helpers/extensions/text_styles.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/views/screens/signatory_transactions.dart';

import 'package:onekitty_admin/features/onekitty/screens/pages/transactions/models/transaction_type.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/transactions/views/screens/transaction_page.dart';
import 'package:onekitty_admin/utils/asset_urls.dart';
import 'package:onekitty_admin/utils/confetti/confentti_widget.dart';
import 'package:onekitty_admin/utils/formatted_currency.dart';

// ignore: must_be_immutable
class WithdrawSuccess extends StatefulWidget {
  const WithdrawSuccess({super.key});

  @override
  State<WithdrawSuccess> createState() => _WithdrawSuccessState();
}

class _WithdrawSuccessState extends State<WithdrawSuccess> {
  final ContributeController contributeController = Get.find();
  final DataController dataController = Get.put(DataController());

  KittyController controller = Get.put(KittyController());
  late ConfettiController _controllerCenter;
  final box = GetStorage();

  @override
  void initState() {
    super.initState();
    _controllerCenter =
        ConfettiController(duration: const Duration(seconds: 8));
    _controllerCenter.play();
  }

  @override
  void dispose() {
    _controllerCenter.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        margin: const EdgeInsets.symmetric(horizontal: 30, vertical: 20),
        child: Column(
          children: [
            Row(
              children: [
                IconButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    icon: const Icon(Icons.arrow_back)),
                Text("back".tr)
              ],
            ),
            const SizedBox(
              height: 20,
            ),
            Text(
              dataController.kitty.value.kitty?.title ?? "",
              textAlign: TextAlign.center,
              style: context.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(
              height: 30,
            ),
            ConfettiCustom(controllerCenter: _controllerCenter),
            SvgPicture.asset(AssetUrl.tickCircle),
            const SizedBox(
              height: 30,
            ),
            Text(
              "withdrawal_successful".tr,
              style: context.titleLarge
                  ?.copyWith(fontWeight: FontWeight.bold, fontSize: 20),
            ),
            const SizedBox(
              height: 15,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20.0),
              child: Obx(() => Text(
                    controller.withdrawData["has_signatory_transaction"] == true
                        ? "withdrawal_request_submitted_successfully".tr
                        : "funds_transferred_message".tr,
                    textAlign: TextAlign.center,
                    style: context.titleSmall?.copyWith(
                        color: AppColors.greyTextColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 15),
                  )),
            ),
            const SizedBox(
              height: 10,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SvgPicture.asset(AssetUrl.money),
                Text(
                  "${"amount_withdrawn".tr} ",
                  style: context.titleSmall?.copyWith(
                      color: AppColors.greyTextColor,
                      fontWeight: FontWeight.bold,
                      fontSize: 15),
                ),
                Text(
                    controller.withdrawData["amount"] ??
                        controller.withdrawData["response_description"] ??
                        FormattedCurrency.getFormattedCurrency(
                            box.read("amountbox")),
                    style: context.dividerTextSmall?.copyWith(fontSize: 15)),
              ],
            ),
            const SizedBox(
              height: 30,
            ),
            Obx(() => controller.withdrawData["has_signatory_transaction"] ==
                    true
                ? Column(
                    children: [
                      OutlinedButton(
                          onPressed: () {
                            Get.to(() => SignatoryTransactions(
                                kittyId:
                                    contributeController.kittGoten.value.iD ??
                                        0));
                          },
                          child:  Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12.0, vertical: 12),
                            child: Text(
                              "view_signatories".tr,
                              style: const TextStyle(fontWeight: FontWeight.bold),
                            ),
                          )),
                      const SizedBox(height: 10),
                      OutlinedButton(
                          onPressed: () {
                            Get.to(() => TransactionPage(
                                  kittyId:
                                      contributeController.kittGoten.value.iD ??
                                          0,
                                  config: TransactionPageConfig(
                                    transactionType: TransactionType.kitty,
                                    title: 'Kitty Transactions',
                                    entityId:  contributeController
                                            .kittGoten.value.iD ??
                                        0,
                                    showExportOptions: true,
                                    kittyId: contributeController
                                            .kittGoten.value.iD ??
                                        0,
                                    showEditOptions: true,
                                  ),
                                ));
                          },
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12.0, vertical: 12),
                            child: Text(
                              "view_transaction_details".tr,
                              style: const TextStyle(fontWeight: FontWeight.bold),
                            ),
                          )),
                    ],
                  )
                :    OutlinedButton(
                          onPressed: () {
                            Get.to(() => TransactionPage(
                                  kittyId:
                                      contributeController.kittGoten.value.iD ??
                                          0,
                                  config: TransactionPageConfig(
                                    transactionType: TransactionType.kitty,
                                    title: 'Kitty Transactions',
                                    entityId:  contributeController
                                            .kittGoten.value.iD ??
                                        0,
                                    showExportOptions: true,
                                    kittyId: contributeController
                                            .kittGoten.value.iD ??
                                        0,
                                    showEditOptions: true,
                                  ),
                                ));
                          },
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12.0, vertical: 12),
                            child: Text(
                              "view_transaction_details".tr,
                              style: const TextStyle(fontWeight: FontWeight.bold),
                            ),
                          )))
          ],
        ),
      ),
    );
  }
}
