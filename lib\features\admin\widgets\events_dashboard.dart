import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onekitty_admin/features/admin/controllers/events_dashboard_controller.dart';
import 'package:onekitty_admin/features/admin/core/my_pluto_grid/my_pluto_grid.dart';
import 'package:onekitty_admin/features/admin/core/widgets/kitty_loading_widget.dart';
import 'package:onekitty_admin/features/admin/widgets/events_filter_widget.dart';

class EventsDashboard extends StatefulWidget {
  const EventsDashboard({super.key});

  @override
  State<EventsDashboard> createState() => EventsDashboardState();
}

class EventsDashboardState extends State<EventsDashboard> {
  late final EventsDashboardController controller;

  @override
  void initState() {
    super.initState();
    controller = Get.put(EventsDashboardController());
  }

  @override
  void dispose() {
    Get.delete<EventsDashboardController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() {
        if (controller.isLoading.value && controller.events.isEmpty) {
          return const KittyLoadingWidget(
            message: 'Loading events...',
            size: 70.0,
          );
        }

        if (controller.hasError.value && controller.events.isEmpty) {
          return _buildErrorState(context);
        }

        return _buildTable(context);
      }),
    );
  }



  Widget _buildTable(BuildContext context) {
    return Obx(() => AppTable(
          title: 'Events Dashboard',
          actions: [
            ActionButton(
              icon: Icons.filter_list,
              label: 'Filters',
              onTap: () => _showFilterDialog(context),
            ),               
          
          ],
          columns: controller.plutoColumns,
          rows: controller.plutoRows,
          totalPages: controller.totalPages.value,
          totalSizePerPage: controller.pageSize.value,
          onPageNavigated: controller.handlePageChange,
          onItemClicked: controller.onEventTapped,
          onRefresh: controller.refreshData,
          onSearch: controller.handleSearch,
          cacheKey: 'events_dashboard',
          enableSorting: true,
          enableExport: true,
          enableMultiSelect: false,
          emptyStateWidget: _buildEmptyState(context),
          loadingWidget: const KittyLoadingWidget(
            message: 'Loading events...',
            size: 60.0,
          ),
          isExternalLoading: controller.isLoading.value && controller.events.isNotEmpty,
        ));
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.event_busy_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No events found',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.grey[600],
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try adjusting your search or filters',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[500],
                ),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: controller.clearFilters,
            icon: const Icon(Icons.clear_all),
            label: const Text('Clear Filters'),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Failed to load events',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.red[600],
                ),
          ),
          const SizedBox(height: 8),
          Obx(() => Text(
                controller.apiMessage.value,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.red[500],
                    ),
                textAlign: TextAlign.center,
              )),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: controller.refreshData,
            icon: const Icon(Icons.refresh),
            label: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: 400,
          padding: const EdgeInsets.all(24),
          child: EventsFilterWidget(controller: controller),
        ),
      ),
    );
  }

  void _showExportDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.file_download),
              title: const Text('Export as CSV'),
              onTap: () {
                Navigator.pop(context);
                controller.exportData('csv');
              },
            ),
            ListTile(
              leading: const Icon(Icons.file_download),
              title: const Text('Export as Excel'),
              onTap: () {
                Navigator.pop(context);
                controller.exportData('excel');
              },
            ),
            ListTile(
              leading: const Icon(Icons.file_download),
              title: const Text('Export as PDF'),
              onTap: () {
                Navigator.pop(context);
                controller.exportData('pdf');
              },
            ),
          ],
        ),
      ),
    );
  }

  void showAdminActionsDialog(BuildContext context) {
    Get.isRegistered<EventsDashboardController>() ? Get.find<EventsDashboardController>() : Get.put(EventsDashboardController());
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.block),
              title: const Text('Block Selected Event'),
              onTap: () {
                Navigator.pop(context);
                _showBlockEventDialog(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.check_circle),
              title: const Text('Unblock Selected Event'),
              onTap: () {
                Navigator.pop(context);
                _showUnblockEventDialog(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.receipt),
              title: const Text('View Transactions'),
              onTap: () {
                Navigator.pop(context);
                Get.snackbar(
                  'Coming Soon',
                  'Transaction view feature will be available soon',
                  snackPosition: SnackPosition.bottom,
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showBlockEventDialog(BuildContext context) {
    final reasonController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Block Event'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Please provide a reason for blocking this event:'),
            const SizedBox(height: 16),
            TextField(
              controller: reasonController,
              decoration: const InputDecoration(
                labelText: 'Reason',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (reasonController.text.isNotEmpty) {
                // For demo purposes, we'll block the first event
                if (controller.events.isNotEmpty) {
                  controller.blockEvent(
                    controller.events.first.id ?? 0,
                    reasonController.text,
                  );
                }
                Navigator.of(context).pop();
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Block Event'),
          ),
        ],
      ),
    );
  }

  void _showUnblockEventDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Unblock Event'),
        content: const Text('Are you sure you want to unblock this event?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              // For demo purposes, we'll unblock the first event
              if (controller.events.isNotEmpty) {
                controller.unblockEvent(controller.events.first.id ?? 0);
              }
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: const Text('Unblock Event'),
          ),
        ],
      ),
    );
  }
}
