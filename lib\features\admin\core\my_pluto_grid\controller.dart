// ==================== app_table_controller.dart ====================
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:pluto_grid/pluto_grid.dart';
import 'dart:async';

class AppTableController extends GetxController {
  final String? cacheKey;
  final Function(int page)? onPageNavigated;
  final Function(String searchWord)? onSearch;
  final Function()? onRefresh;
  final Function(List<PlutoRow> selectedRows)? onSelectionChanged;

  AppTableController({
    this.cacheKey,
    this.onPageNavigated,
    this.onSearch,
    this.onRefresh,
    this.onSelectionChanged,
  });

  final storage = GetStorage();
  late PlutoGridStateManager stateManager;

  // Reactive variables
  final RxInt currentPage = 1.obs;
  final RxBool isLoading = false.obs;
  final RxString searchQuery = ''.obs;
  final RxList<PlutoRow> selectedRows = <PlutoRow>[].obs;
  final RxBool showHeaderFooter = true.obs;
  final RxBool showFilterDrawer = false.obs;
  final ScrollController scrollController = ScrollController();

  Timer? _debounce;
  double _lastScrollPosition = 0;

  @override
  void onInit() {
    super.onInit();
    restoreState();
    _setupScrollListener();
  }

  void initStateManager(PlutoGridStateManager manager) {
    stateManager = manager;
    stateManager.addListener(_onGridSelectionChanged);
  }

  void _setupScrollListener() {
    scrollController.addListener(() {
      final currentScroll = scrollController.position.pixels;
      final delta = currentScroll - _lastScrollPosition;

      if (delta > 5 && showHeaderFooter.value) {
        showHeaderFooter.value = false;
      } else if (delta < -5 && !showHeaderFooter.value) {
        showHeaderFooter.value = true;
      }

      _lastScrollPosition = currentScroll;
    });
  }

  void _onGridSelectionChanged() {
    selectedRows.value = stateManager.checkedRows;
    onSelectionChanged?.call(selectedRows);
  }

  void handleSearch(String value) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    
    _debounce = Timer(const Duration(milliseconds: 300), () {
      searchQuery.value = value;
      onSearch?.call(value);
      cacheState();
    });
  }

  Future<void> refreshTable() async {
    isLoading.value = true;
    await onRefresh?.call();
    isLoading.value = false;
  }

  void goToPage(int page) {
    currentPage.value = page;
    onPageNavigated?.call(page);
    cacheState();
  }

  void toggleFilterDrawer() {
    showFilterDrawer.value = !showFilterDrawer.value;
  }

  void cacheState() {
    if (cacheKey == null) return;
    
    storage.write('${cacheKey}_page', currentPage.value);
    storage.write('${cacheKey}_search', searchQuery.value);
    storage.write('${cacheKey}_scroll', scrollController.hasClients 
        ? scrollController.position.pixels 
        : 0.0);
  }

  void restoreState() {
    if (cacheKey == null) return;

    currentPage.value = storage.read('${cacheKey}_page') ?? 1;
    searchQuery.value = storage.read('${cacheKey}_search') ?? '';
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final scrollPos = storage.read('${cacheKey}_scroll') ?? 0.0;
      if (scrollController.hasClients && scrollPos > 0) {
        scrollController.jumpTo(scrollPos);
      }
    });
  }

  void exportData(String format) {
    // Implement export logic based on format (CSV, Excel, PDF)
    Get.snackbar('Export', 'Exporting as $format...');
  }

  @override
  void onClose() {
    _debounce?.cancel();
    scrollController.dispose();
    super.onClose();
  }
}
