import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onekitty_admin/models/events/event_statistics_model.dart';
import 'package:onekitty_admin/services/api_urls.dart';
import 'package:onekitty_admin/services/http_service.dart';

/// Controller for managing event statistics and dashboard data
class EventStatisticsController extends GetxController {
  final HttpService apiProvider = Get.find();
  final logger = Get.find<Logger>();

  // Loading states
  final RxBool isLoadingStatistics = false.obs;

  // Statistics data using new models
  final Rx<EventStatisticsResponse?> statisticsResponse = Rx<EventStatisticsResponse?>(null);
  final Rx<Overview?> overview = Rx<Overview?>(null);
  final Rx<RevenueBreakdown?> revenueBreakdown = Rx<RevenueBreakdown?>(null);
  final Rx<UserDemographics?> userDemographics = Rx<UserDemographics?>(null);
  final Rx<StatisticsFilters?> filters = Rx<StatisticsFilters?>(null);

  // Error handling
  final RxString errorMessage = ''.obs;
  final RxBool hasError = false.obs;
  
  // Individual section error tracking
  final RxBool overviewError = false.obs;
  final RxBool revenueError = false.obs;
  final RxBool userDemographicsError = false.obs;
  final RxString overviewErrorMessage = ''.obs;
  final RxString revenueErrorMessage = ''.obs;
  final RxString userDemographicsErrorMessage = ''.obs;

  /// Fetch comprehensive event statistics
  Future<bool> fetchEventStatistics({int? eventId}) async {
    try {
      isLoadingStatistics(true);
      hasError(false);
      errorMessage('');
      _clearSectionErrors();

      String url = ApiUrls.EVENT_STATISTICS;
      if (eventId != null) {
        url += "?event_id=$eventId";
      }

      final response = await apiProvider.request(
        url: url,
        method: Method.GET,
      );

      // Parse the response using the new model
      final statisticsResponseData = EventStatisticsResponse.fromJson(response.data);
      statisticsResponse.value = statisticsResponseData;

      if (statisticsResponseData.status && statisticsResponseData.data != null) {
        final data = statisticsResponseData.data!;
        
        // Parse individual sections with error handling
        _parseOverviewSection(data.statistics);
        _parseRevenueSection(data.statistics);
        _parseUserDemographicsSection(data.statistics);
        
        // Parse filters safely
        try {
          filters.value = data.filters;
        } catch (e) {
          logger.w('Error parsing filters: $e');
          filters.value = null;
        }

        // Return true if at least one section was parsed successfully
        return hasOverviewData || hasRevenueData || hasUserDemographicsData;
      } else {
        hasError(true);
        errorMessage(statisticsResponseData.message.isNotEmpty
            ? statisticsResponseData.message
            : 'Failed to fetch statistics');
        return false;
      }
    } catch (e) {
      logger.e('Error fetching event statistics: $e');
      hasError(true);
      errorMessage('An error occurred while fetching statistics');
      return false;
    } finally {
      isLoadingStatistics(false);
    }
  }

  /// Clear all section-specific errors
  void _clearSectionErrors() {
    overviewError.value = false;
    revenueError.value = false;
    userDemographicsError.value = false;
    overviewErrorMessage.value = '';
    revenueErrorMessage.value = '';
    userDemographicsErrorMessage.value = '';
  }

  /// Parse overview section with error handling
  void _parseOverviewSection(Statistics? statistics) {
    try {
      if (statistics?.overview != null) {
        overview.value = statistics!.overview;
        overviewError.value = false;
        overviewErrorMessage.value = '';
      } else {
        overview.value = null;
        overviewError.value = false; // No error, just no data
        overviewErrorMessage.value = '';
      }
    } catch (e) {
      logger.w('Error parsing overview section: $e');
      overview.value = null;
      overviewError.value = true;
      overviewErrorMessage.value = 'Failed to load overview data';
    }
  }

  /// Parse revenue section with error handling
  void _parseRevenueSection(Statistics? statistics) {
    try {
      if (statistics?.revenueBreakdown != null) {
        revenueBreakdown.value = statistics!.revenueBreakdown;
        revenueError.value = false;
        revenueErrorMessage.value = '';
      } else {
        revenueBreakdown.value = null;
        revenueError.value = false; // No error, just no data
        revenueErrorMessage.value = '';
      }
    } catch (e) {
      logger.w('Error parsing revenue section: $e');
      revenueBreakdown.value = null;
      revenueError.value = true;
      revenueErrorMessage.value = 'Failed to load revenue data';
    }
  }

  /// Parse user demographics section with error handling
  void _parseUserDemographicsSection(Statistics? statistics) {
    try {
      if (statistics?.userDemographics != null) {
        userDemographics.value = statistics!.userDemographics;
        userDemographicsError.value = false;
        userDemographicsErrorMessage.value = '';
      } else {
        userDemographics.value = null;
        userDemographicsError.value = false; // No error, just no data
        userDemographicsErrorMessage.value = '';
      }
    } catch (e) {
      logger.w('Error parsing user demographics section: $e');
      userDemographics.value = null;
      userDemographicsError.value = true;
      userDemographicsErrorMessage.value = 'Failed to load user demographics data';
    }
  }

  // Getter methods for accessing data from the new models
  int get totalTicketsSold => overview.value?.totalTicketsSold ?? 0;
  double get totalRevenue => overview.value?.totalRevenue ?? 0.0;
  int get totalAttendees => overview.value?.totalAttendees ?? 0;
  double get averageTicketPrice => overview.value?.averageTicketPrice ?? 0.0;
  int get totalEvents => overview.value?.totalEvents ?? 0;

  double get averageDailyRevenue => revenueBreakdown.value?.averageDailyRevenue ?? 0.0;
  int get totalTransactions => revenueBreakdown.value?.totalTransactions ?? 0;

  int get uniqueCustomers => userDemographics.value?.uniqueCustomers ?? 0;
  int get repeatCustomers => userDemographics.value?.repeatCustomers ?? 0;
  double get repeatCustomerRate => userDemographics.value?.repeatCustomerRate ?? 0.0;
  double get averageTicketsPerUser => userDemographics.value?.averageTicketsPerUser ?? 0.0;

  bool get hasOverviewData => overview.value?.hasData ?? false;
  bool get hasRevenueData => revenueBreakdown.value?.hasData ?? false;
  bool get hasUserDemographicsData => userDemographics.value?.hasData ?? false;

  // Section visibility getters (show section if it has data and no parsing error)
  bool get shouldShowOverviewSection => hasOverviewData && !overviewError.value;
  bool get shouldShowRevenueSection => hasRevenueData && !revenueError.value;
  bool get shouldShowUserDemographicsSection => hasUserDemographicsData && !userDemographicsError.value;

  // Check if any section has data to show
  bool get hasAnyDataToShow => shouldShowOverviewSection || shouldShowRevenueSection || shouldShowUserDemographicsSection;

  /// Get basic performance metrics from available data
  Map<String, dynamic> getPerformanceMetrics() {
    return {
      'total_events': totalEvents,
      'total_tickets_sold': totalTicketsSold,
      'total_revenue': totalRevenue,
      'total_attendees': totalAttendees,
      'average_ticket_price': averageTicketPrice,
      'average_daily_revenue': averageDailyRevenue,
      'total_transactions': totalTransactions,
      'unique_customers': uniqueCustomers,
      'repeat_customers': repeatCustomers,
      'repeat_customer_rate': repeatCustomerRate,
      'average_tickets_per_user': averageTicketsPerUser,
    };
  }

  /// Get summary data for dashboard cards
  Map<String, dynamic> getSummaryData() {
    return {
      'overview': {
        'total_events': totalEvents,
        'total_tickets_sold': totalTicketsSold,
        'total_revenue': totalRevenue,
        'total_attendees': totalAttendees,
        'average_ticket_price': averageTicketPrice,
      },
      'revenue': {
        'total_revenue': totalRevenue,
        'average_daily_revenue': averageDailyRevenue,
        'total_transactions': totalTransactions,
      },
      'demographics': {
        'unique_customers': uniqueCustomers,
        'repeat_customers': repeatCustomers,
        'repeat_customer_rate': repeatCustomerRate,
        'average_tickets_per_user': averageTicketsPerUser,
      }
    };
  }

  /// Refresh all statistics data
  Future<void> refreshStatistics({int? eventId}) async {
    await fetchEventStatistics(eventId: eventId);
  }

  /// Clear all data
  void clearData() {
    statisticsResponse.value = null;
    overview.value = null;
    revenueBreakdown.value = null;
    userDemographics.value = null;
    filters.value = null;

    hasError.value = false;
    errorMessage.value = '';
  }
}
