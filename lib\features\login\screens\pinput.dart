import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onekitty_admin/features/login/controllers/auth_controller.dart';
import 'package:onekitty_admin/helpers/colors.dart';
import 'package:onekitty_admin/features/login/screens/login_screen.dart';
import 'package:onekitty_admin/utils/custom_button.dart';
import 'package:onekitty_admin/helpers/show_snack_bar.dart';
import 'package:onekitty_admin/features/login/screens/pin_confirmation.dart';
import 'package:onekitty_admin/features/onekitty/screens/widgets/richtext.dart';
import 'package:onekitty_admin/services/auth_manager.dart';
import 'package:otp_autofill/otp_autofill.dart';
import 'package:pinput/pinput.dart';

class PinPutPage extends StatefulWidget {
  final String? phoneNumber;
  final bool? isForgot;
  const PinPutPage({super.key, this.phoneNumber, this.isForgot});

  @override
  State<PinPutPage> createState() => _PinPutPageState();
}

class _PinPutPageState extends State<PinPutPage> {
  late OTPTextEditController pinController;
  final authcontroller = Get.put(AuthenticationController());
  final authManager = Get.find<AuthenticationManager>();
  final logger = Get.find<Logger>();
  final values = Get.arguments;
  final AuthenticationController authenticationController = Get.find();

  late OTPInteractor _otpInteractor;
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    _otpInteractor = OTPInteractor();
    _otpInteractor
        .getAppSignature()
        .then((value) => print("signature = $value"));
    pinController = OTPTextEditController(
      codeLength: 5,
      onCodeReceive: (code) {
        if (kDebugMode) {
          print("Your Application receive code = $code");
        }
      },
      otpInteractor: _otpInteractor,
    )..startListenUserConsent((p0) {
        final exp = RegExp(r'(\d{5})');
        return exp.stringMatch(p0 ?? '') ?? '';
      });
  }

  @override
  void dispose() {
    super.dispose();
    pinController.stopListen();
  }

  @override
  Widget build(BuildContext context) {
    final defaultPinTheme = PinTheme(
      width: 56,
      height: 56,
      textStyle: TextStyle(
          fontSize: 20,
          color: Theme.of(context).scaffoldBackgroundColor,
          fontWeight: FontWeight.w600),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withOpacity(0.5),
        border: Border.all(color: const Color.fromARGB(255, 1, 30, 54)),
        borderRadius: BorderRadius.circular(16),
      ),
    );
    final screenSize = MediaQuery.of(context).size;
    return Scaffold(
      body: SingleChildScrollView(
        child: Container(
            height: screenSize.height * 0.9,
            margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 20),
            child: Column(
              children: [
                Image.asset(
                  "assets/images/logo-variation1.png",
                  width: screenSize.width * 0.45,
                ),
                Text(
                  'enter_otp'.tr,
                  style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
                ),
                const SizedBox(
                  height: 15,
                ),
                Text(
                  '${'please_enter_otp_sent_to'.tr}${widget.phoneNumber}',
                  style: const TextStyle(
                      color: AppColors.greyTextColor,
                      fontSize: 15,
                      fontWeight: FontWeight.w600),
                ),
                const SizedBox(
                  height: 40,
                ),
                Pinput(
                  length: 5,

                  controller: pinController,
                  
                  defaultPinTheme: defaultPinTheme,
                  showCursor: true,
                  onCompleted: (value) async {
                    var res = await authenticationController.confirmOtp(value);
                    if (res) {
                      Get.to(() => const PinConfirmPage());
                    }
                    Snack.show(res, authenticationController.apiMessage.string);
                  },
                ),
                const SizedBox(
                  height: 20,
                ),
                Obx(
                  () => CustomKtButton(
                    isLoading: authenticationController.isOtploading.isTrue,
                    onPress: () async {
                      var res = await authenticationController
                          .confirmOtp(pinController.text.trim());
                      if (res) {
                        Get.to(() => const PinConfirmPage());
                      }
                      Snack.show(
                          res, authenticationController.apiMessage.string);

                      // Get.to(() => const PinConfirmPage());
                    },
                    text: "confirm",
                    onPressed: () {},
                    btnText: 'confirm'.tr,
                  ),
                ),
                const SizedBox(
                  height: 15,
                ),
                RichTextWidget(
                    text1: 'did_not_receive_otp'.tr,
                    onPress: () async {
                      final res =
                          await authenticationController.forgotPassRequest(
                        widget.phoneNumber ?? '',
                      );
                      if (res) {
                        Snack.show(
                            res, authenticationController.apiMessage.string);
                      } else {
                        Snack.showError(
                          duration: 10,
                          message1: authenticationController.apiMessage.string,
                        );
                      }
                    },
                    text2: 'resend_otp'.tr),
                const Spacer(),
                RichTextWidget(
                    text1: 'have_an_account'.tr,
                    onPress: () {
                      Get.to(() => const LoginScreen());
                    },
                    text2: 'sign_in_lower'.tr)
              ],
            )),
      ),
    );
  }
}
