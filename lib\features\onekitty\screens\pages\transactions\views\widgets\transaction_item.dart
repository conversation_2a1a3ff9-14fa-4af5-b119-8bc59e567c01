// Unified Transaction Item Widget
// Displays transaction items for all transaction types with consistent UI

import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:get/get.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/transactions/views/widgets/pdf_preview.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/transactions/views/widgets/trans_pdf.dart';
import 'package:onekitty_admin/utils/formatted_currency.dart';
import 'package:share_plus/share_plus.dart';
import 'package:onekitty_admin/models/transaction_model.dart';
import 'package:onekitty_admin/models/transaction_edit_models.dart';
import 'package:onekitty_admin/features/onekitty/widgets/transaction_edit_dialog.dart' as edit_dialog;
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/controllers/user_ktty_controller.dart';
import 'package:onekitty_admin/services/share_whatsapp_service.dart';

import 'package:onekitty_admin/helpers/colors.dart';
import '../../models/transaction_type.dart';

class TransactionItem extends StatefulWidget {
  final TransactionModel transaction;
  final TransactionType transactionType;
  final VoidCallback? onTap;
  final bool showEditOption;

  const TransactionItem({
    super.key,
    required this.transaction,
    required this.transactionType,
    this.onTap,
    this.showEditOption = false,
  });

  @override
  State<TransactionItem> createState() => _TransactionItemState();
}

class _TransactionItemState extends State<TransactionItem> {
  void _showTransactionDetails() {
    showDialog(
      context: context,
      builder: (BuildContext context) => _buildTransactionDialog(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: Colors.grey[200]!),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: _showTransactionDetails,
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: _buildTransactionHeader(),
        ),
      ),
    );
  }

  Widget _buildTransactionHeader() {
    return Row(
      children: [
        _buildCircularAvatar(),
        SizedBox(width: 12.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _getName(),
                style: const TextStyle(
                  color: Colors.black87,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: 4.h),
              Text(
                _getTransactionReference(),
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: 4.h),
              _buildStatusChip(),
            ],
          ),
        ),
        Text(
          '${_getAmountSign()} ${_formatAmount(widget.transaction.amount)}',
          style: TextStyle(
            color: _getAmountColor(),
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  /// Build circular avatar with first letter of company name
  Widget _buildCircularAvatar() {
    String firstLetter =
        _getName().isNotEmpty ? _getName()[0].toUpperCase() : '.';

    return Container(
      width: 40.w,
      height: 40.w,
      decoration: const BoxDecoration(
        color: AppColors.primary,
        shape: BoxShape.circle,
      ),
      child: Center(
        child: Text(
          firstLetter,
          style: TextStyle(
            color: Colors.white,
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  /// Get company name from transaction
  String _getName() {
    if (widget.transaction.secondName != null &&
        widget.transaction.secondName!.isNotEmpty &&
        widget.transaction.firstName != null &&
        widget.transaction.firstName!.isNotEmpty) {
      return "${widget.transaction.firstName} ${widget.transaction.secondName}";
    }

    if (widget.transaction.firstName != null &&
        widget.transaction.firstName!.isNotEmpty) {
      return widget.transaction.firstName!;
    }
    if (widget.transaction.secondName != null &&
        widget.transaction.secondName!.isNotEmpty) {
      return widget.transaction.secondName!;
    }
    return '.';
  }

  /// Get transaction reference/ID
  String _getTransactionReference() {
    if (widget.transaction.transactionRef != null &&
        widget.transaction.transactionRef!.isNotEmpty) {
      return widget.transaction.transactionRef!;
    }

    if (widget.transaction.transactionCode != null &&
        widget.transaction.transactionCode!.isNotEmpty) {
      return widget.transaction.transactionCode!;
    }

    return "n_a".tr;
  }

  /// Get amount sign (+ or -)
  String _getAmountSign() {
    final type = widget.transaction.typeInOut?.toLowerCase();
    if (type == 'in') return '+';
    if (type == 'out') return '-';
    return '.';
  }

  Widget _buildTransactionDialog() {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Container(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Dialog header
            Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.vertical(top: Radius.circular(16.r)),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      'transaction_details'.tr,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, color: Colors.white),
                  ),
                ],
              ),
            ),

            // Dialog content
            Flexible(
              child: SingleChildScrollView(
                padding: EdgeInsets.all(16.w),
                child: _buildTransactionDetails(),
              ),
            ),

            // Action buttons
            Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                border: Border(top: BorderSide(color: Colors.grey[200]!)),
              ),
              child: _buildActionButtons(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionDetails() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Date
        _buildDetailRow(
          'date'.tr,
          DateFormat('dd MMM yyyy, hh:mm a')
              .format(widget.transaction.createdAt ?? DateTime.now()),
          Icons.access_time,
        ),

        // Transaction code
        if (widget.transaction.transactionCode != null &&
            widget.transaction.transactionCode!.isNotEmpty) ...[
          SizedBox(height: 12.h),
          _buildDetailRow(
            'transaction_code'.tr,
            widget.transaction.transactionCode!,
            Icons.qr_code_outlined,
          ),
        ],

        // Transaction reference
        if (widget.transaction.transactionRef != null &&
            widget.transaction.transactionRef!.isNotEmpty) ...[
          SizedBox(height: 12.h),
          _buildDetailRow(
            'reference'.tr,
            widget.transaction.transactionRef!,
            Icons.receipt_outlined,
          ),
        ],

        // Phone number
        if (widget.transaction.phoneNumber != null &&
            widget.transaction.phoneNumber!.isNotEmpty) ...[
          SizedBox(height: 12.h),
          _buildDetailRow(
            'phone_number'.tr,
            widget.transaction.phoneNumber!,
            Icons.phone_outlined,
          ),
        ],

        // Email
        if (widget.transaction.email != null &&
            widget.transaction.email!.isNotEmpty) ...[
          SizedBox(height: 12.h),
          _buildDetailRow(
            'email'.tr,
            widget.transaction.email!,
            Icons.email_outlined,
          ),
        ],

        // Account number
        if (widget.transaction.accounNumber != null &&
            widget.transaction.accounNumber!.isNotEmpty) ...[
          SizedBox(height: 12.h),
          _buildDetailRow(
            'account'.tr,
            widget.transaction.accounNumber!,
            Icons.account_balance_outlined,
          ),
        ],

        // Product type
        if (widget.transaction.product != null &&
            widget.transaction.product!.isNotEmpty) ...[
          SizedBox(height: 12.h),
          _buildDetailRow(
            'type'.tr,
            widget.transaction.product!.toUpperCase(),
            Icons.category_outlined,
          ),
        ],

        // Kitty info
        if (_shouldShowKittyInfo()) ...[
          SizedBox(height: 12.h),
          _buildDetailRow(
            'kitty'.tr,
            widget.transaction.kittyTitle ?? 'n_a'.tr,
            Icons.savings_outlined,
          ),
        ],

        // Payment reference
        if (widget.transactionType == TransactionType.kitty &&
            widget.transaction.payment_ref != null &&
            widget.transaction.payment_ref!.isNotEmpty &&
            widget.transaction.payment_ref !=
                widget.transaction.transactionRef) ...[
          SizedBox(height: 12.h),
          _buildDetailRow(
            'payment_ref'.tr,
            widget.transaction.payment_ref!,
            Icons.payment,
          ),
        ],

        // Amount
        SizedBox(height: 16.h),
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: _getAmountColor().withOpacity(0.1),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Row(
            children: [
              Icon(
                Icons.attach_money,
                color: _getAmountColor(),
                size: 20.w,
              ),
              SizedBox(width: 8.w),
              Text(
                '${'amount_label'.tr}: ',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Expanded(
                child: Text(
                  '${_getAmountSign()} ${_formatAmount(widget.transaction.amount)}',
                  style: TextStyle(
                    color: _getAmountColor(),
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ),
            ],
          ),
        ),

        // Status
        SizedBox(height: 12.h),
        Row(
          children: [
            Icon(
              Icons.info_outline,
              size: 16.w,
              color: AppColors.neutralGrey,
            ),
            SizedBox(width: 8.w),
            Text(
              '${'status_label'.tr}: ',
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.w400,
              ),
            ),
            _buildStatusChip(),
          ],
        ),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16.w,
          color: AppColors.neutralGrey,
        ),
        SizedBox(width: 8.w),
        Text(
          '$label: ',
          style: const TextStyle(
            //color: Colors.black54,
            fontSize: 12,
            fontWeight: FontWeight.w400,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              //color: Colors.black87,
              fontSize: 12,
              fontWeight: FontWeight.w400,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildStatusChip() {
    final status = widget.transaction.status ?? 'PROGRESS';
    final isSuccess = status.toLowerCase().contains('success') ||
        status.toLowerCase().contains('completed');
    final isPending = status.toLowerCase().contains('pending') ||
        status.toLowerCase().contains('progress');

    Color backgroundColor;
    Color textColor;
    String displayStatus;

    if (isSuccess) {
      backgroundColor = Colors.green.withOpacity(0.1);
      textColor = Colors.green.shade700;
      displayStatus = 'SUCCESS';
    } else if (isPending) {
      backgroundColor = Colors.orange.withOpacity(0.1);
      textColor = Colors.orange.shade700;
      displayStatus = 'PROGRESS';
    } else {
      backgroundColor = Colors.red.withOpacity(0.1);
      textColor = Colors.red.shade700;
      displayStatus = status.toUpperCase();
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: Text(
        displayStatus,
        style: TextStyle(
          color: textColor,
          fontSize: 10.sp,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    final bool canEdit = _canEditTransaction();

    return Row(
      children: [
        // Edit button - only show if transaction can be edited
        if (canEdit) ...[
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () => _showEditDialog(context),
              icon: Icon(Icons.edit_outlined, size: 16.w),
              label: AutoSizeText(
                'edit'.tr,
                minFontSize: 7,
                maxLines: 1,
              ),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppColors.primary,
                side: const BorderSide(color: AppColors.midBlue),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
            ),
          ),
          SizedBox(width: 12.w),
        ],

        // Share button - always available
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _shareTransaction(),
            icon: Icon(Icons.share_outlined, size: 16.w),
            label: AutoSizeText(
              'share'.tr,
              minFontSize: 7,
              maxLines: 1,
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
          ),
        ),

        SizedBox(width: 12.w),

        // Receipt button
        (widget.transactionType == TransactionType.event)
            ? OutlinedButton.icon(
                label: Text('View Ticket'),
                onPressed: () => _showTransactionReceipt(),
                icon: Icon(
                  Icons.receipt_outlined,
                  color: AppColors.primary,
                  size: 20.w,
                ),
                style: OutlinedButton.styleFrom(
                  foregroundColor: AppColors.primary,
                  side: const BorderSide(color: AppColors.midBlue),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                ),
              )
            : IconButton(
                onPressed: () => _showTransactionReceipt(),
                icon: Icon(
                  Icons.receipt_outlined,
                  color: AppColors.primary,
                  size: 20.w,
                ),
              ),
      ],
    );
  }

  String _formatAmount(num? amount) {
    return FormattedCurrency.getFormattedCurrency(amount);
  }

  Color _getAmountColor() {
    final type = widget.transaction.typeInOut?.toLowerCase();
    if (type == 'in') return Colors.green;
    if (type == 'out') return Colors.red;
    return AppColors.dark;
  }

  bool _shouldShowKittyInfo() {
    return widget.transactionType == TransactionType.user &&
        widget.transaction.kittyTitle != null &&
        widget.transaction.kittyTitle!.isNotEmpty;
  }

  /// Check if transaction can be edited
  /// Only contributions (product == 'contribution' && typeInOut == 'in') can be edited
  bool _canEditTransaction() {
    return widget.transaction.product?.toLowerCase() == 'contributions' &&
        widget.transaction.typeInOut?.toLowerCase() == 'in';
  }

  /// Show transaction edit dialog
  void _showEditDialog(BuildContext context) {
    edit_dialog.showTransactionEditDialog(
      context: context,
      transaction: widget.transaction,

      isAdmin: false, // Default to false for user transactions
      onSave: (formData) async {
        try {
          // Create the edit request
          final editRequest = TransactionEditRequest(
            internalId: widget.transaction.internalId ?? '',
            newFirstName: formData.firstName,
            newSecondName: formData.secondName,
            newPaymentRef: formData.paymentRef,
            showNames: formData.showNames,
            reason: 'User requested edit',
          );

          // Get the controller and call edit transaction
          final controller = Get.find<UserKittyController>();
          final response = await controller.editTransaction(editRequest);

          if (response.success) {
            Navigator.of(context).pop(); // Close the edit dialog
            Get.snackbar(
              'Success',
              'Transaction updated successfully',
              backgroundColor: Colors.green,
              colorText: Colors.white,
            );
          } else {
            Get.snackbar(
              'Error',
              response.message,
              backgroundColor: Colors.red,
              colorText: Colors.white,
            );
          }
        } catch (e) {
          Get.snackbar(
            'Error',
            'Failed to update transaction: $e',
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
        }
      },
    );
  }

  /// Share transaction details - shows export options bottom sheet
  void _shareTransaction() {
    _showExportBottomSheet();
  }

  /// Show export options bottom sheet
  void _showExportBottomSheet() {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      builder: (BuildContext context) {
        return _buildExportBottomSheet();
      },
    );
  }

  /// Build export bottom sheet widget
  Widget _buildExportBottomSheet() {
    return Container(
      padding: EdgeInsets.all(16.w),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40.w,
            height: 4.h,
            margin: EdgeInsets.only(bottom: 20.h),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2.r),
            ),
          ),

          // Title
          Text(
            'export_transaction'.tr,
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              //color: Colors.black87,
            ),
          ),

          SizedBox(height: 20.h),

          // Export to Text Option
          ListTile(
            leading: Container(
              width: 40.w,
              height: 40.w,
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Icon(
                Icons.text_fields,
                color: AppColors.primary,
                size: 20.w,
              ),
            ),
            title: Text("export_to_text".tr),
            subtitle: Text("share_via_any_app".tr),
            onTap: () async {
              // Get.back();
              await _shareAsText();
            },
          ),

          SizedBox(height: 8.h),

          // WhatsApp Share Option
          ListTile(
            leading: Container(
              width: 40.w,
              height: 40.w,
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Icon(
                Icons.message,
                color: Colors.green,
                size: 20.w,
              ),
            ),
            title: Text("whatsapp_message".tr),
            subtitle: Text("share_directly_via_whatsapp".tr),
            onTap: () async {
              // Get.back();
              await _shareViaWhatsApp();
            },
          ),

          SizedBox(height: 20.h),
        ],
      ),
    );
  }

  /// Share transaction as text using share_plus
  Future<void> _shareAsText() async {
    try {
      String shareMsg = _buildShareMessage();
      await Share.share(shareMsg, subject: 'transaction_details'.tr);
    } catch (e) {
      Get.snackbar(
        'error'.tr,
        '${'failed_to_share_transaction'.tr}: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// Share transaction via WhatsApp
  Future<void> _shareViaWhatsApp() async {
    try {
      String shareMsg = _buildShareMessage();
      await ShareWhatsapp.share(shareMsg);
    } catch (e) {
      Get.snackbar(
        'error'.tr,
        '${'failed_to_share_via_whatsapp'.tr}: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// Build share message with format from guide
  String _buildShareMessage() {
    final DateFormat format = DateFormat.MMMEd().add_jms();
    final DateTime createdAt = widget.transaction.createdAt ?? DateTime.now();

    // Get DataController to access kitty information
    final DataController dataController = Get.find<DataController>();

    // Build message according to guide format
    String shareMsg =
        "Title: ${dataController.kitty.value.kitty?.title ?? 'N/A'}\n"
        "Phone Number: ${widget.transaction.phoneNumber ?? 'N/A'}\n"
        "Amount: ${FormattedCurrency.getFormattedCurrency(widget.transaction.amount)}\n"
        "Name: ${widget.transaction.firstName ?? ''} ${widget.transaction.secondName ?? ''}\n"
        "Transaction Code: ${widget.transaction.transactionCode ?? 'N/A'}\n"
        "Date: ${format.format(createdAt.toLocal())}\n"
        "Kitty: https://onekitty.co.ke/kitty/${widget.transaction.kittyId ?? dataController.kitty.value.kitty?.iD ?? 0}";

    return shareMsg;
  }

  /// Show transaction receipt bottom sheet or tickets if available
  void _showTransactionReceipt() {
    // Check if transaction has tickets
    if (widget.transaction.transactionTicket != null &&
        widget.transaction.transactionTicket!.isNotEmpty) {
      _showTicketsBottomSheet();
    } else {
      _showReceiptBottomSheet();
    }
  }

  /// Show tickets bottom sheet
  void _showTicketsBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      builder: (BuildContext context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.8,
          minChildSize: 0.5,
          maxChildSize: 0.95,
          expand: false,
          builder: (context, scrollController) {
            return _buildTicketsBottomSheet(scrollController);
          },
        );
      },
    );
  }

  /// Show regular receipt bottom sheet
  void _showReceiptBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      builder: (BuildContext context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.9,
          minChildSize: 0.5,
          maxChildSize: 0.95,
          expand: false,
          builder: (context, scrollController) {
            return _buildTransactionReceipt(scrollController);
          },
        );
      },
    );
  }

  /// Build transaction receipt widget
  Widget _buildTransactionReceipt(ScrollController scrollController) {
    final DateFormat format = DateFormat('dd MMM yyyy, hh:mm a');
    final DateTime createdAt = widget.transaction.createdAt ?? DateTime.now();
    final DataController dataController = Get.find<DataController>();

    return Container(
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            width: 40.w,
            height: 4.h,
            margin: EdgeInsets.only(top: 12.h, bottom: 8.h),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2.r),
            ),
          ),

          // Header with close button
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'transaction_receipt'.tr,
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                    //color: Colors.black87,
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: Icon(Icons.close, size: 24.w),
                ),
              ],
            ),
          ),

          Expanded(
            child: SingleChildScrollView(
              controller: scrollController,
              padding: EdgeInsets.all(16.w),
              child: Column(
                children: [
                  // Header Section with Logo and Company Info
                  buildReceiptHeader(),

                  SizedBox(height: 24.h),

                  // Main Receipt Card
                  Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12.r),
                      border: Border.all(color: Colors.grey[300]!, width: 1),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withOpacity(0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        // Receipt Header Section
                        buildReceiptHeaderSection(widget.transaction),

                        // Customer Information Section
                        if (widget.transaction.firstName != null ||
                            widget.transaction.secondName != null ||
                            widget.transaction.phoneNumber != null ||
                            widget.transaction.email != null)
                          buildCustomerSection(widget.transaction),

                        // Transaction Details Section
                        buildTransactionSection(widget.transaction),

                        // Amount Section (Highlighted)
                        buildAmountSection(widget.transaction),

                        // Receipt Footer with Thank You Message
                        buildReceiptFooterSection(widget.transaction),
                      ],
                    ),
                  ),

                  SizedBox(height: 24.h),

                  // Company Footer
                  buildCompanyFooter(),

                  SizedBox(height: 40.h), // Bottom padding
                ],
              ),
            ),
          ),

          // Action buttons
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: AppColors.background,
              border: Border(top: BorderSide(color: Colors.grey[200]!)),
            ),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _shareReceipt(),
                    icon: Icon(Icons.share_outlined, size: 18.w),
                    label: Text('share'.tr),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: AppColors.primary,
                      side: const BorderSide(color: AppColors.primary),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      padding: EdgeInsets.symmetric(vertical: 12.h),
                    ),
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _saveToPDF(),
                    icon: Icon(Icons.download_outlined, size: 18.w),
                    label: Text('save_pdf'.tr),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      padding: EdgeInsets.symmetric(vertical: 12.h),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build tickets bottom sheet widget
  Widget _buildTicketsBottomSheet(ScrollController scrollController) {
    final tickets = widget.transaction.transactionTicket!;

    return Container(
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            width: 40.w,
            height: 4.h,
            margin: EdgeInsets.only(top: 12.h, bottom: 8.h),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2.r),
            ),
          ),

          // Header with close button
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'transaction_tickets'.tr,
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: Icon(Icons.close, size: 24.w),
                ),
              ],
            ),
          ),

          // Tickets list
          Expanded(
            child: ListView.builder(
              controller: scrollController,
              padding: EdgeInsets.all(16.w),
              itemCount: tickets.length,
              itemBuilder: (context, index) {
                return _buildTicketItem(tickets[index]);
              },
            ),
          ),

          // Action buttons
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: AppColors.background,
              border: Border(top: BorderSide(color: Colors.grey[200]!)),
            ),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _shareTickets(),
                    icon: Icon(Icons.share_outlined, size: 18.w),
                    label: Text('share_tickets'.tr),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: AppColors.primary,
                      side: const BorderSide(color: AppColors.primary),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      padding: EdgeInsets.symmetric(vertical: 12.h),
                    ),
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _showReceiptBottomSheet(),
                    icon: Icon(Icons.receipt_outlined, size: 18.w),
                    label: Text('view_receipt'.tr),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      padding: EdgeInsets.symmetric(vertical: 12.h),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build individual ticket item widget
  Widget _buildTicketItem(TransactionTicket ticket) {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: Colors.grey[300]!),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Ticket header
          Row(
            children: [
              Container(
                width: 40.w,
                height: 40.w,
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  Icons.confirmation_number_outlined,
                  color: AppColors.primary,
                  size: 20.w,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      ticket.ticket?.title ?? 'event_ticket'.tr,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (ticket.ticket?.description != null &&
                        ticket.ticket!.description!.isNotEmpty) ...[
                      SizedBox(height: 2.h),
                      Text(
                        ticket.ticket!.description!,
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: Colors.grey[600],
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),
              _buildTicketStatusChip(ticket.status ?? 'Unknown'),
            ],
          ),

          SizedBox(height: 16.h),

          // Ticket details
          Row(
            children: [
              Expanded(
                child: _buildTicketDetailItem(
                  'quantity'.tr,
                  '${ticket.quantity ?? 0}',
                  Icons.confirmation_number,
                ),
              ),
              Expanded(
                child: _buildTicketDetailItem(
                  'amount_label'.tr,
                  _formatAmount(ticket.amount),
                  Icons.attach_money,
                ),
              ),
            ],
          ),

          if (ticket.ticket?.ticketType != null &&
              ticket.ticket!.ticketType!.isNotEmpty) ...[
            SizedBox(height: 12.h),
            _buildTicketDetailItem(
              'type'.tr,
              ticket.ticket!.ticketType!.toUpperCase(),
              Icons.category_outlined,
            ),
          ],

          if (ticket.internalReference != null &&
              ticket.internalReference!.isNotEmpty) ...[
            SizedBox(height: 12.h),
            _buildTicketDetailItem(
              'reference'.tr,
              ticket.internalReference!,
              Icons.qr_code_outlined,
            ),
          ],
        ],
      ),
    );
  }

  /// Build ticket detail item
  Widget _buildTicketDetailItem(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16.w,
          color: AppColors.neutralGrey,
        ),
        SizedBox(width: 8.w),
        Text(
          '$label: ',
          style: TextStyle(
            fontSize: 12.sp,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.w600,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  /// Build ticket status chip
  Widget _buildTicketStatusChip(String status) {
    final isConfirmed = status.toLowerCase().contains('success') ||
        status.toLowerCase().contains('active');
    final isPending = status.toLowerCase().contains('pending');

    Color backgroundColor;
    Color textColor;

    if (isConfirmed) {
      backgroundColor = Colors.green.withOpacity(0.1);
      textColor = Colors.green.shade700;
    } else if (isPending) {
      backgroundColor = Colors.orange.withOpacity(0.1);
      textColor = Colors.orange.shade700;
    } else {
      backgroundColor = Colors.red.withOpacity(0.1);
      textColor = Colors.red.shade700;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Text(
        status,
        style: TextStyle(
          color: textColor,
          fontSize: 10.sp,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// Share tickets information
  Future<void> _shareTickets() async {
    try {
      String ticketsText = _buildTicketsShareText();
      await Share.share(ticketsText, subject: 'event_tickets'.tr);
    } catch (e) {
      Get.snackbar(
        'error'.tr,
        '${'failed_to_share_tickets'.tr}: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// Build tickets share text
  String _buildTicketsShareText() {
    final DateFormat format = DateFormat('dd MMM yyyy, hh:mm a');
    final DateTime createdAt = widget.transaction.createdAt ?? DateTime.now();
    final tickets = widget.transaction.transactionTicket!;

    List<String> shareLines = [];
    shareLines.add('=== EVENT TICKETS ===');
    shareLines.add('');
    shareLines
        .add('Transaction: ${widget.transaction.transactionCode ?? 'N/A'}');
    shareLines.add('Date: ${format.format(createdAt)}');
    shareLines.add('');

    for (int i = 0; i < tickets.length; i++) {
      final ticket = tickets[i];
      shareLines.add('TICKET ${i + 1}:');
      shareLines.add('Title: ${ticket.ticket?.title ?? 'Event Ticket'}');
      shareLines.add('Quantity: ${ticket.quantity ?? 0}');
      shareLines.add('Amount: ${_formatAmount(ticket.amount)}');
      shareLines.add('Status: ${ticket.status ?? 'Unknown'}');
      if (ticket.internalReference != null &&
          ticket.internalReference!.isNotEmpty) {
        shareLines.add('Reference: ${ticket.internalReference}');
      }
      shareLines.add('');
    }

    shareLines.add('Total Amount: ${_formatAmount(widget.transaction.amount)}');
    shareLines.add('');
    shareLines.add('Thank you for using OneKitty!');
    shareLines.add('Visit us at: https://onekitty.co.ke');

    return shareLines.join('\n');
  }

  /// Share receipt as text
  Future<void> _shareReceipt() async {
    try {
      String receiptText = _buildReceiptText();
      await Share.share(receiptText, subject: 'transaction_receipt'.tr);
    } catch (e) {
      Get.snackbar(
        'error'.tr,
        '${'failed_to_share_receipt'.tr}: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// Build receipt text for sharing
  String _buildReceiptText() {
    final DateFormat format = DateFormat('dd MMM yyyy, hh:mm a');
    final DateTime createdAt = widget.transaction.createdAt ?? DateTime.now();
    final DataController dataController = Get.find<DataController>();

    List<String> receiptLines = [];
    receiptLines
        .add('=== ${'onekitty_transaction_receipt'.tr.toUpperCase()} ===');
    receiptLines.add('');
    receiptLines.add(
        '${'receipt_no'.tr}: ${widget.transaction.transactionCode ?? 'n_a'.tr}');
    receiptLines.add('${'date'.tr}: ${format.format(createdAt)}');
    receiptLines
        .add('${'status_label'.tr}: ${widget.transaction.status ?? 'n_a'.tr}');
    receiptLines.add('');

    if (widget.transaction.firstName != null ||
        widget.transaction.secondName != null) {
      receiptLines.add(
          '${'customer'.tr}: ${widget.transaction.firstName ?? ''} ${widget.transaction.secondName ?? ''}'
              .trim());
    }

    if (widget.transaction.phoneNumber != null &&
        widget.transaction.phoneNumber!.isNotEmpty) {
      receiptLines
          .add('${'phone_number'.tr}: ${widget.transaction.phoneNumber}');
    }

    receiptLines.add('');

    if (dataController.kitty.value.kitty?.title != null) {
      receiptLines
          .add('${'kitty'.tr}: ${dataController.kitty.value.kitty!.title}');
    }

    if (widget.transaction.product != null &&
        widget.transaction.product!.isNotEmpty) {
      receiptLines
          .add('${'type'.tr}: ${widget.transaction.product!.toUpperCase()}');
    }

    receiptLines.add('');
    receiptLines.add(
        '${'amount_label'.tr.toUpperCase()}: ${_formatAmount(widget.transaction.amount)}');
    receiptLines.add('');
    receiptLines.add('${'thank_you_for_using_onekitty'.tr}!');
    receiptLines.add('${'visit_us_at'.tr}: https://onekitty.co.ke');

    return receiptLines.join('\n');
  }

  /// Save receipt as PDF
  Future<void> _saveToPDF() async {
    try {
      // Show loading
      Get.dialog(
        const Center(
          child: CircularProgressIndicator(),
        ),
        barrierDismissible: false,
      );

      await generateAndSavePDF(widget.transaction);

      // Close loading
      Get.back();

      Get.snackbar(
        'success'.tr,
        'receipt_saved_as_pdf_successfully'.tr,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      // Close loading
      Get.back();

      Get.snackbar(
        'error'.tr,
        '${'failed_to_save_pdf'.tr}: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
}
