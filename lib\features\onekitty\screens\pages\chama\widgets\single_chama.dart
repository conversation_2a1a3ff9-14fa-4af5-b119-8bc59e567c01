import 'package:date_time_format/date_time_format.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty_admin/features/onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty_admin/models/chama/chama_model.dart';
import 'package:onekitty_admin/nav_routes/nav_routes.dart';
import 'package:onekitty_admin/main.dart' show isLight;
import '../../../../../../../../utils/utils_exports.dart';

class SingleChamaWidget extends StatelessWidget {
  final UserChama chama;
  final Chama chamaDts;

  const SingleChamaWidget({
    super.key,
    required this.chama,
    required this.chamaDts,
  });

  void viewChamaDetails() {
    final chamaDataController = Get.put(ChamaDataController());
    chamaDataController.chama.value = chama;
    chamaDataController.singleChamaDts.value = chamaDts;
    Get.toNamed(NavRoutes.viewingSingleChama);
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      borderRadius: BorderRadius.circular(20),
      color: Colors.transparent,
      child: InkWell(
        onTap: viewChamaDetails,
        borderRadius: BorderRadius.circular(20),
        child: Container(
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: isLight.value ? Colors.white : Colors.grey.shade900,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(),
              SizedBox(height: 20.h),
              _buildMemberSection(),
              SizedBox(height: 16.h),
              _buildDetailsFooter(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Expanded(
          child: Text(
            chama.chama?.title ?? 'chama'.tr,
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w800,
              color: isLight.value ? Colors.grey.shade800 : Colors.white,
              height: 1.2,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        SizedBox(width: 12.w),
        _buildBalanceChip(),
      ],
    );
  }

  Widget _buildBalanceChip() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      decoration: BoxDecoration(
        color: isLight.value
            ? Colors.green.shade50
            : Colors.green.shade900.withOpacity(0.3),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Text(
        FormattedCurrency.getFormattedCurrency(chama.chama?.totaBal),
        style: TextStyle(
          fontSize: 14.sp,
          fontWeight: FontWeight.w700,
          color: isLight.value ? Colors.green.shade700 : Colors.greenAccent,
        ),
      ),
    );
  }

  Widget _buildMemberSection() {
    final List<String> circleImages = [
      AssetUrl.group6,
      AssetUrl.winter,
      AssetUrl.imgEllipse1,
    ];
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(
              width: 84.w,
              height: 40.h,
              child: Stack(
                clipBehavior: Clip.none,
                children: List.generate(
                  circleImages.length,
                  (index) => Positioned(
                    left: index * 24.w,
                    child: CircleAvatar(
                      radius: 16.w,
                      backgroundColor: Colors.white,
                      child: CircleAvatar(
                        radius: 14.w,
                        backgroundImage: AssetImage(circleImages[index]),
                      ),
                    ),
                  ),
                ),
              ),
            ),
            SizedBox(width: 12.w),
            Text(
              "${chama.membersCount} ${'members_count'.tr}",
              style: TextStyle(
                fontSize: 13.sp,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        SizedBox(height: 12.h),
        Row(
          children: [
            Icon(Icons.currency_exchange, size: 18.w, color: Colors.blue),
            SizedBox(width: 8.w),
            Text(
              "${FormattedCurrency.getFormattedCurrency(chama.chama?.amount ?? 0)}/${chama.chama?.frequency}",
              style: TextStyle(
                fontSize: 13.sp,
                fontWeight: FontWeight.w600,
                color: isLight.value ? Colors.grey.shade800 : Colors.white,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDetailsFooter() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Flexible(
          child: Row(
            children: [
              Icon(Icons.schedule, size: 18.w, color: Colors.orange),
              SizedBox(width: 8.w),
              Flexible(
                child: Text(
                  DateTimeFormat.relative(
                    chama.chama!.nextOccurrence ?? DateTime.now(),
                    levelOfPrecision: 1,
                    prependIfBefore: 'Next in ',
                    ifNow: 'now'.tr,
                  ),
                  style: TextStyle(
                    fontSize: 13.sp,
                    fontWeight: FontWeight.w500,
                    color: isLight.value
                        ? Colors.grey.shade600
                        : Colors.grey.shade400,
                  ),
                ),
              ),
            ],
          ),
        ),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 4.h),
          decoration: BoxDecoration(
            color: Colors.blue.withOpacity(0.1),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Text(
            chama.member?.role ?? 'member'.tr,
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.w700,
              color: Colors.blue.shade700,
            ),
          ),
        ),
      ],
    );
  }
}
