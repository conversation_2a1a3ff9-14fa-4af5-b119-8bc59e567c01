import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get_utils/get_utils.dart';
import 'package:onekitty_admin/utils/themes_colors.dart';

enum ToastType { error, success, information, normal }

class ToastUtils {
  static void showToast(String message,
      {ToastType toastType = ToastType.normal}) {
    Fluttertoast.showToast(
      msg: message,
      toastLength: Toast.LENGTH_LONG,
      gravity: ToastGravity.TOP,
      timeInSecForIosWeb: 5,
      backgroundColor: _getToastBgColor(toastType),
      textColor: Colors.white,
      fontSize: 14.0,
    );
  }

  static void showSuccessToast(
      BuildContext context, String message, String? desc) async {
    await Fluttertoast.showToast(
      msg: message,
      toastLength: Toast.LENGTH_LONG,
      gravity: ToastGravity.TOP,
      timeInSecForIosWeb: 5,
      backgroundColor: _getToastBgColor(ToastType.success),
      textColor: Colors.white,
      fontSize: 14.0,
    );
    // CherryToast.success(
    //   title: Text(message),
    //   // backgroundColor: _getToastBgColor(ToastType.success),
    //   autoDismiss: true,
    //   toastPosition: toastPosition ?? Position.top,
    //   description: Text(desc ?? ''),
    // ).show(context);
  }

  static Color? _getToastBgColor(ToastType toastType) {
    switch (toastType) {
      case ToastType.normal:
        return null;
      case ToastType.success:
        return Colors.green;
      case ToastType.information:
        return ColorUtil.blueColor;
      case ToastType.error:
        return ColorUtil.error;
    }
  }

  /* display error */
  static void showErrorToast(BuildContext context, String message, String? desc,
      {bool autoDismiss = true}) {
    showToast('error_message_format'.tr.replaceAll('{message}', message).replaceAll('{description}', desc ?? ''), toastType: ToastType.error);
    // CherryToast.error(
    //   title: Text(message),
    //   autoDismiss: autoDismiss,
    //   description: Text(desc ?? ''),
    // ).show(context);
  }

  static void showInfoToast(
    BuildContext context,
    String message,
    String? desc,
  ) {
    showToast(message, toastType: ToastType.information);

    // CherryToast.info(
    //   title: Text(message),
    //   autoDismiss: true,
    //   description: Text(desc ?? ''),
    // ).show(context);
  }
}
