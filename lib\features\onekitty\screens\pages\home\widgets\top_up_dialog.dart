import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty_admin/helpers/show_toast.dart';
import 'package:onekitty_admin/nav_routes/nav_routes.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/views/screens/cardPayment.dart';
import 'package:onekitty_admin/features/onekitty/screens/widgets/payment_radio.dart';
import 'package:onekitty_admin/features/onekitty/screens/widgets/text_form_field.dart';
import '../../contribution/controllers/user_ktty_controller.dart';

import '../../../../../../../utils/utils_exports.dart';

// ignore_for_file: must_be_immutable
class TopUp extends StatefulWidget {
  const TopUp({super.key});

  @override
  State<TopUp> createState() => _TopUp();
}

class _TopUp extends State<TopUp> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  TextEditingController amountEditTextController = TextEditingController();
  TextEditingController phoneNumberController = TextEditingController();
  TextEditingController emailTextController = TextEditingController();
  final UserKittyController _usercontroller = Get.put(UserKittyController());

  @override
  void initState() {
    phoneNumberController.text =
        _usercontroller.getLocalUser()?.phoneNumber ?? "";
    super.initState();
  }

  bool isNameChecked = false;
  bool isNumberChecked = false;

  int? selectedChannel = 63902;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        title: Text('top_up_to_wallet'.tr),
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: EdgeInsets.only(top: 10.0.h),
          child: Container(
            margin: EdgeInsets.only(bottom: 5.h),
            padding: EdgeInsets.symmetric(horizontal: 30.w),
            child: Stack(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildPhoneFrame(context),
                    SizedBox(height: 20.h),
                    Padding(
                        padding: EdgeInsets.only(left: 1.w),
                        child: Text("amount".tr,
                            style: CustomTextStyles.titleSmallGray90001)),
                    SizedBox(height: 3.h),
                    _buildAmountEditText(context),
                    SizedBox(height: 18.h),
                    Padding(
                        padding: EdgeInsets.only(left: 1.w),
                        child: Text("choose_payment_channel".tr,
                            style: CustomTextStyles.titleSmallGray90001)),
                    SizedBox(height: 10.h),
                    ContributeChannelsBuilder(
                        selectedChannel: selectedChannel ?? 63902,
                        onChange: (newlySelectedChannel) {
                          setState(() {
                            selectedChannel = newlySelectedChannel;
                          });
                        }),
                    SizedBox(height: 18.h),
                    if (selectedChannel == 55) // show email field
                      _buildAEmailField(context),
                    SizedBox(height: 14.h),
                    _buildMakeTopUpButton(context),
                    SizedBox(
                      height: 30.h,
                    )
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Section Widget
  Widget _buildPhoneFrame(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: 1.w, top: 20.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
              padding: EdgeInsets.only(left: 4.w),
              child: Text("phone_number".tr,
                  style: CustomTextStyles.titleSmallGray900)),
          SizedBox(height: 2.h),
          SizedBox(
            child: CustomTextField(
              controller: phoneNumberController,
              paddingHorizontal: 2.spMin,
              maxLength: 13,
              showNoKeyboard: true,
              inputFormatters: <TextInputFormatter>[
                FilteringTextInputFormatter.allow(RegExp("[0-9]")),
              ],
              hintText: "phone_number".tr,
              labelText: "mobile_number_required".tr,
              validator: (value) {
                if (value!.isEmpty) {
                  return "enter_mobile_number".tr;
                } else if (value.length < 9) {
                  return "invalid_mobile_number".tr;
                } else {
                  return null;
                }
              },
            ),
          )
        ],
      ),
    );
  }

  /// Section Widget
  Widget _buildAmountEditText(BuildContext context) {
    return Padding(
        padding: EdgeInsets.only(left: 1.w),
        child: CustomTextField(
          controller: amountEditTextController,
          maxLength: 6,
          paddingHorizontal: 2.spMin,
          showNoKeyboard: true,
          inputFormatters: <TextInputFormatter>[
            FilteringTextInputFormatter.allow(RegExp("[0-9]")),
          ],
          hintText: "amount".tr,
          labelText: "enter_amount_ksh_required".tr,
          validator: (value) {
            if (value!.isEmpty) {
              return "enter_amount".tr;
            }
            final amt = int.tryParse(value);
            if (amt == null) {
              return "enter_valid_amount".tr;
            } else {
              return null;
            }
          },
        ));
  }

  Widget _buildAEmailField(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: 1.w),
      child: CustomTextField(
        controller: emailTextController,
        paddingHorizontal: 2.spMin,
        hintText: "email".tr,
        labelText: "email".tr,
        validator: (value) {
          return null;
        },
      ),
    );
  }

  /// Section Widget
  Widget _buildMakeTopUpButton(BuildContext context) {
    return Obx(() => CustomKtButton(
        isLoading: _usercontroller.topUploading.isTrue,
        onPress: () async {
 

          bool res = await _usercontroller.topup(
            phoneNumber: phoneNumberController.text,
            amount: int.parse(amountEditTextController.text.trim()),
            channel: selectedChannel??63902,
            userId: _usercontroller.getLocalUser()?.id ?? 0,
            email: emailTextController.text,
          );

          if (!mounted) return;
          if (res) {
            if (selectedChannel == 0) {
              ToastUtils.showSuccessToast(
                context,
                _usercontroller.apiMessageTopup.string,
                "success".tr,
              );
              Get.offNamed(NavRoutes.topupOtp);
            }
            if (selectedChannel == 63902) {
              ToastUtils.showSuccessToast(
                context,
                _usercontroller.apiMessageTopup.string,
                "success".tr,
              );
              _usercontroller.getUser();
              Get.back();
            }

            if (selectedChannel == 55) {
              Get.off(() => const CardPayment(isChamaContribute: false));
              //Get.toNamed(NavRoutes.cardpayment);
            }
          } else {
            ToastUtils.showErrorToast(
              context,
              _usercontroller.apiMessageTopup.string,
              "error".tr,
            );
          }
        },
        btnText: "top_up".tr));
  }

  /// Navigates back to the previous screen.
  void onTapArrowLeft(BuildContext context) {
    Navigator.pop(context);
  }
}
