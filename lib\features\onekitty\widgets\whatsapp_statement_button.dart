import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty_admin/services/whatsapp_statement_service.dart';
import 'package:onekitty_admin/utils/custom_text_style.dart';

/// Button widget for requesting WhatsApp statements
class WhatsAppStatementButton extends StatelessWidget {
  final String entityType; // 'event', 'kitty', 'chama'
  final int entityId;
  final String defaultPhoneNumber;
  final String? customLabel;
  final IconData? customIcon;
  final Color? backgroundColor;
  final bool isCompact;

  const WhatsAppStatementButton({
    super.key,
    required this.entityType,
    required this.entityId,
    required this.defaultPhoneNumber,
    this.customLabel,
    this.customIcon,
    this.backgroundColor,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    final service = Get.find<WhatsAppStatementService>();
    
    if (isCompact) {
      return Obx(() => IconButton(
        onPressed: service.isRequestingStatement.value
            ? null
            : () => _requestStatement(context, service),
        icon: service.isRequestingStatement.value
            ? SizedBox(
                width: 16.w,
                height: 16.h,
                child: const CircularProgressIndicator(strokeWidth: 2),
              )
            : Icon(
                customIcon ?? Icons.receipt_long,
                color: backgroundColor ?? Theme.of(context).primaryColor,
              ),
        tooltip: 'request_statement'.tr,
      ));
    }

    return Obx(() => ElevatedButton.icon(
      onPressed: service.isRequestingStatement.value
          ? null
          : () => _requestStatement(context, service),
      icon: service.isRequestingStatement.value
          ? SizedBox(
              width: 16.w,
              height: 16.h,
              child: const CircularProgressIndicator(
                strokeWidth: 2,
                color: Colors.white,
              ),
            )
          : Icon(customIcon ?? Icons.receipt_long),
      label: Text(
        service.isRequestingStatement.value
            ? 'sending'.tr
            : customLabel ?? 'request_statement'.tr,
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: backgroundColor ?? Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    ));
  }

  void _requestStatement(BuildContext context, WhatsAppStatementService service) {
    service.showStatementRequestDialog(
      context: context,
      entityType: entityType,
      entityId: entityId,
      defaultPhoneNumber: defaultPhoneNumber,
    );
  }
}

/// Card widget showing WhatsApp statement options
class WhatsAppStatementCard extends StatelessWidget {
  final String entityType;
  final int entityId;
  final String entityName;
  final String defaultPhoneNumber;

  const WhatsAppStatementCard({
    super.key,
    required this.entityType,
    required this.entityId,
    required this.entityName,
    required this.defaultPhoneNumber,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(8.w),
                  decoration: BoxDecoration(
                    color: Colors.green.shade100,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.chat,
                    color: Colors.green.shade600,
                    size: 24,
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'whatsapp_statement'.tr,
                        style: CustomTextStyles.titleSmallGray900.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        'get_transaction_statements_whatsapp'.tr,
                        style: CustomTextStyles.bodySmallGray600,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            Text(
              'available_statement_types'.tr,
              style: CustomTextStyles.bodyMediumGray600.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: 8.h),
            _buildStatementTypeItem(
              'summary'.tr,
              'quick_overview_key_metrics'.tr,
              Icons.summarize,
            ),
            _buildStatementTypeItem(
              'detailed'.tr,
              'complete_breakdown_transaction_details'.tr,
              Icons.receipt_long,
            ),
            _buildStatementTypeItem(
              'transactions_only'.tr,
              'list_transactions_no_summaries'.tr,
              Icons.list_alt,
            ),
            SizedBox(height: 16.h),
            SizedBox(
              width: double.infinity,
              child: WhatsAppStatementButton(
                entityType: entityType,
                entityId: entityId,
                defaultPhoneNumber: defaultPhoneNumber,
                customLabel: 'request_statement_via_whatsapp'.tr,
                backgroundColor: Colors.green.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatementTypeItem(String title, String description, IconData icon) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: Colors.grey.shade600,
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: CustomTextStyles.bodySmallGray900.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  description,
                  style: CustomTextStyles.bodySmallGray600.copyWith(
                    fontSize: 11,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// Bottom sheet for quick WhatsApp statement request
class WhatsAppStatementBottomSheet extends StatelessWidget {
  final String entityType;
  final int entityId;
  final String entityName;
  final String defaultPhoneNumber;

  const WhatsAppStatementBottomSheet({
    super.key,
    required this.entityType,
    required this.entityId,
    required this.entityName,
    required this.defaultPhoneNumber,
  });

  static void show({
    required BuildContext context,
    required String entityType,
    required int entityId,
    required String entityName,
    required String defaultPhoneNumber,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => WhatsAppStatementBottomSheet(
        entityType: entityType,
        entityId: entityId,
        entityName: entityName,
        defaultPhoneNumber: defaultPhoneNumber,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(20.w),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 40.w,
            height: 4.h,
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          SizedBox(height: 20.h),
          Row(
            children: [
              Icon(
                Icons.chat,
                color: Colors.green.shade600,
                size: 28,
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'whatsapp_statement'.tr,
                      style: CustomTextStyles.titleMediumBlack900,
                    ),
                    Text(
                      '${'request_statement'.tr} for $entityName',
                      style: CustomTextStyles.bodySmallGray600,
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 24.h),
          SizedBox(
            width: double.infinity,
            child: WhatsAppStatementButton(
              entityType: entityType,
              entityId: entityId,
              defaultPhoneNumber: defaultPhoneNumber,
              customLabel: 'request_statement'.tr,
              backgroundColor: Colors.green.shade600,
            ),
          ),
          SizedBox(height: MediaQuery.of(context).viewInsets.bottom),
        ],
      ),
    );
  }
}
