{"status": true, "message": "success", "data": {"media": [{"ID": 45, "CreatedAt": "2025-04-14T21:27:44.744Z", "UpdatedAt": "2025-04-14T21:27:44.744Z", "DeletedAt": null, "title": "Send bulk sms using onekitty", "description": "Do you Know <PERSON><PERSON><PERSON> has a *Bulk SMS* feature, which can be useful for sending messsages at once ✨\n\nVisit https://sms.onekitty.co.ke to get started", "media_url": "https://firebasestorage.googleapis.com/v0/b/onekitty-345a1.appspot.com/o/campaign%2F1744666053184-get-bulk-sms.jpeg?alt=media&token=9b632c11-cce7-4653-a201-496f8b9bb7fb", "type": "BOT_CAMPAIGN", "priority_id": 0, "action_url": "https://sms.onekitty.co.ke", "intent": "", "status": "ACTIVE"}, {"ID": 54, "CreatedAt": "2025-06-08T13:22:37.374Z", "UpdatedAt": "2025-06-08T13:22:37.374Z", "DeletedAt": null, "title": "Why Choose onekitty for your Mchango? ", "description": "Why onekitty.co.ke?\n\n1. its very convinient and promotes transparency \n\n2. Saves your precious time. The AI system runs everything for you. \n\n3. Separate personal funds from contribution funds. Why mix contributions funds with your usual income, it wil be hectic doing reconciliations\n\n4. Reduces the urge of the treasurer to 'eat' contributed money \n\n5. Transparent, every contribution, Reversal, With<PERSON><PERSON> will automatically be updated on the WhatsApp group,\n\n6. Secure - Data in transit and at rest is encrypted .\n\n7. Powered by  partners licensed and regulated by the CBK. \n\n8. A cheaper transaction tarrif for the contributors since its a paybill tarrif. \n\nJoin https://onekitty.co.ke today and experience the freedom 🫵", "media_url": "https://firebasestorage.googleapis.com/v0/b/onekitty-345a1.appspot.com/o/campaign%2F1749388814354-IMG-20241007-WA0008.jpg?alt=media&token=f7296a21-7f4d-4f4d-b0b1-b0f3e70a6284", "type": "BOT_CAMPAIGN", "priority_id": 0, "action_url": "https://onekitty.co.ke/create", "intent": "WEBVIEW", "status": "ACTIVE"}, {"ID": 1, "CreatedAt": "2024-04-04T10:16:20.751Z", "UpdatedAt": "2024-04-04T10:16:20.751Z", "DeletedAt": null, "title": "<PERSON><PERSON><PERSON>", "description": "Onekitty is the ultimate contribution platform", "media_url": "https://firebasestorage.googleapis.com/v0/b/onekitty-345a1.appspot.com/o/WhatsApp%20Image%202024-01-18%20at%2020.09.51.jpeg?alt=media&token=842a8c34-af19-4afb-b56a-bccce9dea88d", "type": "IMAGE", "priority_id": 0, "action_url": "https://onekitty.co.ke/", "intent": "WEBVIEW", "status": "ACTIVE"}, {"ID": 19, "CreatedAt": "2024-12-02T14:59:23.586Z", "UpdatedAt": "2024-12-02T15:05:33.379Z", "DeletedAt": null, "title": "🤝 <PERSON><PERSON><PERSON> Made Simple with OneKitty", "description": "Coordinating a harambee has never been easier! With OneKitty, you can seamlessly create a shared fundraising pool, invite contributors, and manage collections all in one place.\n\n📌 Real-Time Updates: Keep everyone informed with instant notifications on progress.\n📌 Automatic Transfers: Funds are sent to the beneficiary securely and automatically once the target is met.\n\n📌 Simplify your coordination and focus on what matters most – achieving your goals.\n\n Start your harambee with OneKitty today!", "media_url": "https://firebasestorage.googleapis.com/v0/b/onekitty-345a1.appspot.com/o/campaign%2F1733151546615-Are_you_harambee_coordinator.jpeg?alt=media&token=f1239bcc-7d7c-4550-8ce9-cd5c5e72e681", "type": "IMAGE", "priority_id": 0, "action_url": "https://onekitty.co.ke", "intent": "", "status": "ACTIVE"}, {"ID": 43, "CreatedAt": "2025-04-11T12:53:25.828Z", "UpdatedAt": "2025-04-11T12:53:25.828Z", "DeletedAt": null, "title": "Your trust fuels us!", "description": "OneKitty is built on transparency, accountability, and impact. Follow us on X to engage, share your experiences, and grow with us! \n\n👉 https://x.com/onekitty_KE", "media_url": "https://firebasestorage.googleapis.com/v0/b/onekitty-345a1.appspot.com/o/campaign%2F1744375994605-Are_you_harambee_coordinator.jpeg?alt=media&token=5cc50305-b24f-4fb1-b2f4-e39c6ae84900", "type": "BOT_CAMPAIGN", "priority_id": 0, "action_url": "", "intent": "", "status": "ACTIVE"}, {"ID": 46, "CreatedAt": "2025-04-14T21:41:50.841Z", "UpdatedAt": "2025-04-14T21:41:50.841Z", "DeletedAt": null, "title": "Download OneKitty app now and experience smarter fundraising", "description": "Take control of your kitty like never before:\n\n✅ Withdraw funds anytime\n✅ Add & manage signatories and beneficiaries\n✅ Edit kitty details with ease\n✅ Export transactions in PDF, Excel or send via SMS & WhatsApp\n✅ Link multiple WhatsApp groups\n✅ Track referrals\n\nEverything you need — all in one app.\n\nVisit http://onelink.to/nrmce8 to download\n\n", "media_url": "https://firebasestorage.googleapis.com/v0/b/onekitty-345a1.appspot.com/o/campaign%2F1744666887883-download-onekitty-app.jpeg?alt=media&token=a1a24e80-2212-4566-9ca2-fd55e24736c4", "type": "BOT_CAMPAIGN", "priority_id": 0, "action_url": "https://onelink.to/nrmce8", "intent": "", "status": "ACTIVE"}, {"ID": 42, "CreatedAt": "2025-04-11T12:51:02.44Z", "UpdatedAt": "2025-04-18T11:31:37.448Z", "DeletedAt": null, "title": "Enhance Security with <PERSON><PERSON><PERSON><PERSON>al", "description": "Did you know OneKitty allows you to add multiple signatories for transfer transactions?\n\nWith the Maker-Checker process, you can ensure withdrawals require approval, giving you greater control and security over your kitty funds.\n\nvisit https://onekitty.co.ke/create", "media_url": "https://firebasestorage.googleapis.com/v0/b/onekitty-345a1.appspot.com/o/campaign%2F1744664712984-add-multiple-signtories-to-your-group.jpeg?alt=media&token=7c5b997a-7deb-4d99-90a9-6f5b55a0eb1f", "type": "BOT_CAMPAIGN", "priority_id": 0, "action_url": "https://onelink.to/nrmce8", "intent": "", "status": "ACTIVE"}], "page": {"page": 0, "size": 10, "max_page": 1, "total_pages": 2, "total": 17, "last": false, "first": true, "visible": 10}, "user_kitties": [{"kitty": {"ID": 7627, "CreatedAt": "2025-09-12T08:06:23.027Z", "UpdatedAt": "2025-09-29T11:21:56.906Z", "title": "tesrggg yes", "description": "<p>Tesr</p>", "username": "", "beneficiary_account": "************", "beneficiary_account_ref": "", "beneficiary_channel": "63902", "beneficiary_phone_number": "************", "end_date": "2025-09-25T08:00:00Z", "balance": 0, "available_bal": 0, "limit": 100, "status": 0, "referer_merchant_code": null, "phone_number": "************", "kitty_type": 0, "kitty_type_desc": "contributions", "UserID": 1508, "user": null, "settlement_type": 0, "media": null, "has_signatory_transaction": false, "settings": null, "categories": null}, "kitty_status": "Active", "kitt_beneficiary_channel": "Mpesa", "kitty_type": "contributions", "percentage": null, "has_signatory_transaction": false}, {"kitty": {"ID": 7621, "CreatedAt": "2025-09-06T21:16:14.836Z", "UpdatedAt": "2025-09-23T08:05:31.934Z", "title": "Kitty test 8", "description": "<p>Test</p>", "username": "", "beneficiary_account": "************", "beneficiary_account_ref": "", "beneficiary_channel": "63902", "beneficiary_phone_number": "************", "end_date": "2025-09-30T21:01:00Z", "balance": 0, "available_bal": 0, "limit": 10080, "status": 0, "referer_merchant_code": null, "phone_number": "************", "kitty_type": 0, "kitty_type_desc": "contributions", "UserID": 1508, "user": null, "settlement_type": 0, "media": null, "has_signatory_transaction": false, "settings": null, "categories": null}, "kitty_status": "Active", "kitt_beneficiary_channel": "Mpesa", "kitty_type": "contributions", "percentage": null, "has_signatory_transaction": false}, {"kitty": {"ID": 7620, "CreatedAt": "2025-09-06T21:05:39.897Z", "UpdatedAt": "2025-09-06T21:05:39.897Z", "title": "Kitty's test 7", "description": "<p>Testing</p>", "username": "", "beneficiary_account": "************", "beneficiary_account_ref": "", "beneficiary_channel": "63902", "beneficiary_phone_number": "************", "end_date": "2025-09-30T09:00:00Z", "balance": 0, "available_bal": 0, "limit": 100, "status": 0, "referer_merchant_code": null, "phone_number": "************", "kitty_type": 0, "kitty_type_desc": "contributions", "UserID": 1508, "user": null, "settlement_type": 0, "media": null, "has_signatory_transaction": false, "settings": null, "categories": null}, "kitty_status": "Active", "kitt_beneficiary_channel": "Mpesa", "kitty_type": "contributions", "percentage": null, "has_signatory_transaction": false}, {"kitty": {"ID": 7619, "CreatedAt": "2025-09-06T20:47:38.737Z", "UpdatedAt": "2025-09-06T22:47:03.08Z", "title": "<PERSON> check 6", "description": "<p>Checking after creating a kitty if it closes this page</p>", "username": "", "beneficiary_account": "************", "beneficiary_account_ref": "", "beneficiary_channel": "63902", "beneficiary_phone_number": "************", "end_date": "2025-09-30T03:03:00Z", "balance": 0, "available_bal": 0, "limit": 100000, "status": 0, "referer_merchant_code": null, "phone_number": "************", "kitty_type": 0, "kitty_type_desc": "contributions", "UserID": 1508, "user": null, "settlement_type": 0, "media": null, "has_signatory_transaction": false, "settings": null, "categories": null}, "kitty_status": "Active", "kitt_beneficiary_channel": "Mpesa", "kitty_type": "contributions", "percentage": null, "has_signatory_transaction": false}, {"kitty": {"ID": 7617, "CreatedAt": "2025-09-06T15:58:17.359Z", "UpdatedAt": "2025-09-06T15:58:17.359Z", "title": "kitty categories test 4", "description": "<p>Test</p>", "username": "", "beneficiary_account": "************", "beneficiary_account_ref": "", "beneficiary_channel": "63902", "beneficiary_phone_number": "************", "end_date": "2025-09-30T03:03:00Z", "balance": 0, "available_bal": 0, "limit": 120, "status": 0, "referer_merchant_code": null, "phone_number": "************", "kitty_type": 0, "kitty_type_desc": "contributions", "UserID": 1508, "user": null, "settlement_type": 0, "media": null, "has_signatory_transaction": false, "settings": null, "categories": null}, "kitty_status": "Active", "kitt_beneficiary_channel": "Mpesa", "kitty_type": "contributions", "percentage": null, "has_signatory_transaction": false}, {"kitty": {"ID": 7615, "CreatedAt": "2025-09-03T05:50:27.155Z", "UpdatedAt": "2025-09-06T14:38:08.475Z", "title": "kitty category test 2", "description": "<p>Kitty test 2</p>", "username": "", "beneficiary_account": "************", "beneficiary_account_ref": "", "beneficiary_channel": "63902", "beneficiary_phone_number": "************", "end_date": "2025-09-14T05:04:00Z", "balance": 0, "available_bal": 0, "limit": 100, "status": 0, "referer_merchant_code": null, "phone_number": "************", "kitty_type": 0, "kitty_type_desc": "contributions", "UserID": 1508, "user": null, "settlement_type": 0, "media": null, "has_signatory_transaction": false, "settings": null, "categories": null}, "kitty_status": "Active", "kitt_beneficiary_channel": "Mpesa", "kitty_type": "contributions", "percentage": null, "has_signatory_transaction": false}, {"kitty": {"ID": 7614, "CreatedAt": "2025-09-03T05:40:11.794Z", "UpdatedAt": "2025-09-03T05:40:11.794Z", "title": "kitty category test", "description": "<p>Testing category </p>", "username": "", "beneficiary_account": "************", "beneficiary_account_ref": "", "beneficiary_channel": "63902", "beneficiary_phone_number": "************", "end_date": "2025-09-30T05:03:00Z", "balance": 0, "available_bal": 0, "limit": 100, "status": 0, "referer_merchant_code": null, "phone_number": "************", "kitty_type": 0, "kitty_type_desc": "contributions", "UserID": 1508, "user": null, "settlement_type": 0, "media": null, "has_signatory_transaction": false, "settings": null, "categories": null}, "kitty_status": "Active", "kitt_beneficiary_channel": "Mpesa", "kitty_type": "contributions", "percentage": null, "has_signatory_transaction": false}, {"kitty": {"ID": 5260, "CreatedAt": "2025-03-07T23:34:16.747Z", "UpdatedAt": "2025-09-29T11:18:40.035Z", "title": "<PERSON>'s medical", "description": "<p><PERSON>&#39;s medical bill</p>", "username": "", "beneficiary_account": "************", "beneficiary_account_ref": "", "beneficiary_channel": "63902", "beneficiary_phone_number": "************", "end_date": "2025-03-17T23:03:00Z", "balance": 0, "available_bal": 0, "limit": null, "status": 0, "referer_merchant_code": null, "phone_number": "************", "kitty_type": 0, "kitty_type_desc": "contributions", "UserID": 1508, "user": null, "settlement_type": 0, "media": null, "has_signatory_transaction": false, "settings": null, "categories": null}, "kitty_status": "Active", "kitt_beneficiary_channel": "Mpesa", "kitty_type": "contributions", "percentage": null, "has_signatory_transaction": false}, {"kitty": {"ID": 5259, "CreatedAt": "2025-03-07T23:23:36.648Z", "UpdatedAt": "2025-09-06T05:53:36.433Z", "title": "band for band", "description": "<p>band for band</p>", "username": "", "beneficiary_account": "************", "beneficiary_account_ref": "", "beneficiary_channel": "63902", "beneficiary_phone_number": "************", "end_date": "2025-09-30T03:03:00Z", "balance": 15.5, "available_bal": 0, "limit": 1000, "status": 0, "referer_merchant_code": null, "phone_number": "************", "kitty_type": 0, "kitty_type_desc": "contributions", "UserID": 1508, "user": null, "settlement_type": 0, "media": null, "has_signatory_transaction": true, "settings": null, "categories": null}, "kitty_status": "Active", "kitt_beneficiary_channel": "Mpesa", "kitty_type": "contributions", "volumes": 9, "percentage": 0.142, "has_signatory_transaction": true}, {"kitty": {"ID": 5258, "CreatedAt": "2025-03-07T23:01:28.375Z", "UpdatedAt": "2025-03-07T23:01:28.375Z", "title": "Nirvana rock band", "description": "Nirvana rock band", "username": "", "beneficiary_account": "************", "beneficiary_account_ref": "", "beneficiary_channel": "63902", "beneficiary_phone_number": "************", "end_date": "2025-04-29T23:00:00Z", "balance": 0, "available_bal": 0, "limit": null, "status": 0, "referer_merchant_code": null, "phone_number": "************", "kitty_type": 0, "kitty_type_desc": "contributions", "UserID": 1508, "user": null, "settlement_type": 0, "media": null, "has_signatory_transaction": false, "settings": null, "categories": null}, "kitty_status": "Active", "kitt_beneficiary_channel": "Mpesa", "kitty_type": "contributions", "percentage": null, "has_signatory_transaction": false}]}, "errors": null}