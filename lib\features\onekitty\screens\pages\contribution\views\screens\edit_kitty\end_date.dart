import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty_admin/features/onekitty/controllers/contribute_controller.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/controllers/kitty_controller.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/controllers/user_ktty_controller.dart';
import 'package:onekitty_admin/helpers/colors.dart';
import 'package:onekitty_admin/helpers/extensions/text_styles.dart';
import 'package:onekitty_admin/helpers/show_toast.dart'; 
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/widgets/date_picker.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/widgets/row_widget.dart';
import 'package:onekitty_admin/utils/common_strings.dart';
import 'package:onekitty_admin/utils/custom_button.dart';
import 'package:onekitty_admin/utils/datetime/combined_datetime.dart';
import 'package:onekitty_admin/utils/size_config.dart';
import 'package:intl/intl.dart';

class EndDate extends StatefulWidget {
  const EndDate({super.key});

  @override
  State<EndDate> createState() => _EndDateState();
}

class _EndDateState extends State<EndDate> {
  TextEditingController dateController = TextEditingController();
  TextEditingController timeController = TextEditingController();
  KittyController kittyController = Get.put(KittyController());
  DataController datacontroller = Get.put(DataController());
  ContributeController contributeController = Get.put(ContributeController());
  final _formKey = GlobalKey<FormState>();
  DateTime? endDate;
  int? kittyId;
  bool isLoading = false;

  void setValues() {
    final kittyEndDate = datacontroller.kitty.value.kitty?.endDate;
    if (kittyEndDate != null) {
      endDate = kittyEndDate;
      dateController.text = DateFormat('yyyy-MM-dd').format(kittyEndDate);
      timeController.text = DateFormat('HH:mm').format(kittyEndDate);
    }
    kittyId = datacontroller.kitty.value.kitty?.iD ?? 0;
  }

  @override
  void initState() {
    super.initState();
    setValues();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Container(
        margin: const EdgeInsets.all(20),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              Text(
                "edit_kitty_end_date".tr,
                style: context.headlineMedium
                    ?.copyWith(fontWeight: FontWeight.bold),
              ),
              const SizedBox(
                height: 30,
              ),
              SingleLineRow(
                text: "expected_contribution_end_date_info".tr,
                popup: KtStrings.endDateInfo,
              ),
              Obx(() {
                final kittyEndDate = datacontroller.kitty.value.kitty?.endDate;
                if (kittyEndDate != null) {
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    if (dateController.text != DateFormat('yyyy-MM-dd').format(kittyEndDate)) {
                      dateController.text = DateFormat('yyyy-MM-dd').format(kittyEndDate);
                      timeController.text = DateFormat('HH:mm').format(kittyEndDate);
                      endDate = kittyEndDate;
                    }
                  });
                }
                return DatePicker(
                  date: dateController,
                  time: timeController,
                  isAllow: true,
                );
              }),
              const SizedBox(
                height: 20,
              ),
              SizedBox(
                width: SizeConfig.screenWidth,
                height: 50,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    OutlinedButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 12.0),
                        child: Text(
                          "back_label".tr,
                          style: context.dividerTextLarge
                              ?.copyWith(color: AppColors.primary),
                        ),
                      ),
                    ),
                    Obx(() => CustomKtButton(
                          width: 140.w,
                          btnText: "update_label".tr,
                          isLoading:
                              kittyController.isEditEndDateloading.isTrue,
                          onPress: () async {
                            if (_formKey.currentState!.validate()) {
                              final date = dateController.text;
                              final time = timeController.text;
                              if (dateController.text.trim().isEmpty ||
                                  timeController.text.trim().isEmpty) {
                                ToastUtils.showErrorToast(context,
                                    "kindly_pick_end_date".tr, "error_label".tr);
                              }

                              // Combine the date and time into a single DateTime object
                              final combinedDateTime =
                                  combineDateTime(date, time);

                              // Format the combined DateTime object according to the backend's expected format
                              final formattedDateTime =
                                  formatDateTime(combinedDateTime);
                              final dateTime =
                                  DateTime.tryParse(formattedDateTime);

                            
                              if (dateTime == null) {
                                ToastUtils.showErrorToast(context, "error_label".tr,
                                    "select_end_date_15_minutes".tr,
                                    autoDismiss: false);
                                return;
                              } else { 
                                var res = await kittyController.updateEndDate(
                                    newDate: dateTime.toUtc(),
                                    KittyId: kittyId!);
                                if (res) {
                                  var resp = await contributeController
                                      .getKitty(id: kittyId);
                                  if (resp) {
                                    var dataController =
                                        Get.put(DataController());
                                    dataController.kitty.value =
                                        contributeController.singleKitty.value;
                                  }
                                  ToastUtils.showSuccessToast(
                                      context,
                                      kittyController.apiMessage.string,
                                      "Success");
                                      Navigator.pop(context);
                                } else {
                                  ToastUtils.showErrorToast(
                                      context,
                                      kittyController.apiMessage.string,
                                      "Error");
                                }
                              }
                            } else {
                              ToastUtils.showErrorToast(
                                  context, "fill_all_values".tr, "error_label".tr);
                            }
                          },
                        ))
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
