import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty_admin/helpers/colors.dart';
import 'package:onekitty_admin/helpers/connectivity_wrapper.dart';
import 'package:onekitty_admin/helpers/show_toast.dart';
import 'package:onekitty_admin/models/events/verify_ticket.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/controllers/verify_ticket_controller.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/views/screens/qrcode_verifu.dart';
import 'package:onekitty_admin/utils/date_formatter.dart';
import 'package:onekitty_admin/utils/formatted_currency.dart';
import 'package:onekitty_admin/utils/my_button.dart';
import 'package:onekitty_admin/utils/utils_exports.dart';

class VerifyTicket extends StatelessWidget {
  final int eventId;
  const VerifyTicket({super.key, required this.eventId});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(VerifyTicketController());
    final ticketCodeController = TextEditingController();

    return ConnectivityCheck(
        child: Scaffold(
      appBar: AppBar(
        title: Text('verify_ticket'.tr),
      ),
      body: Obx(
        () => PageView(
          controller: controller.pageController.value,
          children: [
            Center(
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Spacer(),
                    Text(
                      'enter_ticket_code'.tr,
                      style: const TextStyle(
                          fontSize: 24, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 20),
                    Obx(
                      () {
                        ticketCodeController.text = controller.prefix.value;
                        return TextField(
                          controller: ticketCodeController,
                          textCapitalization: TextCapitalization.characters,
                          onChanged: (value) {
                            controller.prefix.value = value.toUpperCase();
                          },
                          decoration: InputDecoration(
                            suffixIcon: IconButton(
                                onPressed: () {
                                  controller.prefix.value = "";
                                  ticketCodeController.clear();
                                },
                                icon: const Icon(Icons.clear)),
                            prefixIcon: const Icon(Icons.confirmation_number),
                            hintText: 'enter_code_here'.tr,
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(10),
                              borderSide: const BorderSide(
                                  color: Colors.blue, width: 2),
                            ),
                          ),
                        );
                      },
                    ),
                    const SizedBox(height: 20),
                    Obx(() => MyButton(
                          showLoading: controller.isVerifying.value,
                          onClick:  () {
                            if(ticketCodeController.text.trim().isEmpty){
                              ToastUtils.showErrorToast(context, 'Error', 'Not a valid ticket code');
                            }
                             
                                  // FocusScope.of(context).unfocus();
                                  controller.verifyTicket(
                                    eventId,
                                    ticketCodeController.text.trim(),
                                  );
                                },
                          label: 'verify'.tr,
                        )),
                    const Spacer(),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        GestureDetector(
                            onTap: () {
                              if (controller.pageController.value.page == 0) {
                                controller.pageController.value.jumpToPage(1);
                              } else {
                                controller.pageController.value.jumpToPage(0);
                              }
                            },
                            child: Container(
                                margin: EdgeInsets.only(bottom: 10.h),
                                
                                alignment: Alignment.centerRight,
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 12, vertical: 8),
                                  decoration: BoxDecoration(
                                    color: Colors.blue.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(20),
                                    border: Border.all(
                                        color: Colors.blue.withOpacity(0.3)),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      const Icon(Icons.qr_code_scanner,
                                          color: Colors.blue),
                                      const SizedBox(width: 4),
                                      Text(

                                        'swipe_to_scan'.tr,
                                       overflow: TextOverflow.ellipsis,
                                       maxLines: 1,
                                        style: const TextStyle(
                                            color: Colors.blue,
                                            fontWeight: FontWeight.w500),
                                      )
                                    ],
                                  ),
                                ))),
                      ],
                    )
                  ],
                ),
              ),
            ),
            QrVerifyTicket(eventId: eventId),
          ],
        ),
      ),
    ));
  }
}

class VerifyConfirm extends StatelessWidget {
  final int eventId;
  final VerifyTicketConfirm verify;
  final String code;
  const VerifyConfirm(
      {super.key,
      required this.verify,
      required this.eventId,
      required this.code});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(VerifyTicketController());
    final tickets = verify.transaction?.transactionTicket ?? [];
    final selectedTickets = tickets.map((e) => e.id).toList().obs;

    return Scaffold(
      appBar: AppBar(title: Text('verify_ticket_confirm'.tr)),
      body: Column(
        children: [
          Container(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(Icons.person, size: 20),
                    const SizedBox(width: 12),
                    Text(
                      "${verify.transaction?.firstName} ${verify.transaction?.secondName}",
                      style: const TextStyle(
                          fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    const Icon(Icons.phone, size: 18),
                    const SizedBox(width: 12),
                    Text(
                      verify.transaction?.phoneNumber ?? '',
                      style: const TextStyle(fontSize: 16),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    const Icon(Icons.receipt, size: 18),
                    const SizedBox(width: 12),
                    Text(
                      verify.transaction?.transactionCode ?? '',
                      style: const TextStyle(
                          fontSize: 16, fontWeight: FontWeight.w500),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    const Icon(Icons.access_time, size: 18),
                    const SizedBox(width: 12),
                    Text(
                      '${formatDate(verify.transaction?.createdAt?.toLocal().toString() ?? '')}, ${DateFormat('hh:mm a').format(verify.transaction?.createdAt?.toLocal() ?? DateTime.now())}',
                      style: const TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: tickets.length,
              itemBuilder: (context, index) {
                final ticket = verify.transaction?.transactionTicket?[index];
                final RxBool selected = true.obs;
                return GestureDetector(
                  onTap: () {
                    HapticFeedback.lightImpact();
                    selected.value = !selected.value;
                    if (selected.value) {
                      if (!selectedTickets.contains(ticket?.id ?? 0)) {
                        selectedTickets.add(ticket?.id ?? 0);
                      }
                    } else {
                      selectedTickets.remove(ticket?.id ?? 0);
                    }
                  },
                  child: Obx(
                    () => Container(
                      margin: const EdgeInsets.only(bottom: 12),
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        border: Border.all(
                          color: selected.value
                              ? AppColors.primary
                              : Colors.grey.shade300,
                          width: selected.value ? 2 : 1,
                        ),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Text(
                                  ticket?.ticket?.title ?? "",
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              if (selected.value)
                                const Icon(
                                  Icons.check_circle,
                                  size: 20,
                                  color: AppColors.primary,
                                ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            ticket?.ticket?.description ?? "",
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey.shade600,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 12),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 6),
                                decoration: BoxDecoration(
                                  color: Colors.grey.shade100,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Text(
                                  'Qty: ${ticket?.quantity}',
                                  style: const TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                              Text(
                                FormattedCurrency.getFormattedCurrency(
                                    ticket?.amount),
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          Container(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Obx(() => Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Selected Tickets:',
                          style: TextStyle(
                              fontSize: 16, fontWeight: FontWeight.w500),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: Colors.grey.shade100,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            '${selectedTickets.length}',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    MyButton(
                      showLoading: controller.isConfirming.value,
                      onClick: selectedTickets.isEmpty
                          ? null
                          : () {
                              controller.verifyTicketConfirm(
                                  eventId, code, selectedTickets);
                            },
                      label: 'verify_ticket_button'.tr,
                      width: double.infinity,
                    ),
                  ],
                )),
          ),
        ],
      ),
    );
  }
}
