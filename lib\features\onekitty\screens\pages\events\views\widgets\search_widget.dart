import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/controllers/events_controller.dart';
import 'package:onekitty_admin/helpers/colors.dart';
import 'package:onekitty_admin/helpers/show_toast.dart';
import '../../controllers/controllers.dart';
import '../../controllers/edit_event_controller.dart';
import '../../../../../../../../models/events/categories_model.dart';

class SearchBarWidget extends StatelessWidget {
  final int page;
  const SearchBarWidget({
    super.key,
    required this.page,
  });

  @override
  Widget build(BuildContext context) {
    RxBool isSearching = false.obs;

    final _controller = Get.put(Eventcontroller());
    Timer? _debounce;
    return Builder(builder: (context) {
      final TextEditingController textController =
          TextEditingController(text: _controller.search.value);
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Obx(() => Container(
                  height: 45.h,
                  margin: EdgeInsets.all(4.spMin),
                  alignment: Alignment.bottomCenter,
                  decoration:
                      BoxDecoration(borderRadius: BorderRadius.circular(15)),
                  child: TextField(
                    controller: textController,
                    style: TextStyle(fontSize: 12.spMin),
                    decoration: const InputDecoration(hint: Text('Search ')),
                    onChanged: (val) {
                      // Cancel the previous timer if it's still active
                      if (_debounce?.isActive ?? false) {
                        _debounce!.cancel();
                      }

                      // Set a new timer to execute the function after 1 second of inactivity
                      _debounce = Timer(const Duration(seconds: 1), () async {
                        // Trigger the searching state
                        isSearching(true);

                        // Perform the search operation
                        _controller.search(textController.text);

                        // Fetch events or user events based on the page value
                        if (page == 0) {
                          _controller.resetEvents();
                          await _controller.fetchEvents().whenComplete(() {
                            isSearching(false);
                          }).onError((e, s) => isSearching(false));
                        } else {
                          _controller.resetUserEvents();
                          await _controller.fetchUserEvents().whenComplete(() {
                            isSearching(false);
                          }).onError((e, s) => isSearching(false));
                        }
                      });
                    },
                  ),
                )),
          ),
          SizedBox(width: 12.w),
          OutlinedButton.icon(
            onPressed: () async {
              showModalBottomSheet<Map<String, dynamic>>(
                  context: context,
                  isScrollControlled: true,
                  builder: (BuildContext context) => FilterDialog(page: page));
            },
            label: Text(
              'filter'.tr,
              style: TextStyle(fontWeight: FontWeight.w400, fontSize: 16.spMin),
            ),
            icon: const Icon(Icons.filter_alt_outlined),
          )
        ],
      );
    });
  }
}

class FilterDialog extends StatelessWidget {
  final int page;
  const FilterDialog({super.key, required this.page});

  @override
  Widget build(BuildContext context) {
    final Eventcontroller _controller = Get.find();

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      padding: const EdgeInsets.fromLTRB(20.0, 8.0, 20.0, 20.0),
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Bottom sheet handle
            Center(
              child: Container(
                width: 40.w,
                height: 4.h,
                margin: EdgeInsets.only(bottom: 16.h),
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(2.r),
                ),
              ),
            ),
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    const Icon(Icons.filter_list,
                        color: AppColors.primary, size: 20),
                    SizedBox(width: 8.w),
                    Text(
                      'Filter Kitties',
                      style: TextStyle(
                        fontSize: 18.spMin,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close, size: 20),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                ),
              ],
            ),
            SizedBox(height: 4.h),
            Text(
              'Refine your search results',
              style: TextStyle(
                fontSize: 12.spMin,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 24.h),

            // Status Section
            if (page == 1) ...[
              Text(
                'Status',
                style: TextStyle(
                  fontSize: 14.spMin,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
              SizedBox(height: 8.h),
              Obx(() {
                return Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: DropdownButtonFormField<String>(
                    decoration: InputDecoration(
                      hintText: 'Select Status',
                      hintStyle: TextStyle(color: Colors.grey.shade500),
                      prefixIcon: Icon(Icons.radio_button_checked,
                          color: Colors.grey.shade400, size: 20),
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 12.w,
                        vertical: 12.h,
                      ),
                    ),
                    value: _controller.selectedStatus.value,
                    items: Get.find<GlobalControllers>()
                        .enums
                        .value
                        .eventStatus
                        .map((status) {
                      return DropdownMenuItem<String>(
                        value: status,
                        child: Text(status),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        _controller.selectedStatus.value = value;
                      }
                    },
                  ),
                );
              }),
              SizedBox(height: 16.h),
            ],

            // Category Section
            Text(
              'Category',
              style: TextStyle(
                fontSize: 14.spMin,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
            SizedBox(height: 8.h),
            GetX<EditEventController>(
              initState: (state) async {
                await state.controller?.getCategories();
              },
              builder: (controller) {
                if (controller.isLoadingCategories.isTrue) {
                  return Container(
                    height: 50.h,
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    width: double.infinity,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.category,
                                color: Colors.grey.shade400, size: 20),
                            SizedBox(width: 8.w),
                            Text(
                              'All Categories',
                              style: TextStyle(color: Colors.grey.shade500),
                            ),
                          ],
                        ),
                        const CupertinoActivityIndicator()
                      ],
                    ),
                  );
                }

                return Obx(() => Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      child: DropdownButtonFormField<CategoriesModel>(
                        decoration: InputDecoration(
                          hintText: 'All Categories',
                          hintStyle: TextStyle(color: Colors.grey.shade500),
                          prefixIcon: Icon(Icons.category,
                              color: Colors.grey.shade400, size: 20),
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 12.w,
                            vertical: 12.h,
                          ),
                        ),
                        value: _controller.selectedCategory.value,
                        items: controller.categories.map((category) {
                          return DropdownMenuItem<CategoriesModel>(
                            value: category,
                            child: Text(
                              category.title ?? '',
                              overflow: TextOverflow.ellipsis,
                            ),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            _controller.selectedCategory.value = value;
                            controller.category.value = value.id!;
                          }
                        },
                      ),
                    ));
              },
            ),
            SizedBox(height: 16.h),

            // Date Range Section
            Text(
              'Date Range',
              style: TextStyle(
                fontSize: 14.spMin,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
            SizedBox(height: 8.h),

            // Start Date
            Text(
              'Start Date',
              style: TextStyle(
                fontSize: 12.spMin,
                color: Colors.grey.shade600,
              ),
            ),
            SizedBox(height: 4.h),
            GestureDetector(
              onTap: () async {
                DateTime? picked = await showDatePicker(
                  context: context,
                  initialDate: DateTime.now(),
                  firstDate: DateTime(2000),
                  lastDate: DateTime(2101),
                );
                if (picked != null) {
                  _controller.startDate.value = picked.toString().split(' ')[0];
                }
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 14.h),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Row(
                  children: [
                    Icon(Icons.calendar_today,
                        color: Colors.grey.shade400, size: 18),
                    SizedBox(width: 8.w),
                    Obx(() => Text(
                          _controller.startDate.value.isNotEmpty
                              ? DateFormat('yyyy-MM-dd').format(
                                  DateTime.parse(_controller.startDate.value))
                              : 'Select date',
                          style: TextStyle(
                            fontSize: 14.spMin,
                            color: _controller.startDate.value.isNotEmpty
                                ? Colors.black87
                                : Colors.grey.shade500,
                          ),
                        )),
                  ],
                ),
              ),
            ),
            SizedBox(height: 12.h),

            // End Date
            Text(
              'End Date',
              style: TextStyle(
                fontSize: 12.spMin,
                color: Colors.grey.shade600,
              ),
            ),
            SizedBox(height: 4.h),
            GestureDetector(
              onTap: () async {
                DateTime? picked = await showDatePicker(
                  context: context,
                  initialDate: DateTime.now(),
                  firstDate: DateTime(2000),
                  lastDate: DateTime(2101),
                );
                if (picked != null) {
                  _controller.endDate.value = picked.toString().split(' ')[0];
                }
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 14.h),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Row(
                  children: [
                    Icon(Icons.calendar_today,
                        color: Colors.grey.shade400, size: 18),
                    SizedBox(width: 8.w),
                    Obx(() => Text(
                          _controller.endDate.value.isNotEmpty
                              ? DateFormat('yyyy-MM-dd').format(
                                  DateTime.parse(_controller.endDate.value))
                              : 'Select date',
                          style: TextStyle(
                            fontSize: 14.spMin,
                            color: _controller.endDate.value.isNotEmpty
                                ? Colors.black87
                                : Colors.grey.shade500,
                          ),
                        )),
                  ],
                ),
              ),
            ),
            SizedBox(height: 24.h),

            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {
                      _controller.startDate.value = '';
                      _controller.endDate.value = '';
                      _controller.selectedCategory.value = null;
                      _controller.selectedStatus.value = "ACTIVE";
                    },
                    style: OutlinedButton.styleFrom(
                      padding: EdgeInsets.symmetric(vertical: 14.h),
                      side: BorderSide(color: Colors.grey.shade300),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.clear_all,
                            size: 18, color: Colors.grey.shade700),
                        SizedBox(width: 4.w),
                        Text(
                          'Clear All',
                          style: TextStyle(
                            color: Colors.grey.shade700,
                            fontSize: 14.spMin,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  flex: 2,
                  child: Obx(
                    () => ElevatedButton(
                      onPressed: () async {
                        try {
                          _controller.isApplyingFilters(true);
                          
                          // Set filter parameters
                          _controller.filterCategory.value = _controller.selectedCategory.value?.id;
                          _controller.startDate(
                              _controller.startDate.value.isNotEmpty
                                  ? DateTime.parse(_controller.startDate.value)
                                      .toUtc()
                                      .toIso8601String()
                                  : "");
                          _controller.endDate(_controller.endDate.value.isNotEmpty
                              ? DateTime.parse(_controller.endDate.value)
                                  .toUtc()
                                  .toIso8601String()
                              : "");
                          
                          // Reset appropriate events list and fetch
                          if (page == 0) {
                            // Page 0 = All Events
                            _controller.resetEvents();
                            _controller.status("ACTIVE");
                            await _controller.fetchEvents();
                          } else {
                            // Page 1 = User Events
                            _controller.resetUserEvents();
                            _controller.status(_controller.selectedStatus.value);
                            await _controller.fetchUserEvents();
                          }
                          
                          Navigator.pop(context);
                          ToastUtils.showSuccessToast(context, 'Success', 'Filters applied successfully');
                        } catch (e) {
                          ToastUtils.showErrorToast(context, 'Error', 'Failed to apply filters: ${e.toString()}');
                        } finally {
                          _controller.isApplyingFilters(false);
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        padding: EdgeInsets.symmetric(vertical: 14.h),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                      ),
                      child: _controller.isApplyingFilters.value
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : Text(
                              'Apply Filters',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 14.spMin,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
