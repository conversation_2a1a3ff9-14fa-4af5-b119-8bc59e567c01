import 'dart:async';
import 'dart:math';

import 'package:flutter_contacts/contact.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:flutter_contacts/properties/phone.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:onekitty_admin/configs/country_specifics.dart';
import 'package:onekitty_admin/features/onekitty/widgets/custom_international_phone_input.dart';
import 'package:onekitty_admin/features/onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty_admin/helpers/colors.dart';
import 'package:onekitty_admin/helpers/show_toast.dart';
import 'package:onekitty_admin/models/chama/chamaDto.dart';

import 'package:onekitty_admin/features/onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty_admin/utils/utils_exports.dart';
import 'package:onekitty_admin/features/onekitty/widgets/getx_contact_picker.dart';
import 'package:onekitty_admin/features/onekitty/controllers/contact_picker_controller.dart';

class Invite extends StatefulWidget {
  const Invite({super.key});

  @override
  _InviteState createState() => _InviteState();
}

class _InviteState extends State<Invite> {
  final ChamaDataController dataController = Get.put(ChamaDataController());
  final ChamaController chamaController = Get.put(ChamaController());
  TextEditingController phoneController = TextEditingController();
  TextEditingController firstNameController = TextEditingController();
  TextEditingController lastNameController = TextEditingController();
  PhoneNumber num = CountryConfig.phoneNumber;
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  List<String> dropdownItems = [];

  final _streamController = StreamController<void>();
  Stream<void> get _stream => _streamController.stream;

  void startTimer() {
    Timer.periodic(const Duration(seconds: 1), (timer) {
      _streamController.add(null);
    });
  }

  void cancelTimer() {
    _streamController.close();
  }

  String invitePhone = "";

  final ContactPickerController contactController = Get.put(ContactPickerController());
  String selectedvalue = "MEMBER";
  @override
  void initState() {
    dropdownItems = chamaController.roles.map((role) => role.role).toList();

    startTimer();
    super.initState();
  }

  @override
  void dispose() {
    contactController.clearSelection();
    super.dispose();
  }



  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        //appBar: buildAppBar(context),
        body: SingleChildScrollView(
          child: Container(
            width: double.maxFinite,
            padding: EdgeInsets.all(10.w),
            child: Column(
              children: [
                const RowAppBar(),
                Text(dataController.chama.value.chama?.title ?? "",
                    style: CustomTextStyles.titleMediumBlack900),
                // Text("${chamaDataController.chama.value.membersCount} Members",
                // style: theme.textTheme.titleLarge),
                GetBuilder(
                  builder: (ChamaController chamaController) {
                    if (chamaController.isloadingChama.isTrue) {
                      return Text('checking'.tr);
                    }

                    return Text(
                        "${chamaController.chamaMembers.length} ${'members_count'.tr}",
                        style: theme.textTheme.titleLarge);
                  },
                ),
                SizedBox(height: 20.h),
                Text('add_chama_members'.tr, style: theme.textTheme.titleLarge),
                SizedBox(height: 10.h), 
                _inputPhoneNumber(context),

                SizedBox(height: 20.h),

                StreamBuilder<void>(
                  stream: _stream,
                  builder: (context, snapshot) {
                    return buildInviteContacts(context);
                  },
                ),
              ],
            ),
          ),
        ),
        floatingActionButton: Obx(
          () => CustomKtButton(
            isLoading: chamaController.isAdding.isTrue,
            width: 180.w,
            height: 50.h,
            btnText: 'add_members'.tr,
            onPress: () async {
              final selected = contactController.selectedContacts;
              try {
                final List<Member> members = [];
                for (var index = 0; index < selected.length; index++) {
                  final contact = selected[index];
                  final phoneNumber = contact.phones.first.normalizedNumber;

                  final order = chamaController.chamaMembers
                      .map((member) => member.receivingOrder ?? 0)
                      .reduce(max);
                  final member = Member(
                    phoneNumber: phoneNumber,
                    firstName: contact.name.first,
                    secondName: contact.name.last,
                    role: contact.name.prefix,
                    receivingOrder: order + index + 2,
                    status: "ACTIVE",
                  );
                  members.add(member);
                }

                final chamaMembers = MembersDto(
                    chamaId: dataController.singleChamaDts.value.id ?? 0,
                    members: members);

                final resp =
                    await chamaController.addMember(memebersDto: chamaMembers);
                if (resp) {
                  ToastUtils.showSuccessToast(
                    context,
                    chamaController.apiMessage.string,
                    'success'.tr,
                  );
                  contactController.clearSelection();
                  chamaController.getChamaMembers(
                      chamaId: dataController.singleChamaDts.value.id ?? 0,
                      sort: "LEADERS");
                  Get.back();
                } else {
                  ToastUtils.showErrorToast(
                    context,
                    chamaController.apiMessage.string,
                    'error'.tr,
                  );
                }
              } catch (e) {
                // Handle error gracefully, show error toast, log error, etc.
                ToastUtils.showErrorToast(
                  context,
                  'an_error_occurred_adding_member'.tr,
                  'error'.tr,
                );
              }
            },
            alignment: Alignment.bottomRight,
          ),
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
      ),
    );
  }

  Widget _inputPhoneNumber(BuildContext context) {
    return Form(
      key: formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          CustomInternationalPhoneInput(

              suffix: InkWell(
                onTap: () async {
                  final List<Contact>? selectedContacts = await showDialog<List<Contact>>(
                    context: context,
                    builder: (context) =>   Dialog(
                      child: GetXContactPicker(
                        mode: ContactPickerMode.multiple,
                        title: 'select_contacts'.tr,
                      ),
                    ),
                  );

                  // Handle the returned result when dialog is popped
                  if (selectedContacts != null && selectedContacts.isNotEmpty) {
                    for (var contact in selectedContacts) {
                      selectContact(contact, context);
                    }
                    // Refresh the UI to show newly selected contacts
                    setState(() {});
                  }
                },
                child: CustomImageView(
                  imagePath: AssetUrl.addrbook,
                  height: 20.h,
                ),
              ),
            
            onInputChanged: (num) {
              setState(() {
                invitePhone = num.phoneNumber!;
              });
            },
          ),
          SizedBox(
            height: 8.h,
          ),
          CustomElevatedButton(
              leftIcon: Container(
                margin: EdgeInsets.only(right: 1.w),
                child: CustomImageView(
                    imagePath: AssetUrl.imgPlus, height: 15.h, width: 15.w),
              ),
              onPressed: () {
                if (formKey.currentState!.validate()) {
                  String phoneNumber = invitePhone;

                  Phone phone =
                      Phone(phoneNumber, normalizedNumber: phoneNumber);

                  Contact contact = Contact(
                    phones: [phone],
                  );
                  selectContact(contact, context);

                  setState(() {
                    phoneController.clear();
                  });
                }
              },
              height: 45.h,
              width: 100.w,
              text: 'add'.tr,
              buttonStyle: CustomButtonStyles.fillIndigR,
              buttonTextStyle: CustomTextStyles.titleSmallWhiteA700),
        ],
      ),
    );
  }

  Widget buildInviteContacts(BuildContext context) {
    startTimer();
    // Check if selected contacts list is empty
    return Obx(() {
      if (contactController.selectedContacts.isEmpty) {
      return CustomImageView(
        imagePath: AssetUrl.imgGroup13,
        height: 150.h,
        width: 254.w,
      );
    } else {
      return Container(
        height: 400.h,
        margin: EdgeInsets.only(left: 2.h),
        padding: EdgeInsets.symmetric(horizontal: 2.h, vertical: 17.h),
        decoration: AppDecoration.outlineGray
            .copyWith(borderRadius: BorderRadiusStyle.roundedBorder8),
        child: Column(
          children: [
            Expanded(
              child: ListView.builder(
                itemCount: contactController.selectedContacts.length,
                itemBuilder: (context, index) {
                  final selectedContact = contactController.selectedContacts[index];

                  return Container(
                    decoration: BoxDecoration(
                        borderRadius: BorderRadiusStyle.roundedBorder8),
                    child: Column(
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CustomImageView(
                              imagePath: AssetUrl.dotSix,
                              height: 25.h,
                              width: 25.w,
                              margin: EdgeInsets.only(right: 3.h),
                            ),
                            Opacity(
                              opacity: 0.5,
                              child: Padding(
                                padding: EdgeInsets.only(top: 6.h, bottom: 8.h),
                                child: Text(
                                  "${index + 1}",
                                  style: theme.textTheme.titleSmall!.copyWith( 
                                  ),
                                ),
                              ),
                            ),
                            CustomImageView(
                              imagePath: AssetUrl.imgPerson,
                              height: 25.h,
                              width: 25.w,
                              margin: EdgeInsets.only(left: 3.h),
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Padding(
                                  padding: EdgeInsets.only(
                                      left: 6.h, top: 1.h, bottom: 1.h),
                                  child: Text(
                                    "${selectedContact.name.first} ${selectedContact.name.last}",
                                    overflow: TextOverflow.ellipsis,
                                    style: CustomTextStyles.titleSmallGray90001 
                                  ),
                                ),
                                Padding(
                                  padding: EdgeInsets.only(
                                      left: 6.h, top: 1.h, bottom: 1.h),
                                  child: Text(
                                    selectedContact.phones.first.number,
                                    style: CustomTextStyles.titleSmallGray90001
                                        
                                  ),
                                ),
                              ],
                            ),
                            const Spacer(),
                            selectedContact.name.prefix == "CHAIRPERSON"
                                ? CustomImageView(
                                    imagePath: AssetUrl.crownsv,
                                    height: 18.h,
                                    width: 18.w,
                                    margin: EdgeInsets.symmetric(vertical: 9.h),
                                  )
                                : const SizedBox.shrink(),
                            Padding(
                              padding: EdgeInsets.symmetric(
                                  vertical: 10.h, horizontal: 2.h),
                              child: Text(selectedContact.name.prefix),
                            ),
                            InkWell(
                              onTap: () {
                                contactController.removeContact(selectedContact);
                              },
                              child: CustomImageView(
                                imagePath: AssetUrl.imgIconoirCancel,
                                height: 18.h,
                                width: 18.w,
                                margin: EdgeInsets.symmetric(
                                    vertical: 9.h, horizontal: 5.h),
                              ),
                            ),
                            IconButton(
                              icon: const Icon(
                                Icons.edit,
                                color: AppColors.blueButtonColor,
                              ),
                              padding: EdgeInsets.symmetric(vertical: 10.h),
                              onPressed: () {
                                _chamaOptionsDialog(
                                    context, index, selectedContact);
                              },
                            )
                          ],
                        ),
                        SizedBox(height: 2.h),
                        const Divider()
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      );
    }
    });
  }

  void selectContact(
    Contact selectedContact,
    BuildContext context,
  ) {
    contactController.selectContact(selectedContact);
  }

  void _chamaOptionsDialog(
      BuildContext context, int index, Contact selectedContact) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        // Local state for the selected status within the dialog

        firstNameController.text = selectedContact.name.first;
        lastNameController.text = selectedContact.name.last;
        selectedvalue = selectedContact.name.prefix;
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {
            return Align(
              alignment: Alignment.centerRight,
              child: AlertDialog(
                title: Text(
                  'update_member_details'.tr,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                content: SizedBox(
                  height: 200.h,
                  child: Column(
                    children: [
                      CustomTextField(
                        controller: firstNameController,
                        labelText: 'enter_first_name'.tr,
                      ),
                      CustomTextField(
                        controller: lastNameController,
                        labelText: 'enter_last_name'.tr,
                      ),
                      SizedBox(
                        height: 8.h,
                      ),
                      DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: 'select_role'.tr,
                          fillColor: Colors.blueAccent.withOpacity(0.1),
                        ),
                        isExpanded: true,
                        items: dropdownItems
                            .map(
                              (String item) => DropdownMenuItem<String>(
                                value: item,
                                child: Text(
                                  item,
                                  style: const TextStyle(
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                            )
                            .toList(),
                        value: selectedvalue,
                        onChanged: (String? value) {
                          setState(() {
                            selectedvalue = value!;
                          });
                        },
                      ),
                    ],
                  ),
                ),
                actions: [
                  CustomElevatedButton(
                    text: 'save'.tr,
                    onPressed: () {
                      try {
                        updateContact(
                          selectedContact.phones.first.normalizedNumber,
                          firstNameController.text,
                          lastNameController.text,
                          selectedvalue,
                        );
                      } catch (e) {}

                      Navigator.of(context).pop(); // Close the dialog
                    },
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  void updateContact(
    String id,
    String newFirstName,
    String newLastName,
    String role,
  ) {
    setState(() {
      final selectedContact = contactController.selectedContacts;

      //access and update the contact list
      final contact = selectedContact
          .firstWhere((contact) => contact.phones.first.normalizedNumber == id);
      contact.name.first = newFirstName;
      contact.name.last = newLastName;
      contact.name.prefix = role;
    });
  }
}
