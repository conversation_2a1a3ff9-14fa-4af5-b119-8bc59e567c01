import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onekitty_admin/features/admin/services/kitty_admin_service.dart';
import 'package:onekitty_admin/models/kitty_model.dart';
import 'package:pluto_grid/pluto_grid.dart';
import 'package:intl/intl.dart';

class KittyDashboardController extends GetxController {
  final KittyAdminService _kittyService = KittyAdminService();
  final Logger _logger = Logger();

  // Reactive variables
  final RxList<Kitty> kitties = <Kitty>[].obs;
  final RxBool isLoading = false.obs;
  final RxString apiMessage = ''.obs;
  final RxBool hasError = false.obs;

  // Pagination
  final RxInt currentPage = 0.obs;
  final RxInt pageSize = 15.obs;
  final RxInt totalPages = 1.obs;
  final RxInt totalItems = 0.obs;
  final RxBool hasNext = false.obs;
  final RxBool hasPrevious = false.obs;

  // Filters
  final RxString search = ''.obs;
  final RxString chamaId = ''.obs;
  final RxString frequency = ''.obs;
  final RxString kittyId = ''.obs;
  final RxString startDate = ''.obs;
  final RxString endDate = ''.obs;

  // PlutoGrid
  final RxList<PlutoRow> plutoRows = <PlutoRow>[].obs;
  final RxList<PlutoColumn> plutoColumns = <PlutoColumn>[].obs;

  Timer? _debounce;

  @override
  void onInit() {
    super.onInit();
    _initializeColumns();
    fetchKitties(0);
  }

  @override
  void onClose() {
    _debounce?.cancel();
    super.onClose();
  }

  void _initializeColumns() {
    plutoColumns.value = [
      PlutoColumn(
        title: 'ID',
        field: 'id',
        type: PlutoColumnType.text(),
        width: 80,
        enableSorting: true,
        enableColumnDrag: false,
        frozen: PlutoColumnFrozen.start,
      ),
      PlutoColumn(
        title: 'Created At',
        field: 'created_at',
        type: PlutoColumnType.text(),
        width: 120,
        enableSorting: true,
      ),
      PlutoColumn(
        title: 'Title',
        field: 'title',
        type: PlutoColumnType.text(),
        width: 150,
        enableSorting: true,
      ),
      PlutoColumn(
        title: 'Description',
        field: 'description',
        type: PlutoColumnType.text(),
        width: 200,
        enableSorting: false,
      ),
      PlutoColumn(
        title: 'Beneficiary Account',
        field: 'beneficiary_account',
        type: PlutoColumnType.text(),
        width: 150,
        enableSorting: false,
      ),
      PlutoColumn(
        title: 'Channel',
        field: 'beneficiary_channel',
        type: PlutoColumnType.text(),
        width: 150,
        enableSorting: false,
      ),
      PlutoColumn(
        title: 'Phone',
        field: 'beneficiary_phone',
        type: PlutoColumnType.text(),
        width: 130,
        enableSorting: false,
      ),
      PlutoColumn(
        title: 'End Date',
        field: 'end_date',
        type: PlutoColumnType.text(),
        width: 120,
        enableSorting: true,
      ),
      PlutoColumn(
        title: 'Balance',
        field: 'balance',
        type: PlutoColumnType.text(),
        width: 120,
        enableSorting: true,
      ),
      PlutoColumn(
        title: 'Phone Number',
        field: 'phone_number',
        type: PlutoColumnType.text(),
        width: 130,
        enableSorting: false,
      ),
    ];
  }

  Future<void> fetchKitties(int page) async {
    try {
      isLoading(true);
      hasError(false);
      
      final result = await _kittyService.fetchKitties(
        page: page,
        size: pageSize.value,
        search: search.value.isNotEmpty ? search.value : null,
        chamaId: chamaId.value.isNotEmpty ? chamaId.value : null,
        frequency: frequency.value.isNotEmpty ? frequency.value : null,
        kittyId: kittyId.value.isNotEmpty ? kittyId.value : null,
        startDate: startDate.value.isNotEmpty ? startDate.value : null,
        endDate: endDate.value.isNotEmpty ? endDate.value : null,
      );

      if (result['success']) {
        kitties.value = result['kitties'];
        _updatePlutoRows();
        
        final pagination = result['pagination'];
        currentPage.value = pagination['page'];
        totalPages.value = pagination['total_pages'];
        totalItems.value = pagination['total_items'];
        hasNext.value = pagination['has_next'];
        hasPrevious.value = pagination['has_previous'];
        
        apiMessage.value = result['message'];
      } else {
        hasError(true);
        apiMessage.value = result['message'];
        kitties.clear();
        plutoRows.clear();
      }
    } catch (e) {
      _logger.e('Error in fetchKitties: $e');
      hasError(true);
      apiMessage.value = 'An unexpected error occurred';
      kitties.clear();
      plutoRows.clear();
    } finally {
      isLoading(false);
    }
  }

  void _updatePlutoRows() {
    plutoRows.value = kitties.map((kitty) {
      return PlutoRow(cells: {
        'id': PlutoCell(value: kitty.iD?.toString() ?? '-'),
        'created_at': PlutoCell(
          value: kitty.createdAt != null
              ? DateFormat('dd/MM/yy HH:mm').format(kitty.createdAt!)
              : '-',
        ),
        'title': PlutoCell(value: kitty.title ?? '-'),
        'description': PlutoCell(value: kitty.description ?? '-'),
        'beneficiary_account': PlutoCell(value: kitty.beneficiaryAccount ?? '-'),
        'beneficiary_channel': PlutoCell(value: kitty.beneficiaryChannel ?? '-'),
        'beneficiary_phone': PlutoCell(value: kitty.beneficiaryPhoneNumber ?? '-'),
        'end_date': PlutoCell(
          value: kitty.endDate != null
              ? DateFormat('dd/MM/yy HH:mm').format(kitty.endDate!)
              : '-',
        ),
        'balance': PlutoCell(
          value: kitty.balance != null
              ? NumberFormat.currency(symbol: 'KES ').format(kitty.balance)
              : '-',
        ),
        'phone_number': PlutoCell(value: kitty.phoneNumber ?? '-'),
      });
    }).toList();
  }

  void handleSearch(String value) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    
    _debounce = Timer(const Duration(seconds: 1), () {
      search.value = value;
      fetchKitties(0);
    });
  }

  void handlePageChange(int page) {
    fetchKitties(page);
  }

  void clearFilters() {
    search.value = '';
    chamaId.value = '';
    frequency.value = '';
    kittyId.value = '';
    startDate.value = '';
    endDate.value = '';
    fetchKitties(0);
  }

  void refreshData() {
    fetchKitties(currentPage.value);
  }

  Future<void> selectStartDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );

    if (picked != null) {
      startDate.value = picked.toUtc().toIso8601String();
      fetchKitties(0);
    }
  }

  Future<void> selectEndDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null) {
      endDate.value = picked.toUtc().toIso8601String();
      fetchKitties(0);
    }
  }

  void clearStartDate() {
    startDate.value = '';
    fetchKitties(0);
  }

  void clearEndDate() {
    endDate.value = '';
    fetchKitties(0);
  }

  void onKittyTapped(PlutoRow row) {
    final index = plutoRows.indexOf(row);
    if (index >= 0 && index < kitties.length) {
      final selectedKitty = kitties[index];
      _logger.i('Kitty tapped: ${selectedKitty.title}');
      
      // Navigate to single kitty view
      // You can implement navigation logic here
      Get.snackbar(
        'Kitty Selected',
        'Selected: ${selectedKitty.title}',
        snackPosition: SnackPosition.bottom,
      );
    }
  }

  Future<void> exportData(String format) async {
    try {
      isLoading(true);
      
      final filters = <String, dynamic>{
        'search': search.value,
        'chama_id': chamaId.value,
        'frequency': frequency.value,
        'kitty_id': kittyId.value,
        'start_date': startDate.value,
        'end_date': endDate.value,
      };

      final result = await _kittyService.exportKittiesData(
        format: format,
        filters: filters,
      );

      if (result['success']) {
        Get.snackbar(
          'Export Successful',
          'Data exported successfully. Download will start shortly.',
          snackPosition: SnackPosition.bottom,
        );
        // Handle download URL if provided
        if (result['download_url'] != null) {
          // Implement download logic
        }
      } else {
        Get.snackbar(
          'Export Failed',
          result['message'],
          snackPosition: SnackPosition.bottom,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      _logger.e('Error exporting data: $e');
      Get.snackbar(
        'Export Error',
        'An error occurred while exporting data',
        snackPosition: SnackPosition.bottom,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading(false);
    }
  }
}
