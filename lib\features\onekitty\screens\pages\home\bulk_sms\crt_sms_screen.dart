import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:excel/excel.dart' as ex;
import 'package:csv/csv.dart';
import 'package:file_picker/file_picker.dart';
import 'package:fl_country_code_picker/fl_country_code_picker.dart';
// import 'package:flutter_animated_dialog_updated/flutter_animated_dialog.dart';
import 'package:flutter_animated_dialog_updated/flutter_animated_dialog.dart';
import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:onekitty_admin/configs/country_specifics.dart';

import 'package:onekitty_admin/helpers/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:onekitty_admin/features/onekitty/controllers/bulksms_controller.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/controllers/user_ktty_controller.dart';
import 'package:onekitty_admin/helpers/show_toast.dart';
import 'package:onekitty_admin/utils/crud_navigation_helper.dart';
import 'package:onekitty_admin/models/bulkSms/messagesDTO.dart';
import 'package:onekitty_admin/models/bulk_sms/sms_groups.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/home/<USER>/create_msg_group.dart';

import 'package:onekitty_admin/nav_routes/nav_routes.dart';
import 'package:onekitty_admin/utils/my_button.dart';
import 'package:onekitty_admin/utils/size_config.dart';
import 'package:onekitty_admin/features/onekitty/widgets/getx_contact_picker.dart';

import '../../../../../../../utils/utils_exports.dart';
import 'widgets/calc.dart';

// ignore_for_file: must_be_immutable
class CrtSmsScreen extends StatefulWidget {
  const CrtSmsScreen({super.key});

  @override
  CrtSmsScreenState createState() => CrtSmsScreenState();
}

class CrtSmsScreenState extends State<CrtSmsScreen> {
  TextEditingController messageController = TextEditingController();
  UserKittyController userController = Get.put(UserKittyController());
  BulkSMSController smsController = Get.put(BulkSMSController());
  final MessageController _message = Get.put(MessageController());
  final _calc = Calc();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  TextEditingController phoneController = TextEditingController();
  PhoneNumber num = CountryConfig.phoneNumber;
  String myPhone = "";
  GlobalKey<NavigatorState> navigatorKey = GlobalKey();
  late Timer _timer;
  final countryPicker = const FlCountryCodePicker();

  CountryCode? countryCode;

  bool isConfirm = false;
  @override
  void initState() {
    try {
      for (SelectedContact item in smsController.selectedContacts) {
        if (item.type == 'contact') {
          selectContact(item.contact!, context);
        }
      }
    } catch (e) {}
    messageController.text = _message.reQmessage.value;
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {});
    });
    super.initState();
  }

  @override
  void dispose() {
    _timer.cancel(); // Cancel the timer to prevent memory leaks
    super.dispose();
  }

  Set<Contact> selectedContacts = <Contact>{};

  void selectContact(Contact selectedContact, BuildContext context) {
    CrudNavigationHelper.selectContact(
      
      selectedContact: selectedContact,
      context: context,
      selectedContacts: selectedContacts,
      onStateChanged: () => setState(() {}),
      isMounted: () => mounted,
    );
  }

  final controller = Get.put(CreateMsgController());

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        appBar: AppBar(
          leadingWidth: 120.w,
          leading: Row(
            children: [
              IconButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  icon: const Icon(Icons.arrow_back)),
              Text("back".tr)
            ],
          ),
        ),
        bottomNavigationBar: Padding(
          padding: const EdgeInsets.all(12.0),
          child: _buildRow1(context),
        ),
        body: Padding(
          padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 30),
          child: CustomScrollView(
            slivers: [
              SliverAppBar.large(
                backgroundColor: Theme.of(context).scaffoldBackgroundColor,
                automaticallyImplyLeading: false,
                pinned: true,
                bottom: PreferredSize(
                    preferredSize: Size(double.infinity, 150.h),
                    child: ColoredBox(
                      color: Theme.of(context).scaffoldBackgroundColor,
                      child: Column(
                        children: [
                          _buildRow(context),
                          SizedBox(height: 1.h),
                          _buildMessage(context),
                          Row(
                            children: [
                              Text(
                                '${'contacts'.tr}: ',
                                style: const TextStyle(
                                    fontSize: 18, fontWeight: FontWeight.bold),
                              ),
                              const Spacer(),
                              Column(
                                children: [
                                  IconButton(
                                      // onPressed: (){},
                                      onPressed: _addContact,
                                      icon: const Icon(
                                          Icons.person_add_outlined)),
                                  Text('add_contact'.tr,
                                      style: TextStyle(fontSize: 8.sp))
                                ],
                              ),
                              SizedBox(
                                width: 10.w,
                              ),
                              Column(
                                children: [
                                  IconButton(
                                    tooltip: 'sms_group'.tr,
                                    onPressed: _selectGroup,
                                    icon: const Icon(Icons.group_add_outlined),
                                  ),
                                  Text('sms_group'.tr,
                                      style: TextStyle(fontSize: 8.sp))
                                ],
                              ),
                              SizedBox(
                                width: 10.w,
                              ),
                              Column(
                                children: [
                                  IconButton(
                                    tooltip: 'csv_excel_file'.tr,
                                    onPressed: _pickFileAndImportContacts,
                                    icon:
                                        const Icon(Icons.upload_file_outlined),
                                  ),
                                  Text('csv_excel_file'.tr,
                                      style: TextStyle(fontSize: 8.sp))
                                ],
                              ),
                            ],
                          ),
                        ],
                      ),
                    )),
                flexibleSpace: FlexibleSpaceBar(
                  background: Column(
                    children: [
                      Text("send_bulk_sms".tr, style: theme.textTheme.titleLarge),
                      SizedBox(height: 13.h),
                      Text("conveniently_send_messages".tr,
                          style: CustomTextStyles.bodyMediumBluegray700),
                      SizedBox(height: 20.h),
                      // Align(
                      //   alignment: Alignment.centerLeft,
                      //   child: Padding(
                      //     padding: EdgeInsets.only(left: 2.w),
                      //     child: Text(
                      //         "Messages cost 0.8 ${CountryConfig.getCurrencyCode} per 160 characters",
                      //         style: CustomTextStyles.bodySmallBluegray700),
                      //   ),
                      // ),
                    ],
                  ),
                ),
              ),
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.only(top: 12.0),
                  child: _buildSelectedNumbers(context),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _selectGroup() async {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        controller.fetchMessageGroups();
        return Dialog(
          child: Obx(() {
            if (controller.isloading.value) {
              return const Center(
                child: CircularProgressIndicator(),
              );
            }

            return Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text('select_groups'.tr, style: theme.textTheme.titleLarge),
                  const SizedBox(height: 16),
                  if (controller.groups.isEmpty) ...[
                    const SizedBox(height: 50),
                    Center(child: Text('no_groups_found'.tr)),
                    const SizedBox(
                      height: 10,
                    ),
                    MyButton(
                      label: 'create_group'.tr,
                      onClick: () {
                        Get.to(() => const CreateMessageGroup(
                            isChecking: true, view: true));
                      },
                    ),
                    const SizedBox(height: 50),
                  ],
                  if (controller.groups.isNotEmpty)
                    Flexible(
                      child: ListView.builder(
                        shrinkWrap: true,
                        itemCount: controller.groups.length,
                        itemBuilder: (context, index) {
                          final group = controller.groups[index];
                          return Container(
                            margin: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 6),
                            decoration: BoxDecoration(
                              border: Border.all(
                                  color: Colors.grey.withOpacity(0.2)),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Obx(() {
                              final remove = smsController.selectedContacts
                                  .where((e) => e.type == 'group')
                                  .map((e) => e.group?.id)
                                  .toList()
                                  .contains(group.id);
                              return ListTile(
                                onTap: () {
                                  if (remove) {
                                    smsController.removeSelectedContact(
                                        contact: smsController.selectedContacts
                                            .firstWhere((e) =>
                                                e.type == 'group' &&
                                                e.group?.id == group.id));
                                  } else {
                                    controller.groupMembers.value =
                                        group.smsGroupMembers ?? [];
                                    Get.to(() => CreateMessageGroup(
                                          isChecking: true,
                                          smsGroup: group,
                                          view: true,
                                          isCheckingAdding: true,
                                        ));
                                  }
                                  setState(() {});
                                },
                                trailing: Chip(
                                    backgroundColor: remove
                                        ? null
                                        : AppColors.primary.withOpacity(0.2),
                                    side: BorderSide(
                                      color: remove
                                          ? Colors.red.withOpacity(0.5)
                                          : Colors.grey.withOpacity(0.4),
                                      width: remove ? 1.0 : 0.5,
                                    ),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    avatar: remove
                                        ? const Icon(
                                            Icons.remove,
                                            color: Colors.red,
                                          )
                                        : const Icon(Icons.add
                                            // color: Colors.white,
                                            ),
                                    label: Text(
                                      remove ? 'remove'.tr : 'add'.tr,
                                      style: TextStyle(
                                          color: remove
                                              ? Colors.red
                                              : Colors.white),
                                    )),
                                title: Text('${group.name}'),
                                subtitle: Text(
                                    '${'total_members'.tr}: ${group.smsGroupMembers?.length ?? 0}'),
                              );
                            }),
                          );
                        },
                      ),
                    ),
                  // ElevatedButton(
                  // onPressed: () {
                  //   Navigator.pop(context);
                  // },
                  // child: const Text('Continue'),
                  // ),
                ],
              ),
            );
          }),
        );
      },
    ).then((selectedGroups) {
      if (selectedGroups != null) {
        // Handle selected groups
      }
    });
  }

  Future<void> _addContact() async {
    showDialog(
        context: context,
        builder: (context) {
          return Dialog(child: _buildAddMemberSection(context));
        });
  }

  /// Build widget for phone number input and "Add Member" button
  Widget _buildAddMemberSection(BuildContext context) {
    final phoneController = TextEditingController();
    final GlobalKey<FormState> _formKey2 = GlobalKey<FormState>();
    final firstName = TextEditingController();
    final lastName = TextEditingController();
    return Form(
      key: _formKey2,
      child: Container(
        width: double.infinity,
        height: 300,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
        ),
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: firstName,
                    decoration: InputDecoration(
                      labelText: 'first_name'.tr,
                      border: const OutlineInputBorder(),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: TextField(
                    controller: lastName,
                    decoration: InputDecoration(
                      labelText: 'last_name'.tr,
                      border: const OutlineInputBorder(),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            TextFormField(
              keyboardType: TextInputType.number,
              maxLength: 14,
              controller: phoneController,
              decoration: InputDecoration(
                labelText: 'phone_number'.tr,
                prefixText: '${CountryConfig.dialCode} ',
                border: const OutlineInputBorder(),
                counterText: '',
                helperText: 'phone_number_format_hint'.tr,
                suffixIcon: IconButton(
                  icon: const Icon(Icons.contacts_outlined),
                  onPressed: () async {
                    _message.reQmessage.value = messageController.text;
                    final List<Contact>? selectedContacts = await Get.to(() => const GetXContactPicker(
                      mode: ContactPickerMode.multiple,
                      display: ContactPickerDisplay.fullScreen,
                      title: 'Select Contacts',
                    ));

                    if (selectedContacts != null && selectedContacts.isNotEmpty) {
                      for (var contact in selectedContacts) {
                        selectContact(contact, context);
                      }
                      setState(() {}); // Refresh UI to show selected contacts
                    }
                  },
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'phone_number_required'.tr;
                }
                String number = value.startsWith('0') ? value : '0$value';
                if (!RegExp(r'^0[17][0-9]{8}$').hasMatch(number)) {
                  return 'enter_valid_phone_number'.tr;
                }
                return null;
              },
              onChanged: (value) {
                String normalized =
                    value.startsWith('0') ? value.substring(1) : value;
                setState(() {
                  myPhone = '${CountryConfig.dialCode}$normalized';
                });
              },
            ),
            const SizedBox(height: 12),
            const SizedBox(height: 12),
            MyButton(
              onClick: () {
                if (_formKey2.currentState!.validate()) {
                  String phoneNumber = myPhone;

                  Phone phone =
                      Phone(phoneNumber, normalizedNumber: phoneNumber);
                  Contact contact = Contact(
                    phones: [phone],
                    displayName: "${firstName.text} ${lastName.text}",
                    name: Name(
                      first: firstName.text,
                      last: lastName.text,
                    ),
                  );
                  smsController.addSelectedContact(
                      context: context, type: 'contact', contact: contact);
                  // setState(() {
                  phoneController.clear();
                  firstName.clear();
                  lastName.clear();
                  // });
                } else {
                  ToastUtils.showErrorToast(
                      context, "Enter a number to continue", "Error");
                }
              },
              label: 'add_member'.tr,
            ),
          ],
        ),
      ),
    );
  }

  /// Picks CSV/XLSX file using FilePicker, parses, and imports into group
  Future<void> _pickFileAndImportContacts() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: ['csv', 'xlsx'],
    );
    if (result == null) return;

    File file = File(result.files.single.path!);
    String extension = result.files.single.extension!.toLowerCase();

    if (extension == 'csv') {
      final input = file.openRead();
      final fields = await input
          .transform(utf8.decoder)
          .transform(const CsvToListConverter())
          .toList();
      final _groupMemberContact = <SmsGroupMember>[];
      setState(() {
        for (var row in fields.skip(1)) {
          // Phone phone = Phone(row[2] ?? '', normalizedNumber: row[2] ?? '');
          _groupMemberContact.add(SmsGroupMember(
            firstName: row[0] ?? '',
            secondName: row[1] ?? '',
            phoneNumber: row[2] ?? '',
          ));
        }
      });

      final existingGroup = smsController.selectedContacts
          .where((e) => e.type == 'group')
          .map((e) => e.group)
          .where((g) => g?.name == file.path.split('/').last)
          .isNotEmpty;

      if (!existingGroup) {
        smsController.addSelectedContact(
            context: context,
            type: 'group',
            group: SmsGroups(
                name: file.path.split('/').last,
                smsGroupMembers: _groupMemberContact));
      } else {
        ToastUtils.showInfoToast(
            context, "This file has already been added", "Check");
      }
    } else if (extension == 'xlsx') {
      var bytes = file.readAsBytesSync();
      var excel = ex.Excel.decodeBytes(bytes);
      var sheet = excel.tables[excel.tables.keys.first]!;
      final _groupMemberContact = <SmsGroupMember>[];
      for (var row in sheet.rows.skip(1)) {
        // Phone phone = Phone(row[2]?.value?.toString() ?? '',
        //     normalizedNumber: row[2]?.value?.toString() ?? '');
        _groupMemberContact.add(SmsGroupMember(
            firstName: row[0]?.value?.toString() ?? '',
            secondName: row[1]?.value?.toString() ?? '',
            phoneNumber: row[2]?.value?.toString() ?? ''));
      }

      final existingGroup = smsController.selectedContacts
          .where((e) => e.type == 'group')
          .map((e) => e.group)
          .where((g) => g?.name == file.path.split('/').last)
          .isNotEmpty;

      if (!existingGroup) {
        smsController.addSelectedContact(
            context: context,
            type: 'group',
            group: SmsGroups(
                name: file.path.split('/').last,
                smsGroupMembers: _groupMemberContact));
      } else {
        ToastUtils.showInfoToast(
            context, "This file has already been added", "Check");
      }
    }
  }

  /// Section Widget
  Widget _buildRow(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 2.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text("Message", style: CustomTextStyles.titleSmallGray90001),
          const Spacer(),
          Padding(
            padding: EdgeInsets.only(bottom: 3.h),
            child: Text("Est. cost:", style: theme.textTheme.bodySmall),
          ),
          Padding(
            padding: EdgeInsets.only(left: 3.w, bottom: 3.h),
            child: Text(
                "${_calc.calculateCharges(messageController.text, smsController.selectedContacts.where((e) => e.type == 'contact').toList().length + smsController.selectedContacts.where((e) => e.type == 'group').map((e) => e.group!.smsGroupMembers!.map((e) => Contact(displayName: "${e.firstName ?? ''} ${e.secondName ?? ''}", name: Name(first: e.firstName ?? '', last: e.secondName ?? ''), phones: [
                          Phone(e.phoneNumber!,
                              normalizedNumber: e.phoneNumber!)
                        ]))).expand((e) => e).toList().length)} ${CountryConfig.getCurrencyCode}",
                style: CustomTextStyles.labelLargeff545963),
          ),
        ],
      ),
    );
  }

  Widget _buildMessage(BuildContext context) {
    return Form(
      key: _formKey,
      child: TextFormField(
        controller: messageController,
        decoration: InputDecoration(
          hintText: "e.g Hello, prepare for a meeting...",
          hintStyle: CustomTextStyles.bodyMediumBluegray400,
          contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 9.h),
        ),
        maxLines: 5,
        validator: (value) {
          if (value!.isEmpty) {
            return "Message is Empty, Add messages";
          } else {
            return null;
          }
        },
        onChanged: (value) {
          setState(() {
            // _message.reSmessage.value = value;
            _calc.calculateCharges(value, selectedContacts.length);
          });
        },
      ),
    );
  }

  /// Section Widget
  Widget _buildSendMessage(BuildContext context) {

    return Obx(() => CustomKtButton(
          isLoading: smsController.isConfirming.isTrue,
          onPress: () async {
            if (_formKey.currentState!.validate()) {
              final allGroups = smsController.selectedContacts
                  .where((e) => e.type == 'group')
                  .map((e) => e.group)
                  .toList();
              final List<Contact> groupContacts = [];
              final Set<String> uniquePhoneNumbers = {};

              // Add contacts from groups
              for (var group in allGroups) {
                for (var member in group!.smsGroupMembers!) {
                  final phoneNumber = member.phoneNumber!;
                  if (!uniquePhoneNumbers.contains(phoneNumber)) {
                    uniquePhoneNumbers.add(phoneNumber);
                    groupContacts.add(Contact(
                        displayName:
                            "${member.firstName ?? ''} ${member.secondName ?? ''}",
                        name: Name(first: member.firstName ?? '', last: member.secondName ?? ''),
                        phones: [
                          Phone(phoneNumber, normalizedNumber: phoneNumber)
                        ]));
                  }
                }
              }

              // Add individual contacts
              for (var contact in smsController.selectedContacts
                  .where((e) => e.type == 'contact')) {
                final phoneNumber = contact.contact!.phones.first.number;
                if (!uniquePhoneNumbers.contains(phoneNumber)) {
                  uniquePhoneNumbers.add(phoneNumber);
                  groupContacts.add(contact.contact!);
                }
              }

              final recipients = groupContacts.length;
              final messagedto = MessagesDto(
                  userId: userController.user.value.id ?? 0,
                  message: messageController.text,
                  recipient: groupContacts);
              final res =
                  await smsController.ConfirmMessages(message: messagedto);
              if (res) {
                showConfirmDialog(
                    context: context,
                    price: smsController.resData["price"],
                    smscount: smsController.resData["sms_count"],
                    recipients: recipients,
                    charactercount: smsController.resData["characters_count"],
                    messageDto: messagedto);
              } else {
                if (smsController.apiMessage.string ==
                    "insufficient balance,top up to continue") {
                  Get.toNamed(NavRoutes.topup);
                }
                ToastUtils.showInfoToast(
                    context,
                    smsController.apiMessage.string,
                    "Please top up to send the message");
              }
            }
          },
          width: 151.w,
          btnText: "Send Message",
        ));
  }

  Widget _buildRow1(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text("Add the contacts that should receive the message",
            style: CustomTextStyles.bodyMediumGray600),
        SizedBox(height: 8.h),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // _buildBack(context),
            const Spacer(),
            Padding(
              padding: const EdgeInsets.all(4.0),
              child: _buildSendMessage(context),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSelectedNumbers(BuildContext context) {
    final selectedContacts = smsController.selectedContacts;
    //  ref
    //     .watch(selectContactControllerProvider.notifier)
    //     .getSelectedContacts();

    // Check if selected contacts list is empty
    if (selectedContacts.isEmpty) {
      return CustomImageView(
        imagePath: AssetUrl.imgGroup13,
        height: 150.h,
        width: 254.w,
      );
    } else {
      return Container(
          margin: EdgeInsets.only(left: 2.h),
          padding: EdgeInsets.symmetric(horizontal: 15.h, vertical: 17.h),
          decoration: AppDecoration.outlineGray
              .copyWith(borderRadius: BorderRadiusStyle.roundedBorder8),
          child: Obx(
            () => Column(
              children: [
                if (selectedContacts.isNotEmpty)
                  Align(
                      alignment: Alignment.centerRight,
                      child: TextButton(
                          onPressed: () {
                            showDialog(
                              context: context,
                              builder: (BuildContext context) {
                                return AlertDialog(
                                  title: const Text('Warning'),
                                  content: const Text(
                                      'Are you sure you want to clear all contacts?'),
                                  actions: [
                                    TextButton(
                                      onPressed: () => Navigator.pop(context),
                                      child: const Text('Cancel'),
                                    ),
                                    TextButton(
                                      onPressed: () {
                                        smsController
                                            .removeAllSelectedContacts();
                                        Navigator.pop(context);
                                      },
                                      child: const Text('Clear'),
                                    ),
                                  ],
                                );
                              },
                            );
                          },
                          child: Text('clear all',
                              style: theme.textTheme.bodySmall))),
                ListView.builder(
                    physics: const NeverScrollableScrollPhysics(),
                    shrinkWrap: true,
                    itemCount: selectedContacts.length,
                    itemBuilder: (context, index) {
                      if (selectedContacts[index].type == 'group') {
                        return ListTile(
                          onTap: () {
                            controller.groupMembers.value =
                                selectedContacts[index]
                                        .group
                                        ?.smsGroupMembers ??
                                    [];
                            Get.to(() => CreateMessageGroup(
                                isChecking: true,
                                smsGroup: selectedContacts[index].group,
                                view: true));
                            /*
                            showDialog(
                              context: context,
                              builder: (BuildContext context) {
                                final group = selectedContacts[index].group;
                                final groupMembers =
                                    group?.smsGroupMembers?.toList().obs ??
                                        <SmsGroupMember>[].obs;
                                return Dialog(
                                  child: Container(
                                    padding: const EdgeInsets.all(16),
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Text('Group Members',
                                            style: theme.textTheme.titleLarge),
                                        const SizedBox(height: 16),
                                        Flexible(
                                          child: Obx(
                                            () => ListView.builder(
                                              shrinkWrap: true,
                                              itemCount: groupMembers.length,
                                              itemBuilder:
                                                  (context, memberIndex) {
                                                final member =
                                                    groupMembers[memberIndex];
                                                return ListTile(
                                                  trailing: IconButton(
                                                    onPressed: () {
                                                      groupMembers.removeAt(
                                                          memberIndex);
                                                          selectedContacts[index].group?.smsGroupMembers?.removeAt(memberIndex);
                                                    },
                                                    icon: const Icon(Icons
                                                        .remove_circle_outline),
                                                  ),
                                                  leading: const CircleAvatar(
                                                      child:
                                                          Icon(Icons.person)),
                                                  title: Text(
                                                      '${member.firstName ?? ''} ${member.secondName ?? ''}'),
                                                  subtitle: Text(
                                                      member.phoneNumber ?? ''),
                                                );
                                              },
                                            ),
                                          ),
                                        ),
                                        ElevatedButton(
                                          onPressed: () =>
                                              Navigator.pop(context),
                                          child: const Text('Close'),
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            );
                          */
                          },
                          leading: const CircleAvatar(child: Icon(Icons.group)),
                          title:
                              Text(selectedContacts[index].group?.name ?? ''),
                          subtitle: Text(
                              "total contacts: ${selectedContacts[index].group?.smsGroupMembers?.length ?? 0}"),
                          trailing: IconButton(
                            onPressed: () {
                              smsController.removeSelectedContact(
                                  contact: selectedContacts[index]);
                              // ref
                              //     .read(selectContactControllerProvider.notifier)
                              //     .removeSelectedContact(selectedContacts[index]);
                              // setState(() {});
                            },
                            icon: const Icon(Icons.cancel),
                          ),
                        );
                      }
                      return ListTile(
                        leading: const CircleAvatar(child: Icon(Icons.person)),
                        title: Text(
                            selectedContacts[index].contact?.displayName ?? ''),
                        subtitle: Text(selectedContacts[index]
                                .contact
                                ?.phones
                                .first
                                .number ??
                            ''),
                        trailing: IconButton(
                          onPressed: () {
                            smsController.removeSelectedContact(
                                contact: selectedContacts[index]);
                            setState(() {}); // Refresh UI after removing contact
                          },
                          icon: const Icon(Icons.cancel),
                        ),
                      );
                    }),
              ],
            ),
          )

          /* ListView.builder(
          shrinkWrap: true,
          itemCount: ref
              .watch(selectContactControllerProvider.notifier)
              .getSelectedContacts()
              .length,
          itemBuilder: (context, index) {
            final selectedContact = ref
                .watch(selectContactControllerProvider.notifier)
                .getSelectedContacts()[index];
        
            return Container(
              decoration: BoxDecoration(
                  borderRadius: BorderRadiusStyle.roundedBorder8),
              child: Column(
                children: [
                  Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Opacity(
                          opacity: 0.5,
                          child: Padding(
                            padding:
                                EdgeInsets.only(top: 6.h, bottom: 8.h),
                            child: Text(
                              "${index + 1}",
                              style: theme.textTheme.titleSmall!.copyWith(
                                color: appTheme.blueGray700
                                    .withOpacity(0.53),
                              ),
                            ),
                          ),
                        ),
                        CustomImageView(
                          imagePath: AssetUrl.imgPerson,
                          height: 25.h,
                          width: 25.w,
                          margin: EdgeInsets.only(left: 6.h),
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: EdgeInsets.only(
                                  left: 6.h, top: 1.h, bottom: 1.h),
                              child: Text(
                                selectedContact.displayName,
                                style: CustomTextStyles
                                    .titleSmallGray90001
                                    .copyWith(
                                        color: isLight.value
                                            ? appTheme.gray90001
                                            : appTheme.whiteA70001),
                              ),
                            ),
                            Padding(
                              padding: EdgeInsets.only(
                                  left: 6.h, top: 1.h, bottom: 1.h),
                              child: Text(
                                selectedContact.phones.first.number,
                                style: CustomTextStyles
                                    .titleSmallGray90001
                                    .copyWith(
                                        color: isLight.value
                                            ? appTheme.gray90001
                                            : appTheme.whiteA70001),
                              ),
                            ),
                          ],
                        ),
                        const Spacer(),
                        InkWell(
                          onTap: () {
                            ref
                                .read(selectContactControllerProvider
                                    .notifier)
                                .removeSelectedContact(selectedContact);
                            setState(() {});
                          },
                          child: CustomImageView(
                            imagePath: AssetUrl.imgIconoirCancel,
                            height: 18.h,
                            width: 18.w,
                            margin: EdgeInsets.symmetric(vertical: 9.h),
                          ),
                        ),
                      ]),
                  SizedBox(height: 2.h),
                  const Divider()
                ],
              ),
            );
          },
        ),
        */
          );
    }
  }

  /// Navigates back to the previous screen.
  void onTapArrowLeft(BuildContext context) {
    Navigator.pop(context);
  }

  Future<void> showConfirmDialog({
    required BuildContext context,
    required price,
    required smscount,
    required recipients,
    required charactercount,
    required messageDto,
  }) {
    return showAnimatedDialog(
      barrierDismissible: true,
      animationType: DialogTransitionType.sizeFade,
      curve: Curves.fastOutSlowIn,
      duration: const Duration(milliseconds: 900),
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          child: SizedBox(
            height: SizeConfig.screenHeight * .36,
            width: SizeConfig.screenWidth * .8,
            child: Padding(
              padding: EdgeInsets.symmetric(
                  horizontal: getProportionalScreenWidth(10)),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Center(
                    child: Text(
                      "Message Summary",
                      style:
                          TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                    ),
                  ),
                  SizedBox(height: 7.h),

                  Text(
                    "Number of messages: ${smscount.toString()}",
                    style: const TextStyle(
                        fontSize: 12, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 7.h),

                  Text(
                    "character Count: ${charactercount.toString()}",
                    style: const TextStyle(
                        fontSize: 12, fontWeight: FontWeight.bold),
                  ),

                  Text(
                    "Recipients: ${recipients.toString()}",
                    style: const TextStyle(
                        fontSize: 12, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 15.h),
                  // Text(title, style: CustomTextStyles.titleSmallIndigo500),
                  Text(
                    "SMS will Charge: ${price.toString()}${CountryConfig.getCurrencyCode}",
                    style: const TextStyle(
                        fontSize: 12, fontWeight: FontWeight.bold),
                    // style: TextStyle(color: Colors.green),
                  ),

                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Theme.of(context).canvasColor,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20.sp),
                            side: BorderSide(
                                width: 2.sp,
                                color: Theme.of(context).primaryColor),
                          ),
                        ),
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        child: const Text(
                          "cancel",
                          style: TextStyle(color: Colors.black),
                        ),
                      ),
                      const Spacer(),
                      Obx(
                        () => CustomKtButton(
                          isLoading: smsController.isSending.isTrue,
                          width: 90.w,
                          height: 30.h,
                          btnText: "Confirm",
                          onPress: () async {
                            try {
                              final res = await smsController.sendMessages(
                                  message: messageDto);
                              if (res) {
                                selectedContacts.clear();
                                setState(() {
                                  messageController.clear();
                                });
                                Get.offNamed(NavRoutes.SmSsent);
                              } else {
                                ToastUtils.showInfoToast(
                                    context,
                                    smsController.apiMessage2.string,
                                    "Add recipients");
                              }
                            } catch (e) {}
                          },
                        ),
                      ),
                      SizedBox(
                        height: 10.sp,
                      ),
                    ],
                  )
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
