import 'dart:io';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:get/instance_manager.dart';
import 'package:get_storage/get_storage.dart';
import 'package:logger/logger.dart';
import 'package:onekitty_admin/features/login/controllers/auth_controller.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/controllers/beneficiary_controller.dart';
import 'package:onekitty_admin/features/onekitty/controllers/bulksms_controller.dart';
import 'package:onekitty_admin/features/onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty_admin/features/onekitty/controllers/contribute_controller.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/controllers/controllers.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/controllers/create_event_controller.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/controllers/edit_event_controller.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/controllers/edit_ticket_controller.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/controllers/events_controller.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/controllers/invite_page_controller.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/controllers/invite_users_controller.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/controllers/payments_page_controller.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/controllers/signatory_controller.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/transfers/services/transfer_service.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/controllers/verify_ticket_controller.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/controllers/view_single_event.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/controllers/vieweventcontroller.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/controllers/kitty_controller.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/controllers/user_ktty_controller.dart';
import 'package:onekitty_admin/features/updateKYC/controllers/kyc_controller.dart';
import 'package:onekitty_admin/helpers/loader_easy.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/create_event/time_and_location.dart';
import 'package:onekitty_admin/services/analytics.dart';
import 'package:onekitty_admin/services/api_urls.dart';
import 'package:onekitty_admin/services/custom_logger.dart';
import 'package:onekitty_admin/services/http_service.dart';
import 'package:onekitty_admin/services/location_service.dart' show LocationService;
import 'package:onekitty_admin/services/permission_service.dart';
import 'package:onekitty_admin/utils/cache_keys.dart';
import 'package:onekitty_admin/services/location_service.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/transactions/services/transaction_service.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter_device_imei/flutter_device_imei.dart';
import 'package:onekitty_admin/configs/is_phone_valid.dart';

// Main service initialization function with optimized loading
Future<void> servicesInitialize() async {
  // Critical services first
  await GetStorage.init();
  Loader.configLoader();
  Get.lazyPut(() => GetStorage(), fenix: true);
  
  // Set up logging only once
  Get.lazyPut(() => Logger(filter: CustomLogFilter()), fenix: true);
  
  // Initialize critical controllers
  Get.put(HttpService());
  Get.put(AuthenticationController());
  
  // Initialize network service
  HttpService httpService = Get.find();
  httpService.initializeDio();
  
  // Start non-critical initializations in parallel
  // await Future.wait([
  //   getBaseUrl(),
  // ]);
  
  // Initialize controllers with lazy loading
  _initLazyControllers();
  
  // Initialize location service
  Get.put(LocationService());

  // Initialize permission service for deferred permission handling
  Get.put(PermissionService());

  // Initialize browser components if needed
  await initInappWebview();
  
  // Initialize analytics and phone validator in the background
  Future.delayed(const Duration(milliseconds: 500), () {
    AnalyticsEngine.init();
    _initPhoneValidator();
  });
}



// Initialize controllers with lazy loading for better startup performance
void _initLazyControllers() {
  // Core functionality controllers
  Get.lazyPut(()=> PermissionService());
  Get.lazyPut(() => UserKittyController(), fenix: true);
  Get.lazyPut(() => ContributeController(), fenix: true);
  Get.lazyPut(() => BulkSMSController(), fenix: true);
  Get.lazyPut(() => KittyController(), fenix: true);
  Get.lazyPut(() => ChamaController(), fenix: true);
  Get.lazyPut(() => GlobalControllers(), fenix: true);
  
  // Event-related controllers - these can be loaded later as they aren't needed immediately
  Get.lazyPut(() => KYCController(), fenix: true);
  Get.lazyPut(() => Eventcontroller(), fenix: true);
  Get.lazyPut(() => CreateEventController(), fenix: true);
  Get.lazyPut(() => TimeAndLocationController(), fenix: true);
  Get.lazyPut(() => EditTicketController(), fenix: true);
  Get.lazyPut(() => EditEventController(), fenix: true);
  Get.lazyPut(() => ViewSingleEventController(), fenix: true);
  Get.lazyPut(() => PaymentsController(), fenix: true);
  Get.lazyPut(() => InvitePageController(), fenix: true);
  Get.lazyPut(() => VerifyTicketController(), fenix: true);
  Get.lazyPut(() => TransferService(), fenix: true);
  Get.lazyPut(() => SignatoryTransactionController(), fenix: true);
  Get.lazyPut(() => ViewEventController(), fenix: true);
  Get.lazyPut(() => InviteUsersController(), fenix: true);
  Get.lazyPut(()=>ChamaDataController(), fenix: true);
  Get.lazyPut(() => BeneficiaryController(), fenix: true);

  // Transaction service
  Get.lazyPut(() => TransactionService(), fenix: true);
}



// Fetch base URL from remote config
Future<void> getBaseUrl() async {
  debugPrint("------Getting url-------");
  bool maintenance = false;
  final remoteConfig = FirebaseRemoteConfig.instance;
  await remoteConfig.setDefaults({
    "base_url_dev": ApiUrls.BASE_URL_DEV,
    "base_url": ApiUrls.BASE_URL_LIVE,
    "maintainance": maintenance
  });
  await remoteConfig.setConfigSettings(RemoteConfigSettings(
    fetchTimeout: const Duration(minutes: 1),
    minimumFetchInterval: const Duration(minutes: 15),
  ));
  // Fetch remote config values
  await remoteConfig.fetch();
  await remoteConfig.activate();
  String baseurlDev = remoteConfig.getString("base_url_dev");
  String baseurlProd = remoteConfig.getString("base_url");
  ApiUrls.BASE_URL_DEV = baseurlDev;
  ApiUrls.BASE_URL_LIVE = baseurlProd;
  maintenance = remoteConfig.getBool("maintainance");
  GetStorage().write(CacheKeys.maintainance, maintenance);
  debugPrint("===========URL FETCHED============");
}

// Initialize WebView if needed
Future<void> initInappWebview() async {
  if (!kIsWeb && defaultTargetPlatform == TargetPlatform.android) {
    await InAppWebViewController.setWebContentsDebuggingEnabled(kDebugMode);
  }
}

// Pre-warm phone validator for faster subsequent validations
Future<void> _initPhoneValidator() async {
  try {
    // Pre-warm with a test number to initialize the library
    PhoneNumberValidator.isValidPhone('+254700000000');
    debugPrint('Phone validator initialized');
  } catch (e) {
    debugPrint('Phone validator init failed: $e');
  }
}


