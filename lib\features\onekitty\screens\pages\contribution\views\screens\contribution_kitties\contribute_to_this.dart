import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onekitty_admin/features/onekitty/controllers/contribute_controller.dart';
import 'package:onekitty_admin/nav_routes/nav_routes.dart';

class ContributeToThis extends StatefulWidget {
  final int kittyId;
  const ContributeToThis({super.key, required this.kittyId});

  @override
  State<ContributeToThis> createState() => _ContributeToThisState();
}

class _ContributeToThisState extends State<ContributeToThis> {
  final ContributeController contributeController = Get.find();
  bool isLoading = true;
  bool hasError = false;
  String errorMessage = '';

  @override
  void initState() {
    super.initState();
    Future.microtask(() => getKittyData());
  }

  Future<void> getKittyData() async {
    if (!mounted) return;
    
    try {
      setState(() {
        isLoading = true;
        hasError = false;
      });
      
      final res = await contributeController.getKitty(id: widget.kittyId);
      
      if (mounted) {
        
        Navigator.pushReplacementNamed(context, "${NavRoutes.kittycontributionScreen}${widget.kittyId}");
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          hasError = true;
          errorMessage = e.toString();
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('loading_kitty_data'.tr),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Center(
        child: hasError
          ? _buildErrorView()
          : _buildLoadingView(),
      ),
    );
  }

  Widget _buildLoadingView() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const CircularProgressIndicator(),
        const SizedBox(height: 20),
        Text(
          'loading_kitty_data_message'.tr,
          style: Theme.of(context).textTheme.titleMedium,
        ),
      ],
    );
  }

  Widget _buildErrorView() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Icon(
          Icons.error_outline,
          color: Colors.red,
          size: 60,
        ),
        const SizedBox(height: 16),
        Text(
          'failed_to_load_data'.tr,
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          child: Text(
            errorMessage,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ),
        const SizedBox(height: 24),
        ElevatedButton(
          onPressed: getKittyData,
          child: Text('try_again'.tr),
        ),
      ],
    );
  }
}
