import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:onekitty_admin/features/admin/features/home/<USER>/home_screen.dart';
import 'package:onekitty_admin/utils/my_button.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:onekitty_admin/features/onekitty/controllers/contribute_controller.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/controllers/kitty_controller.dart';
import 'package:onekitty_admin/helpers/colors.dart';
import 'package:onekitty_admin/helpers/extensions/text_styles.dart';
import 'package:onekitty_admin/helpers/show_toast.dart';
import 'package:onekitty_admin/nav_routes/nav_routes.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/widgets/row_widget.dart';
import 'package:onekitty_admin/features/onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty_admin/utils/whatsapp_validator.dart';
import 'package:onekitty_admin/utils/common_strings.dart';
import 'package:onekitty_admin/utils/custom_button.dart';
import 'package:onekitty_admin/utils/size_config.dart';

class WhatsAppErrorDialog extends StatelessWidget {
  final String? msg;
  final String? rsn;
  final String? whatsappNo;
  const WhatsAppErrorDialog({super.key, this.msg, this.rsn, this.whatsappNo});
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Row(
          children: [
            Icon(Icons.error, color: Colors.red, size: 28),
            SizedBox(width: 10),
            Text(
              'Oops! WhatsApp Hiccup',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
            ),
          ],
        ),
        const SizedBox(height: 15),
        Text(
          "${msg ?? ''}. We couldn't attach your WhatsApp group to the kitty. Here's what might have gone wrong:",
          style: const TextStyle(fontSize: 14, color: Colors.black87),
        ),
        const SizedBox(height: 15),
        Container(
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            color: Colors.red.shade50,
            borderRadius: BorderRadius.circular(8),
          ),
          child: rsn != null
              ? Text('$rsn'.replaceAll(', ', ',\n'))
              : const Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ListTile(
                      contentPadding: EdgeInsets.zero,
                      leading:
                          Icon(Icons.circle, size: 10, color: Colors.black87),
                      title: Text(
                          "The group link provided is invalid or has expired",
                          style: TextStyle(fontSize: 14)),
                    ),
                    ListTile(
                      contentPadding: EdgeInsets.zero,
                      leading:
                          Icon(Icons.circle, size: 10, color: Colors.black87),
                      title: Text(
                          "The group is set to admin-approval only, meaning an admin must approve the bot's request to join",
                          style: TextStyle(fontSize: 14)),
                    ),
                    ListTile(
                      contentPadding: EdgeInsets.zero,
                      leading:
                          Icon(Icons.circle, size: 10, color: Colors.black87),
                      title: Text(
                          "OneKitty was previously part of the group but was removed by an admin",
                          style: TextStyle(fontSize: 14)),
                    ),
                  ],
                ),
        ),
        const SizedBox(height: 15),
        Row(
          children: [
            const Icon(Icons.warning, color: Colors.red),
            const SizedBox(width: 10),
            Expanded(
              child: Column(
                children: [
                  const Text(
                    "Please try again or contact support if the issue persists send the whatsAppLink to this Number",
                    style: TextStyle(fontSize: 14, color: Colors.black87),
                  ),
                  TextButton(
                    child: Text('$whatsappNo'),
                    onPressed: () async {
                      final Uri whatsappUrl =
                          Uri.parse("https://wa.me/$whatsappNo");
                      if (await canLaunch(whatsappUrl.toString())) {
                        await launch(whatsappUrl.toString());
                      } else {
                        throw 'Could not launch $whatsappUrl';
                      }
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class ErrorPage extends StatefulWidget {
  final String link;
  final String reasons;
  final String messages;
  final String whatsAppNo;
  const ErrorPage(
      {super.key,
      required this.link,
      required this.reasons,
      required this.messages,
      required this.whatsAppNo});

  @override
  State<ErrorPage> createState() => _ErrorPageState();
}

class _ErrorPageState extends State<ErrorPage> {
  final box = GetStorage();
  TextEditingController linkController = TextEditingController();
  TextEditingController whatsappNoController = TextEditingController();
  final formkey = GlobalKey<FormState>();
  final formkey2 = GlobalKey<FormState>();
  final ContributeController singleKitty = Get.find();
  final KittyController kittyController = Get.find();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    List<String> reasonsList = widget.reasons.split('_');
    return SafeArea(
      child: Scaffold(
        // appBar: AppBar(),
        body: Container(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Form(
            key: formkey,
            child: SingleChildScrollView(
              child: Column(
                children: [
                  Image.asset("assets/images/Error states.png"),
                  Text(
                    "Oops! WhatsApp link trouble",
                    style: context.titleText,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  if (widget.messages.isNotEmpty)
                    Text(
                      widget.messages,
                      textAlign: TextAlign.center,
                    ),
                   const Text(
                    "Don't worry, we can fix it! Here are some of the reasons:",
                    textAlign: TextAlign.center,
                  ),
                  if (widget.messages.isNotEmpty) const SizedBox(height: 15),
                  ListView.builder(
                      itemCount: reasonsList.length,
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemBuilder: (context, index) {
                        return Row(
                          children: [
                            SizedBox(
                                width: 40,
                                child: Icon(
                                  Icons.circle,
                                  size: 8.spMin,
                                )),
                            Expanded(child: Text(reasonsList[index])),
                          ],
                        );
                      }),
                  const SizedBox(
                    height: 10,
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "Whatsapp group link",
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold, fontSize: 18),
                      ),
                      const SizedBox(
                        height: 15,
                      ),
                      CustomTextField(
                        controller: linkController,
                        hintText: widget.link,
                        validator: (value) {
                          if (value!.isEmpty) {
                            return "Enter whatsapp invite link";
                          } else if (!WhatsAppValidator.isValidWhatsAppLink(value)) {
                            return WhatsAppValidator.getValidationErrorMessage();
                          }
                          return null;
                        },
                      ),
                      const Text("Confirm or enter a new group link above"),
                    ],
                  ),
                  const SizedBox(
                    height: 15,
                  ),
                  Obx(
                    () => CustomKtButton(
                      btnText: "Connect to WhatsApp",
                      isLoading: kittyController.isLinkloading.isTrue,
                      onPress: () async {
                        if (formkey.currentState!.validate()) {
                          var res = await kittyController.joinGroup(
                              id: kittyController.kittCreated.value.iD ?? 0,
                              context: context,
                              link: linkController.text.trim());
                          if (res) {
                            Get.offNamed(NavRoutes.myKittiescontribtionScreen);
                          }
                        }
                      },
                      text: '',
                      onPressed: () {},
                    ),
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  Row(
                    children: [
                      Expanded(
                          child: Divider(
                        thickness: 1,
                        color: AppColors.slate.withOpacity(1),
                      )),
                      Text(
                        " OR ",
                        style: context.labelLarge?.copyWith(
                            color: AppColors.greyTextColor,
                            fontWeight: FontWeight.bold,
                            fontSize: 17),
                      ),
                      Expanded(
                          child: Divider(
                        thickness: 1,
                        color: AppColors.slate.withOpacity(1),
                      ))
                    ],
                  ),
                  SingleLineRow(
                    text: "Contact OneKitty support",
                    popup: KtStrings.whatsAppJoinMessage,
                  ),
                  const Text(
                    "Send a WhatsApp message with the link for an update",
                  ),
                  const SizedBox(
                    height: 15,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      MyButton(
                          width: 100.w,
                          outlined: true,
                          onClick: () {
                            Get.back();
                            Get.back();
                          },
                          label: "Skip"),
                      MyButton(
                        width: 200.w,
                        label: "Send Message",
                        onClick: () async {
                          if (formkey.currentState!.validate()) {
                            final Uri url = Uri.parse(
                                "https://wa.me/${kittyController.whatsappnumber.value}?text=Please attach my kitty with this group link: ${linkController.text.trim()}\nKitty link:${singleKitty.urlKit.string}");
                            if (!await launchUrl(url)) {
                              ToastUtils.showErrorToast(
                                  context, "Could not launch the url", "Error");
                            }
                          }
                        },
                      ),
                    ],
                  )
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget buildBottom() {
    //final kittyCreated = singleKitty.kittGoten;
    return Container(
      height: SizeConfig.screenHeight * 0.4,
      margin: const EdgeInsets.symmetric(vertical: 20, horizontal: 20),
      decoration: const BoxDecoration(
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20), topRight: Radius.circular(20))),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          Row(
            children: [
              Text(
                "Title:",
                style: context.titleText?.copyWith(color: AppColors.stackBlue),
              ),
              const SizedBox(
                width: 4,
              ),
              Text(
                kittyController.kittCreated.value.title ?? "",
                //kittyCreated.value.title ?? "",
                style: context.dividerTextLarge,
              )
            ],
          ),
          const SizedBox(
            height: 12,
          ),
          Row(
            children: [
              Text(
                "Description:",
                style: context.titleText?.copyWith(color: AppColors.stackBlue),
              ),
              const SizedBox(
                width: 4,
              ),
              Expanded(
                child: Text(
                  kittyController.kittCreated.value.description ?? "",
                  overflow: TextOverflow.fade,
                  //kittyCreated.value.description ?? "",
                  style: context.dividerTextLarge,
                ),
              )
            ],
          ),
          Row(
            children: [
              Text(
                "Kitty Url:",
                style: context.titleText?.copyWith(color: AppColors.stackBlue),
              ),
              const SizedBox(
                width: 4,
              ),
              SelectableText(
                kittyController.urlKit.string,
                showCursor: true,
              )
            ],
          ),
          //Add Kitty Url that can be copied
          const SizedBox(
            height: 10,
          ),
          ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                Get.to(() => AdminHomeScreen());
              },
              child: const Text("Go to Home"))
        ],
      ),
    );
  }
}
