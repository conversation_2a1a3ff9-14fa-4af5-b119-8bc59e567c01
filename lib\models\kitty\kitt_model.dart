// To parse this JSON data, do
//
//     final kittyUser = kittyUserFromJson(jsonString);

import 'dart:convert';

import 'package:onekitty_admin/models/kitty_model.dart';


KittyUser kittyUserFromJson(String str) => KittyUser.fromJson(json.decode(str));

String kittyUserToJson(KittyUser data) => json.encode(data.toJson());

class KittyUser {
  Kitty? kitty;
  String? kittyStatus;
  String? kittBeneficiaryChannel;
  String? kittyType;
  int? volumes;

  KittyUser(
      {this.kitty,
      this.kittyStatus,
      this.kittBeneficiaryChannel,
      this.kittyType,
      this.volumes});

  factory KittyUser.fromJson(Map<String, dynamic> json) => KittyUser(
      kitty: json["kitty"] == null ? null : Kitty.fromJson(json["kitty"]),
      kittyStatus: json["kitty_status"],
      kittBeneficiaryChannel: json["kitt_beneficiary_channel"],
      kittyType: json["kitty_type"],
      volumes: json["volumes"]);

  Map<String, dynamic> toJson() => {
        "kitty": kitty?.toJson(),
        "kitty_status": kittyStatus,
        "kitt_beneficiary_channel": kittBeneficiaryChannel,
        "kitty_type": kittyType,
        "volumes": volumes
      };
}