import 'package:flutter/material.dart';
import 'package:flutter_contacts/contact.dart';
import 'package:get/get.dart';
import 'package:onekitty_admin/services/permission_service.dart';

enum ContactsLoadingState { initial, loading, loaded, error, permissionDenied }

class ContactPickerController extends GetxController {
  final RxList<Contact> selectedContacts = <Contact>[].obs;
  final RxList<Contact> filteredContacts = <Contact>[].obs;
  final RxList<Contact> _allContacts = <Contact>[].obs;
  final Rx<ContactsLoadingState> loadingState = ContactsLoadingState.initial.obs;
  final RxString errorMessage = ''.obs;

  List<Contact> get allContacts => _allContacts;

  @override
  void onInit() {
    super.onInit();
    // Don't load contacts automatically
  }

  /// Load contacts with permission handling
  Future<void> loadContacts(BuildContext context) async {
    if (loadingState.value == ContactsLoadingState.loading) return;

    loadingState.value = ContactsLoadingState.loading;
    errorMessage.value = '';

    try {
      final contacts = await PermissionService.instance.getContactsWithPermission(
        context: context,
        customMessage: 'Access your contacts to easily select people to invite.',
      );

      if (contacts.isEmpty && !PermissionService.instance.contactsPermissionGranted) {
        loadingState.value = ContactsLoadingState.permissionDenied;
      } else {
        _allContacts.assignAll(contacts);
        filteredContacts.assignAll(contacts);
        loadingState.value = ContactsLoadingState.loaded;
      }
    } catch (e) {
      errorMessage.value = e.toString();
      loadingState.value = ContactsLoadingState.error;
    }
  }

  void filterContacts(String query) {
    if (query.isEmpty) {
      filteredContacts.assignAll(allContacts);
    } else {
      filteredContacts.assignAll(
        allContacts.where((contact) =>
            contact.displayName.toLowerCase().contains(query.toLowerCase())
        ).toList(),
      );
    }
  }

  void selectContact(Contact contact) {
    if (!selectedContacts.contains(contact)) {
      selectedContacts.add(contact);
    }
  }

  void removeContact(Contact contact) {
    selectedContacts.remove(contact);
  }

  void clearSelection() {
    selectedContacts.clear();
  }

  bool isSelected(Contact contact) {
    return selectedContacts.contains(contact);
  }

  /// Retry loading contacts
  Future<void> retryLoadContacts(BuildContext context) async {
    loadingState.value = ContactsLoadingState.initial;
    await loadContacts(context);
  }
}