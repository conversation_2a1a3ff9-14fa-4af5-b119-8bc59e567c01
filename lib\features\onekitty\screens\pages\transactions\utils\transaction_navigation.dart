// Transaction Navigation Utilities
// Helper functions for navigating to different transaction types

import 'package:get/get.dart';
import '../views/screens/transaction_page.dart';
import '../models/transaction_type.dart';

class TransactionNavigation {
  /// Navigate to user transactions
  static void toUserTransactions({
    String? userId,
    String? title,
  }) {
    Get.to(() => TransactionPage(
      config: TransactionPageConfig(
        transactionType: TransactionType.user,
        userId: userId,
        title: title ?? 'My Transactions',
        showExportOptions: true,
        showEditOptions: false,
      ),
    ));
  }

  /// Navigate to kitty transactions
  static void toKittyTransactions({
    required int kittyId,
    String? title,
    bool showEditOptions = true,
  }) {
    Get.to(() => TransactionPage(
      config: TransactionPageConfig(
        transactionType: TransactionType.kitty,
        entityId: kittyId,
        title: title ?? 'Kitty Transactions',
        showExportOptions: true,
        showEditOptions: showEditOptions,
      ),
    ));
  }

  /// Navigate to chama transactions
  static void toChamaTransactions({
    required int chamaId,
    String? accountNo,
    String? title,
    bool isFullPage = true,
  }) {
    Get.to(() => TransactionPage(
      config: TransactionPageConfig(
        transactionType: TransactionType.chama,
        entityId: chamaId,
        accountNo: accountNo,
        title: title ?? 'Chama Transactions',
        isFullPage: isFullPage,
        showExportOptions: true,
        showEditOptions: false,
      ),
    ));
  }

  /// Navigate to event transactions
  static void toEventTransactions({
    required int eventId,
    String? title,
  }) {
    Get.to(() => TransactionPage(
      config: TransactionPageConfig(
        transactionType: TransactionType.event,
        entityId: eventId,
        title: title ?? 'Event Transactions',
        showExportOptions: true,
        showEditOptions: false,
      ),
    ));
  }

  /// Navigate to transactions with custom configuration
  static void toTransactionsWithConfig(TransactionPageConfig config) {
    Get.to(() => TransactionPage(config: config));
  }
}

/// Extension methods for easier navigation
extension TransactionNavigationExtension on GetInterface {
  /// Navigate to user transactions
  void toUserTransactions({
    String? userId,
    String? title,
  }) {
    TransactionNavigation.toUserTransactions(
      userId: userId,
      title: title,
    );
  }

  /// Navigate to kitty transactions
  void toKittyTransactions({
    required int kittyId,
    String? title,
    bool showEditOptions = true,
  }) {
    TransactionNavigation.toKittyTransactions(
      kittyId: kittyId,
      title: title,
      showEditOptions: showEditOptions,
    );
  }

  /// Navigate to chama transactions
  void toChamaTransactions({
    required int chamaId,
    String? accountNo,
    String? title,
    bool isFullPage = true,
  }) {
    TransactionNavigation.toChamaTransactions(
      chamaId: chamaId,
      accountNo: accountNo,
      title: title,
      isFullPage: isFullPage,
    );
  }

  /// Navigate to event transactions
  void toEventTransactions({
    required int eventId,
    String? title,
  }) {
    TransactionNavigation.toEventTransactions(
      eventId: eventId,
      title: title,
    );
  }
}

/// Example usage:
/// 
/// // Navigate to user transactions
/// Get.toUserTransactions();
/// 
/// // Navigate to kitty transactions
/// Get.toKittyTransactions(kittyId: 123, title: 'My Kitty Transactions');
/// 
/// // Navigate to chama transactions with account filter
/// Get.toChamaTransactions(
///   chamaId: 456, 
///   accountNo: 'ACC001',
///   title: 'Chama ABC Transactions'
/// );
/// 
/// // Navigate to event transactions
/// Get.toEventTransactions(eventId: 789, title: 'Event XYZ Transactions');
/// 
/// // Custom configuration
/// TransactionNavigation.toTransactionsWithConfig(
///   TransactionPageConfig(
///     transactionType: TransactionType.kitty,
///     entityId: 123,
///     title: 'Custom Kitty View',
///     showExportOptions: false,
///     showEditOptions: true,
///   ),
/// );
