import 'package:flutter/material.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/chama/chama_steps.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/chama/contribute/contribute.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/chama/meetings/meetings.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/chama/members/penalize_members.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/chama/penalties/penalties.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/chama/resources/resources.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/chama/resources/updateR.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/chama/resources/upload_resource.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/chama/settings/settings.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/chama/signatories/add_signatories.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/chama/signatories/sig_transactions.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/chama/signatories/signatories.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/chama/members/beneficiary.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/chama/members/invite.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/chama/members/operations.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/chama/members/members_order.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/chama/tabs/add_group.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/chama/tabs/update_chama.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/chama/viewing_single_chama/viewing_single_chama.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/chama/widgets/create_tabs.dart';

import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/views/screens/contr_error_screen.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/views/screens/contr_kitty_url_screen.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/views/screens/contributing_ktty_screen.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/views/screens/contribution_kitties/contr_kitties_screen.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/views/screens/event_loading_screen.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/views/screens/view_single_event_viewer.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/home/<USER>';
import 'package:onekitty_admin/features/onekitty/screens/pages/transactions/views/screens/transaction_page.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/transactions/models/transaction_type.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/views/screens/sucess_contr_screen.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/views/screens/viewing_single_kitty/viewing_single_kitty_page.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/create_event/create_event_page.dart';

import 'package:onekitty_admin/features/onekitty/screens/pages/home/<USER>/all_sms_screen.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/withdraw/withdaraw_sucess.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/withdraw/withdraw_error.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/home/<USER>/crt_sms_screen.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/home/<USER>/empty_sms_screen.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/home/<USER>/single_sms.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/home/<USER>/success_screen.dart';

import 'package:onekitty_admin/features/onekitty/screens/pages/home/<USER>/entry_page.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/home/<USER>/tawk.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/home/<USER>/topUp_otp.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/home/<USER>/top_up_dialog.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/views/screens/create_kitty/pages/create_kitty.dart';

import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/views/screens/edit_kitty/end_date.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/views/screens/edit_kitty/step1.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/views/screens/edit_kitty/whatsapp_link.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/widgets/success.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/profile/edit_profile.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/profile/merchant/referrals.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/profile/merchant/transactions.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/profile/profile_page.dart';
import 'package:onekitty_admin/features/splash_Screens/splash_screen.dart';

class NavRoutes {
  static const String eventsPage = '/view_events';
  static const String createEvent = '/createEvent';

  static const String homeScreen = '/home_screen';

  static const String crtContributionKittyPage = '/crt_contribution_kitty_page';

  // static const String bottomNavSection = '/bottom_nav_screen';
  static const String splashScreen = '/';
  static const String createAnyKittyScreen = '/create/';

  static const String urlScreen = '/enter_url_screen';
  static const String kittycontributionScreen = '/kitty/:id';
  static const String contrSuccessScreen = '/success_Screen';
  static const String contrErrScreen = '/error_screen';
  static const String myKittiescontribtionScreen = '/my_Kitties_screen';
  static const String createkittyScreen = '/create_kitty_screen';
  static const String viewingsinglekittyScreen = '/view_single_kitty';
  static const String seeAlltranscScreen = '/see_all_transc';
  static const String editEndDatekittyScreen = '/edit_kitty_end_date';
  //static const String confirmPayscreen = '/confirmpayment';
  static const String editKittyscreen = '/editKitty';
  static const String emptyblksmsScreen = '/emptyblksms';
  static const String crtsmsScreen = '/crtsms';
  static const String usertransactions = '/usertransactions';
  //static const String cardpayment = '/cardpayment';

  static const String withdrawPage = '/withdraw/withdraw.dart';

  static const String chatRoom = '/chatbot/chatbot.dart';
  static const String addWhatsappGroup = '/edit_kitty/whatsapp_link.dart';
  static const String tawk = '/tawk/tawk.dart';

  //bulks sms

  static const String withdrawSuccessPage = '/withdraw/withdaraw_sucess';
  static const String withdrawErrorPage = '/withdraw/withdraw_error';
  static const String SmSsent = '/smssuccess';
  static const String mainbulksms = '/bulksmspage';
  static const String singlebulksms = '/singlesmspage';
  static const String topup = '/topup';
  static const String topupOtp = '/topupOTP';
  static const String createSuccess = '/create/success_page';
  static const String chamaTransactionsPage = '/chama/transactions';

  //chama Routes
  static const String chamaStepper = '/crtChamaSteps';
  static const String updateChama = '/updateChama';
  static const String resourcesView = '/reources';
  static const String uploadResource = '/uploadRsrc';
  static const String members = '/members';
  static const String singleChama = '/singlechama';
  static const String addGrouplink = '/grouplink';
  static const String penalties = '/penalties';
  static const String meetings = '/meetings';
  static const String signatories = '/signatories';
  static const String settings = '/settings';
  static const String signatoryTransactiions = '/signatoryTransactiions';
  static const String penalizeMultiple = '/penalizeMultiple';
  static const String viewingSingleChama = '/viewingSingleChama';
  static const String chamaSettings = '/chamaSettings';
  static const String operation = '/operations';
  static const String invite = '/inviteMembers';
  static const String beneficiary = '/benf';
  static const String updateRs = '/updateRs';
  static const String addSignatory = '/addSignatoty';
  static const String profile = '/profile';
  static const String updateProfile = '/updateProfile';
  static const String chamaContributeRoute = '/chama/:id';
  static const String viewsingleEvent = "/events/:username";

//Refferals
  static const String referrals = '/referrals';
  static const String mrntTransac = '/merchantsTransactions';

  static Map<String, WidgetBuilder> routes = {
    createEvent: (context) => const CreateEventPage(),
    splashScreen: (context) => const SplashScreen(),
    eventsPage: (context) {
      return const ViewSingleEventViewer();
    },
    homeScreen: (context) => const HomeScreen(),
    urlScreen: (context) => EnteringUrlForAKittyScreen(),
    kittycontributionScreen: (context) => const ContributingToAKitty(),
    contrSuccessScreen: (context) => SuccessfulContibutionPage(),
    contrErrScreen: (context) => ContibutionErrorScreen(),
    myKittiescontribtionScreen: (context) => const MyKittiesScreen(),
    viewingsinglekittyScreen: (context) => const ViewingSingleKittyScreen(),
    seeAlltranscScreen: (context) => const TransactionPage(
      config: TransactionPageConfig(
        transactionType: TransactionType.kitty,
        title: 'All Transactions',
        showExportOptions: true,
        showEditOptions: true,
      ),
    ),
    createkittyScreen: (context) => const StepOne(),
    editEndDatekittyScreen: (context) => const EndDate(),
    //confirmPayscreen: (context) => ProcessPaymentOtp(),
    editKittyscreen: (context) => const EditKittyDetails(),
    emptyblksmsScreen: (context) => EmptyBulkSmsScreen(),
    crtsmsScreen: (context) => const CrtSmsScreen(),
    usertransactions: (context) => const TransactionPage(
      config: TransactionPageConfig(
        transactionType: TransactionType.user,
        title: 'My Transactions',
        showExportOptions: true,
        showEditOptions: false,
      ),
    ),

    chatRoom: (context) => const ChatbotEntryPage(),
    addWhatsappGroup: (context) => const WhatsAppEditLink(),
    tawk: (context) => const TawkWidget(),
    //cardpayment: (context) => CardPayment(),

    withdrawSuccessPage: (context) => const WithdrawSuccess(),
    withdrawErrorPage: (context) => WithdrawError(),
    SmSsent: (context) => const SmsSentScreen(),
    mainbulksms: (context) => const AllSmsScreen(),
    singlebulksms: (context) => const SingleSmsScreen(),
    topup: (context) => const TopUp(),
    topupOtp: (context) => const TopUpOtp(),
    createSuccess: (context) => const SucessPage(),
    // chamaTransactionsPage:(context) => ChamaTransactionsPage(),
    chamaStepper: (context) => const ChamaStepper(),
    updateChama: (context) => const UpdateChama(),
    resourcesView: (context) => const ResourcesWidget(),
    uploadResource: (context) => const UploadResource(),
    singleChama: (context) => const ViewingSingleChama(),
    addGrouplink: (context) => const AddGroup(),
    penalties: (context) => const PenaltiesPage(),
    meetings: (context) => const MeetingsPage(),
    signatories: (context) => const Signatories(),
    settings: (context) => const ChamaSettings(),
    signatoryTransactiions: (context) => const SignatoryTransactions(),
    
    penalizeMultiple: (context) => const ChamaMembersPage(),
    viewingSingleChama: (context) => const ViewingSingleChama(),
    chamaSettings: (context) => const ChamaSettings(),
    members: (context) => const Operations(),
    operation: (context) => const MembersOrder(),
    invite: (context) => const Invite(),
    beneficiary: (context) => const Beneficiary(),
    updateRs: (context) => const UpdateResource(),
    addSignatory: (context) => const AddSignatories(),
    profile: (context) => const ProfilePage(),
    updateProfile: (context) => const EditProfilePage(),
    chamaContributeRoute: (context) => const ChamaContributePage(),
    referrals: (context) => const Referrals(),
    mrntTransac: (context) => const Transactions(),
    createAnyKittyScreen: (context) => const CreateAnyKittiesPage(),
    viewsingleEvent: (context) => const EventLoadingScreen(),
  };
}
