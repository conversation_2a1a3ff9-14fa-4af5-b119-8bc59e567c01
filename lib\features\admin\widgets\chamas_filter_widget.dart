import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onekitty_admin/features/admin/controllers/chamas_dashboard_controller.dart';
import 'package:intl/intl.dart';

class ChamasFilterWidget extends StatefulWidget {
  final ChamasDashboardController controller;

  const ChamasFilterWidget({
    super.key,
    required this.controller,
  });

  @override
  State<ChamasFilterWidget> createState() => _ChamasFilterWidgetState();
}

class _ChamasFilterWidgetState extends State<ChamasFilterWidget> {
  Timer? _debounce;
  late TextEditingController _chamaIdController;
  late TextEditingController _kittyIdController;
  late TextEditingController _frequencyController;

  @override
  void initState() {
    super.initState();
    _chamaIdController = TextEditingController(text: widget.controller.chamaId.value);
    _kittyIdController = TextEditingController(text: widget.controller.kittyId.value);
    _frequencyController = TextEditingController(text: widget.controller.frequency.value);
  }

  @override
  void dispose() {
    _debounce?.cancel();
    _chamaIdController.dispose();
    _kittyIdController.dispose();
    _frequencyController.dispose();
    super.dispose();
  }

  void _handleTextChange(String value, Function(String) setter) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    
    _debounce = Timer(const Duration(seconds: 1), () {
      setter(value);
      widget.controller.fetchChamas(0);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Filter Chamas',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(Icons.close),
            ),
          ],
        ),
        const SizedBox(height: 24),
        
        // Date Range Filters
        Text(
          'Date Range',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        const SizedBox(height: 12),
        
        Row(
          children: [
            Expanded(
              child: _buildDateField(
                context,
                label: 'Start Date',
                value: widget.controller.startDate.value,
                onTap: () => widget.controller.selectStartDate(context),
                onClear: widget.controller.clearStartDate,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildDateField(
                context,
                label: 'End Date',
                value: widget.controller.endDate.value,
                onTap: () => widget.controller.selectEndDate(context),
                onClear: widget.controller.clearEndDate,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 24),
        
        // Search Filters
        Text(
          'Search Filters',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        const SizedBox(height: 12),
        
        TextField(
          controller: _chamaIdController,
          decoration: const InputDecoration(
            labelText: 'Chama ID',
            hintText: 'Enter specific chama ID',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.savings),
          ),
          keyboardType: TextInputType.number,
          onChanged: (value) => _handleTextChange(
            value,
            (val) => widget.controller.chamaId.value = val,
          ),
        ),
        
        const SizedBox(height: 16),
        
        TextField(
          controller: _kittyIdController,
          decoration: const InputDecoration(
            labelText: 'Kitty ID',
            hintText: 'Enter associated kitty ID',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.account_balance),
          ),
          keyboardType: TextInputType.number,
          onChanged: (value) => _handleTextChange(
            value,
            (val) => widget.controller.kittyId.value = val,
          ),
        ),
        
        const SizedBox(height: 16),
        
        DropdownButtonFormField<String>(
          value: widget.controller.frequency.value.isEmpty ? null : widget.controller.frequency.value,
          decoration: const InputDecoration(
            labelText: 'Frequency',
            hintText: 'Select frequency',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.schedule),
          ),
          items: const [
            DropdownMenuItem(value: 'WEEKLY', child: Text('Weekly')),
            DropdownMenuItem(value: 'MONTHLY', child: Text('Monthly')),
            DropdownMenuItem(value: 'QUARTERLY', child: Text('Quarterly')),
            DropdownMenuItem(value: 'YEARLY', child: Text('Yearly')),
          ],
          onChanged: (value) {
            widget.controller.frequency.value = value ?? '';
            widget.controller.fetchChamas(0);
          },
        ),
        
        const SizedBox(height: 24),
        
        // Admin Action Buttons
        Text(
          'Admin Actions',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        const SizedBox(height: 12),
        
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            _buildActionChip(
              context,
              label: 'Send Funds',
              icon: Icons.send,
              color: Colors.green,
              onTap: () => _showSendFundsDialog(context),
            ),
            _buildActionChip(
              context,
              label: 'Add Penalty',
              icon: Icons.gavel,
              color: Colors.orange,
              onTap: () => _showAddPenaltyDialog(context),
            ),
            _buildActionChip(
              context,
              label: 'Block Chama',
              icon: Icons.block,
              color: Colors.red,
              onTap: () => _showBlockChamaDialog(context),
            ),
            _buildActionChip(
              context,
              label: 'Unblock Chama',
              icon: Icons.check_circle,
              color: Colors.blue,
              onTap: () => _showUnblockChamaDialog(context),
            ),
            _buildActionChip(
              context,
              label: 'View Transactions',
              icon: Icons.list,
              color: Colors.purple,
              onTap: () => _showTransactionsDialog(context),
            ),
            _buildActionChip(
              context,
              label: 'Export Data',
              icon: Icons.download,
              color: Colors.teal,
              onTap: () => _showExportDialog(context),
            ),
          ],
        ),
        
        const SizedBox(height: 24),
        
        // Action Buttons
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () {
                  widget.controller.clearFilters();
                  _chamaIdController.clear();
                  _kittyIdController.clear();
                  _frequencyController.clear();
                },
                icon: const Icon(Icons.clear_all),
                label: const Text('Clear All'),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                icon: const Icon(Icons.check),
                label: const Text('Apply'),
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 16),
        
        // Filter Summary
        Obx(() {
          final activeFilters = _getActiveFilters();
          if (activeFilters.isEmpty) {
            return const SizedBox.shrink();
          }
          
          return Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Theme.of(context).primaryColor.withOpacity(0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Active Filters (${activeFilters.length})',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).primaryColor,
                      ),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 4,
                  children: activeFilters.map((filter) => Chip(
                    label: Text(
                      filter,
                      style: const TextStyle(fontSize: 12),
                    ),
                    backgroundColor: Theme.of(context).primaryColor.withOpacity(0.2),
                    deleteIcon: const Icon(Icons.close, size: 16),
                    onDeleted: () => _removeFilter(filter),
                  )).toList(),
                ),
              ],
            ),
          );
        }),
      ],
    );
  }

  Widget _buildDateField(
    BuildContext context, {
    required String label,
    required String value,
    required VoidCallback onTap,
    required VoidCallback onClear,
  }) {
    final displayValue = value.isNotEmpty
        ? DateFormat('dd/MM/yyyy').format(DateTime.parse(value))
        : '';

    return InkWell(
      onTap: onTap,
      child: InputDecorator(
        decoration: InputDecoration(
          labelText: label,
          border: const OutlineInputBorder(),
          suffixIcon: value.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear, size: 18),
                  onPressed: onClear,
                )
              : const Icon(Icons.calendar_today),
        ),
        child: Text(
          displayValue.isEmpty ? 'Select date' : displayValue,
          style: TextStyle(
            color: displayValue.isEmpty
                ? Theme.of(context).hintColor
                : Theme.of(context).textTheme.bodyLarge?.color,
          ),
        ),
      ),
    );
  }

  Widget _buildActionChip(
    BuildContext context, {
    required String label,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return ActionChip(
      label: Text(label),
      avatar: Icon(icon, size: 18, color: color),
      onPressed: onTap,
      backgroundColor: color.withOpacity(0.1),
      side: BorderSide(color: color.withOpacity(0.3)),
    );
  }

  List<String> _getActiveFilters() {
    final filters = <String>[];
    
    if (widget.controller.startDate.value.isNotEmpty) {
      final date = DateFormat('dd/MM/yyyy').format(
        DateTime.parse(widget.controller.startDate.value),
      );
      filters.add('Start: $date');
    }
    
    if (widget.controller.endDate.value.isNotEmpty) {
      final date = DateFormat('dd/MM/yyyy').format(
        DateTime.parse(widget.controller.endDate.value),
      );
      filters.add('End: $date');
    }
    
    if (widget.controller.chamaId.value.isNotEmpty) {
      filters.add('Chama ID: ${widget.controller.chamaId.value}');
    }
    
    if (widget.controller.kittyId.value.isNotEmpty) {
      filters.add('Kitty ID: ${widget.controller.kittyId.value}');
    }
    
    if (widget.controller.frequency.value.isNotEmpty) {
      filters.add('Frequency: ${widget.controller.frequency.value}');
    }
    
    return filters;
  }

  void _removeFilter(String filter) {
    if (filter.startsWith('Start:')) {
      widget.controller.clearStartDate();
    } else if (filter.startsWith('End:')) {
      widget.controller.clearEndDate();
    } else if (filter.startsWith('Chama ID:')) {
      widget.controller.chamaId.value = '';
      _chamaIdController.clear();
    } else if (filter.startsWith('Kitty ID:')) {
      widget.controller.kittyId.value = '';
      _kittyIdController.clear();
    } else if (filter.startsWith('Frequency:')) {
      widget.controller.frequency.value = '';
      _frequencyController.clear();
    }
    
    widget.controller.fetchChamas(0);
  }

  void _showSendFundsDialog(BuildContext context) {
    // Implementation for send funds dialog
    Get.snackbar(
      'Send Funds',
      'Send funds functionality will be implemented',
      snackPosition: SnackPosition.bottom,
    );
  }

  void _showAddPenaltyDialog(BuildContext context) {
    // Implementation for add penalty dialog
    Get.snackbar(
      'Add Penalty',
      'Add penalty functionality will be implemented',
      snackPosition: SnackPosition.bottom,
    );
  }

  void _showBlockChamaDialog(BuildContext context) {
    // Implementation for block chama dialog
    Get.snackbar(
      'Block Chama',
      'Block chama functionality will be implemented',
      snackPosition: SnackPosition.bottom,
    );
  }

  void _showUnblockChamaDialog(BuildContext context) {
    // Implementation for unblock chama dialog
    Get.snackbar(
      'Unblock Chama',
      'Unblock chama functionality will be implemented',
      snackPosition: SnackPosition.bottom,
    );
  }

  void _showTransactionsDialog(BuildContext context) {
    // Implementation for view transactions dialog
    Get.snackbar(
      'View Transactions',
      'View transactions functionality will be implemented',
      snackPosition: SnackPosition.bottom,
    );
  }

  void _showExportDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export Data'),
        content: const Text('Select export format:'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              widget.controller.exportData('csv');
            },
            child: const Text('CSV'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              widget.controller.exportData('excel');
            },
            child: const Text('Excel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              widget.controller.exportData('pdf');
            },
            child: const Text('PDF'),
          ),
        ],
      ),
    );
  }
}
