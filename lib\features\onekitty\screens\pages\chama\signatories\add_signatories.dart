import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:onekitty_admin/configs/country_specifics.dart';
import 'package:onekitty_admin/features/onekitty/widgets/custom_international_phone_input.dart';
import 'package:onekitty_admin/features/onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty_admin/helpers/colors.dart';
import 'package:onekitty_admin/helpers/show_snack_bar.dart';
import 'package:onekitty_admin/models/chama/add_signatory_request.dart';
import 'package:onekitty_admin/nav_routes/nav_routes.dart';
import 'package:onekitty_admin/features/onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty_admin/utils/size_config.dart';

import '../../../../../../../utils/utils_exports.dart';

class AddSignatories extends StatefulWidget {
  const AddSignatories({super.key});

  @override
  State<AddSignatories> createState() => _AddSignatoriesState();
}

class _AddSignatoriesState extends State<AddSignatories> {
  final TextEditingController whatsappNumberController =
      TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController searchController = TextEditingController();
  String? _dropdownValue;
  final ChamaDataController chamaDataController =
      Get.put(ChamaDataController());
  final ChamaController chamaController = Get.put(ChamaController());
  PhoneNumber num = CountryConfig.phoneNumber;
  String myPhone = "";
  String? phoneNumber;
  String? name;
  int? memberID;
  final formKey = GlobalKey<FormState>();
  List filteredMembers = [];

  @override
  void initState() {
    filteredMembers = chamaController.chamaMembers;
    searchController.clear();
    super.initState();
  }

  @override
  void dispose() {
    whatsappNumberController.dispose();
    emailController.dispose();
    searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      //appBar: buildAppBar(context),
      body: SafeArea(
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 12),
          child: Form(
            key: formKey,
            child: SingleChildScrollView(
              child: Column(
                children: [
                  const RowAppBar(),
                  Text("select_signatories".tr,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold, fontSize: 22)),
                  SizedBox(
                    height: 5.h,
                  ),
                  Text(
                    "add_signatories_to_chama_group".tr,
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(
                    height: 15.h,
                  ),
                  Container(
                    decoration: BoxDecoration(
                        border: Border.all(color: AppColors.blueButtonColor),
                        borderRadius: BorderRadius.circular(12)),
                    child: ListTile(
                      onTap: () {
                        showBottomAddSheet();
                      },
                      title: Text(
                          "${name ?? "select_contact".tr}\n${phoneNumber ?? ""}"),
                      trailing: IconButton(
                          onPressed: () {
                            showBottomAddSheet();
                          },
                          icon: const Icon(Icons.arrow_drop_down_rounded)),
                    ),
                  ),
                  const SizedBox(
                    height: 15,
                  ),
                  Container(
                    decoration: BoxDecoration(
                        border: Border.all(color: AppColors.blueButtonColor),
                        borderRadius: BorderRadius.circular(12)),
                    child: ListTile(
                      title: Text(
                          _dropdownValue ?? "select_a_notification_type".tr),
                      trailing: DropdownButton(
                          underline: const SizedBox(),
                          items: [
                            DropdownMenuItem(
                              value: "SMS",
                              child: Text("sms".tr),
                            ),
                            DropdownMenuItem(
                              value: "WHATSAPP",
                              child: Text("whatsapp".tr),
                            ),
                            DropdownMenuItem(
                              value: "EMAIL",
                              child: Text("email".tr),
                            ),
                            DropdownMenuItem(
                              value: "ALL",
                              child: Text("all".tr),
                            ),
                          ],
                          //value: _dropdownValue,
                          onChanged: dropdownCallback),
                    ),
                  ),
                  const SizedBox(
                    height: 12,
                  ),
                  if (_dropdownValue == "EMAIL" || _dropdownValue == "ALL")
                    CustomTextField(
                      controller: emailController,
                      hintText: "enter_email".tr,
                      labelText: "email".tr,
                    ),
                  if (_dropdownValue == "WHATSAPP" || _dropdownValue == "ALL")
                    CustomInternationalPhoneInput(
                      onInputChanged: (
                        num,
                      ) {
                        setState(() {
                          myPhone = num.phoneNumber!;
                        });
                      },
                      initialPhoneNumber: num.phoneNumber,
                    ),
                  SizedBox(
                    height: 12.h,
                  ),
                  Obx(
                    () => CustomKtButton(
                        isLoading: chamaController.isSignatoryLoading.isTrue,
                        onPress: () async {
                          await addSignatory();
                        },
                        btnText: "add_signatory".tr),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void dropdownCallback(String? selectedValue) {
    if (selectedValue is String) {
      setState(() {
        _dropdownValue = selectedValue;
      });
    }
  }

  void selectedContact(String? selectedName, String? selectedPhone) {
    if (selectedName is String) {
      setState(() {
        name = selectedName;
        phoneNumber = selectedPhone;
      });
    }
  }

  showBottomAddSheet() {
    showModalBottomSheet(
        isScrollControlled: true,
        context: context,
        builder: (context) {
          return DraggableScrollableSheet(
              maxChildSize: 0.97,
              initialChildSize: 0.7,
              expand: false,
              builder: (context, scrollController) {
                return Container(
                  decoration:
                      BoxDecoration(borderRadius: BorderRadius.circular(20)),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                  child: Column(
                    children: [
                      TextFormField(
                        controller: searchController,
                        decoration: InputDecoration(
                            hintText: "search_members".tr,
                            suffixIcon: IconButton(
                                onPressed: () {},
                                icon: const Icon(Icons.search))),
                        onChanged: (value) {
                          filteredMembers = chamaController.chamaMembers
                              .where((memeber) => memeber.firstName!
                                  .toLowerCase()
                                  .contains(value.toLowerCase()))
                              .toList();
                          setState(() {});
                        },
                      ),
                      Expanded(
                        child: GetX(
                            init: ChamaController(),
                            initState: (state) {
                              Future.delayed(Duration.zero, () async {
                                try {
                                  await state.controller?.getChamaMembers(
                                    chamaId: chamaDataController
                                        .chama.value.chama?.id,
                                  );
                                  chamaController.reset();
                                } catch (e) {
                                  throw e;
                                }
                              });
                            },
                            builder: (ChamaController chamaController) {
                              if (chamaController.isloadingChama.isTrue) {
                                return SizedBox(
                                  height: SizeConfig.screenHeight * .33,
                                  child: Center(
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        SpinKitDualRing(
                                          color: ColorUtil.blueColor,
                                          lineWidth: 4.sp,
                                          size: 40.0.sp,
                                        ),
                                        Text(
                                          "loading".tr,
                                          style: const TextStyle(
                                            color: Colors.white,
                                          ),
                                        )
                                      ],
                                    ),
                                  ),
                                );
                              } else if (chamaController.chamaMembers.isEmpty) {
                                return Text("no_members_added_yet".tr);
                              } else if (filteredMembers.isEmpty) {
                                return Padding(
                                  padding: EdgeInsets.only(top: 15.h),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      Text("member_not_found".tr),
                                    ],
                                  ),
                                );
                              } else if (filteredMembers.isNotEmpty) {
                                return ListView.separated(
                                    controller: chamaController.controller,
                                    itemBuilder: (context, index) {
                                      final member = filteredMembers[index];
                                      final fullName =
                                          "${member.firstName} ${member.secondName}";
                                      final phone = "${member.phoneNumber}";
                                      final memberId = member.id;
                                      return ListTile(
                                        onTap: () {
                                          selectedContact(fullName, phone);
                                          memberID = memberId;
                                          Navigator.pop(context);
                                        },
                                        title: Text(fullName),
                                        subtitle: Text(phone),
                                        trailing: ElevatedButton.icon(
                                            onPressed: () {
                                              selectedContact(fullName, phone);
                                              memberID = memberId;
                                              Navigator.pop(context);
                                            },
                                            icon: const Icon(Icons.add),
                                            label: Text("add".tr)),
                                      );
                                    },
                                    separatorBuilder: (context, index) {
                                      return const Divider();
                                    },
                                    itemCount: filteredMembers.length);
                              }
                              return Text("no_members_added_yet".tr);
                            }),
                      ),
                    ],
                  ),
                );
              });
        });
  }

  addSignatory() async {
    if (formKey.currentState!.validate()) {
      SignatoryRequest request = SignatoryRequest(
        chamaId: chamaDataController.chama.value.chama?.id,
        memberId: memberID,
        phoneNumber: phoneNumber,
        notificationType: _dropdownValue,
        email: emailController.text.trim(),
        whatsAppNumber: myPhone != "" ? myPhone.substring(1) : null,
      );
      bool res = await chamaController.addSignatory(request: request);
      if (res) {
        if (!mounted) return;
        Snack.show(res, chamaController.apiMessage.string);
        Get.offAndToNamed(NavRoutes.signatories);
      } else {
        if (!mounted) return;
        Snack.show(res, chamaController.apiMessage.string);
      }
    }
  }
}
