import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onekitty_admin/features/admin/api_urls.dart';
import 'package:onekitty_admin/models/events/events_model.dart';
import 'package:onekitty_admin/services/http_service.dart';

class EventsAdminService {
  final HttpService _apiProvider = HttpService();
  final Logger _logger = Logger();

  /// Fetch all events with pagination and filters
  Future<Map<String, dynamic>> fetchEvents({
    int page = 0,
    int size = 15,
    String? search,
    String? phoneNumber,
    String? kittyId,
    String? frequency,
    String? startDate,
    String? endDate,
  }) async {
    try {
      // Build query parameters
      final Map<String, dynamic> queryParams = {
        'page': page,
        'size': size,
      };

      if (search != null && search.isNotEmpty) {
        queryParams['search'] = search;
      }
      if (phoneNumber != null && phoneNumber.isNotEmpty) {
        queryParams['phone_number'] = phoneNumber;
      }
      if (kittyId != null && kittyId.isNotEmpty) {
        queryParams['kitty_id'] = kittyId;
      }
      if (frequency != null && frequency.isNotEmpty) {
        queryParams['frequency'] = frequency;
      }
      if (startDate != null && startDate.isNotEmpty) {
        queryParams['start_date'] = startDate;
      }
      if (endDate != null && endDate.isNotEmpty) {
        queryParams['end_date'] = endDate;
      }

      // Build URL with query parameters
      final queryString = queryParams.entries
          .map((e) => '${e.key}=${Uri.encodeComponent(e.value.toString())}')
          .join('&');
      
      final url = '${AdminApiUrls.getAllEvents}?$queryString';

      final response = await _apiProvider.request(
        method: Method.GET,
        url: url,
      );

      if (response.data['status'] ?? false) {
        final data = response.data['data'];
        final items = data['items'] as List;
        
        final events = items
            .map((item) => Event.fromJson(item))
            .toList();

        return {
          'success': true,
          'events': events,
          'pagination': {
            'page': data['page'] ?? page,
            'size': data['size'] ?? size,
            'total_pages': data['total_pages'] ?? 1,
            'total_items': data['total_items'] ?? 0,
            'has_next': !(data['last'] ?? true),
            'has_previous': !(data['first'] ?? true),
            'is_last': data['last'] ?? true,
            'is_first': data['first'] ?? true,
          },
          'message': response.data['message'] ?? 'Events fetched successfully',
        };
      } else {
        return {
          'success': false,
          'events': <Event>[],
          'message': response.data['message'] ?? 'Failed to fetch events',
        };
      }
    } catch (e) {
      _logger.e('Error fetching events: $e');
      return {
        'success': false,
        'events': <Event>[],
        'message': 'An error occurred while fetching events: ${e.toString()}',
      };
    }
  }

  /// Fetch a single event by ID
  Future<Map<String, dynamic>> fetchEventById(int eventId) async {
    try {
      final response = await _apiProvider.request(
        method: Method.GET,
        url: '${AdminApiUrls.getEventById}$eventId',
      );

      if (response.data['status'] ?? false) {
        final eventData = response.data['data']['event'];
        final event = Event.fromJson(eventData);

        return {
          'success': true,
          'event': event,
          'message': response.data['message'] ?? 'Event fetched successfully',
        };
      } else {
        return {
          'success': false,
          'event': null,
          'message': response.data['message'] ?? 'Failed to fetch event',
        };
      }
    } catch (e) {
      _logger.e('Error fetching event by ID: $e');
      return {
        'success': false,
        'event': null,
        'message': 'An error occurred while fetching event: ${e.toString()}',
      };
    }
  }

  /// Update event status (block/unblock)
  Future<Map<String, dynamic>> updateEventStatus({
    required int eventId,
    required String status, // "ACTIVE", "BLOCKED", "ENDED", "PENDING REVIEW"
    String? description,
    List<Map<String, dynamic>>? media,
  }) async {
    try {
      final params = {
        'event_id': eventId,
        'event_status': status,
      };

      if (description != null && description.isNotEmpty) {
        params['description'] = description;
      }

      if (media != null && media.isNotEmpty) {
        params['media'] = media;
      }

      final response = await _apiProvider.request(
        method: Method.POST,
        url: AdminApiUrls.updateEventStatus,
        params: params,
      );

      return {
        'success': response.data['status'] ?? false,
        'message': response.data['message'] ?? 'Status update failed',
      };
    } catch (e) {
      _logger.e('Error updating event status: $e');
      return {
        'success': false,
        'message': 'An error occurred while updating event status: ${e.toString()}',
      };
    }
  }

  /// Block event with reason and media
  Future<Map<String, dynamic>> blockEvent({
    required int eventId,
    required String reason,
    List<Map<String, dynamic>>? media,
  }) async {
    return updateEventStatus(
      eventId: eventId,
      status: 'BLOCKED',
      description: reason,
      media: media,
    );
  }

  /// Unblock event
  Future<Map<String, dynamic>> unblockEvent(int eventId) async {
    return updateEventStatus(
      eventId: eventId,
      status: 'ACTIVE',
    );
  }

  /// Get event transactions
  Future<Map<String, dynamic>> fetchEventTransactions({
    required int eventId,
    int page = 0,
    int size = 15,
  }) async {
    try {
      final url = '${AdminApiUrls.getEventTransactions}?event_id=$eventId&page=$page&size=$size';

      final response = await _apiProvider.request(
        method: Method.GET,
        url: url,
      );

      if (response.data['status'] ?? false) {
        final data = response.data['data'];
        
        return {
          'success': true,
          'transactions': data['transactions'] ?? [],
          'pagination': {
            'page': data['page'] ?? page,
            'size': data['size'] ?? size,
            'total_pages': data['total_pages'] ?? 1,
            'total_items': data['total_items'] ?? 0,
          },
          'message': response.data['message'] ?? 'Transactions fetched successfully',
        };
      } else {
        return {
          'success': false,
          'transactions': [],
          'message': response.data['message'] ?? 'Failed to fetch transactions',
        };
      }
    } catch (e) {
      _logger.e('Error fetching event transactions: $e');
      return {
        'success': false,
        'transactions': [],
        'message': 'An error occurred while fetching transactions: ${e.toString()}',
      };
    }
  }

  /// Export events data
  Future<Map<String, dynamic>> exportEventsData({
    required String format, // 'csv', 'excel', 'pdf'
    Map<String, dynamic>? filters,
  }) async {
    try {
      final response = await _apiProvider.request(
        method: Method.POST,
        url: AdminApiUrls.exportData,
        params: {
          'type': 'events',
          'format': format,
          'filters': filters ?? {},
        },
      );

      return {
        'success': response.data['status'] ?? false,
        'download_url': response.data['data']?['download_url'],
        'message': response.data['message'] ?? 'Export failed',
      };
    } catch (e) {
      _logger.e('Error exporting events data: $e');
      return {
        'success': false,
        'message': 'An error occurred while exporting data: ${e.toString()}',
      };
    }
  }
}
