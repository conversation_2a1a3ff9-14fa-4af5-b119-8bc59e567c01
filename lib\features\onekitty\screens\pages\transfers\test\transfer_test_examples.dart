// Transfer Test Examples
// Example usage and test cases for the unified transfer system

import 'package:flutter_test/flutter_test.dart';
import '../transfers_exports.dart';

void main() {
  group('Transfer System Tests', () {
    test('TransferType enum should have correct display names', () {
      expect(TransferType.event.displayName, 'Event Transfer');
      expect(TransferType.chama.displayName, 'Chama Transfer');
      expect(TransferType.penalty.displayName, 'Penalty Transfer');
    });

    test('TransferType should have correct approval requirements', () {
      expect(TransferType.event.requiresSignatoryApproval, false);
      expect(TransferType.chama.requiresSignatoryApproval, true);
      expect(TransferType.penalty.requiresSignatoryApproval, true);
    });

    test('TransferPageConfig should create correctly', () {
      final config = const TransferPageConfig(
        transferType: TransferType.event,
        entityId: 123,
        title: 'Test Transfer',
      );

      expect(config.transferType, TransferType.event);
      expect(config.entityId, 123);
      expect(config.title, 'Test Transfer');
      expect(config.isPenaltyTransfer, false);
    });

    test('TransferPageConfig copyWith should work correctly', () {
      final original = const TransferPageConfig(
        transferType: TransferType.event,
        entityId: 123,
      );

      final copied = original.copyWith(
        transferType: TransferType.chama,
        isPenaltyTransfer: true,
      );

      expect(copied.transferType, TransferType.chama);
      expect(copied.entityId, 123); // Should remain unchanged
      expect(copied.isPenaltyTransfer, true);
    });
  });
}

// Example usage patterns
class TransferExamples {
  // Event transfer example
  static void eventTransferExample() {
    // Simple event transfer
    TransferNavigation.toEventTransfer(
      eventId: 123,
      title: 'Concert Ticket Refund',
    );
  }

  // Chama transfer example
  static void chamaTransferExample() {
    // Regular chama transfer
    TransferNavigation.toChamaTransfer(
      chamaId: 456,
      title: 'Monthly Disbursement',
    );
  }

  // Penalty transfer example
  static void penaltyTransferExample() {
    // Transfer from penalty kitty
    TransferNavigation.toPenaltyTransfer(
      chamaId: 456,
      title: 'Penalty Refund',
    );
  }

  // Custom configuration example
  static void customConfigExample() {
    TransferNavigation.toTransferWithConfig(
      const TransferPageConfig(
        transferType: TransferType.chama,
        entityId: 789,
        title: 'Emergency Fund Transfer',
        isPenaltyTransfer: false,
        showApprovalFlow: true,
      ),
    );
  }
}