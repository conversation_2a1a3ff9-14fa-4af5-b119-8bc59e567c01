// ignore_for_file: must_be_immutable

import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:onekitty_admin/helpers/context_extension.dart';
import 'package:onekitty_admin/helpers/extensions/text_styles.dart';

class CustomButton extends StatelessWidget {
  String? text;
  Function? onPressed;
  final double? buttonHeight;
  final Color? buttonColor;
  CustomButton({
    super.key,
    this.text,
    this.onPressed,
    this.buttonHeight,
    this.buttonColor,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      curve: Curves.easeIn,
      decoration: BoxDecoration(
        // gradient: const LinearGradient(colors: [
        //   SasapayPalette.primaryColorDeep,
        //   SasapayPalette.accentColor,
        //   SasapayPalette.primaryColor,
        // ]),
        color: buttonColor ?? Theme.of(context).primaryColor,

        borderRadius: const BorderRadius.all(Radius.circular(10)),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      margin: const EdgeInsets.symmetric(vertical: 10),
      height: buttonHeight ?? 60,
      child: TextButton(
        onPressed: onPressed!(),
        child: Text(
          text ?? "",
        ),
      ),
    );
  }
}

class CustomKtButton extends StatelessWidget {
  final String btnText;
  final VoidCallback? onPress;
  final bool isLoading;
  final double? width;
  final double? height;
  final bool? isActive;
  final EdgeInsets? margin;
  final Alignment? alignment;

  const CustomKtButton(
      {super.key,
      required this.btnText,
      this.onPress,
      this.width,
      this.height,
      this.margin,
      this.alignment,
      this.isActive = true,
      this.isLoading = false,
      String? text,
      Null Function()? onPressed});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width ?? double.infinity,
      height: height ?? 50.sp,
      child: ElevatedButton(
        style: ButtonStyle(
          backgroundColor: isActive != null && isActive!
              ? WidgetStateProperty.all(Theme.of(context).primaryColor)
              : WidgetStateProperty.all(
                  Theme.of(context).colorScheme.outline,
                ),
        ),
        onPressed: isLoading
            ? null
            : () {
                context.hideKeyboard();

                onPress?.call();
              },
        child: isLoading
            ? const SpinKitDualRing(
                color: Colors.white,
                size: 40.0,
              )
            : AutoSizeText(
                btnText,
                style: TextStyle(
                    fontSize: 15.sp,
                    color: Colors.white,
                    fontWeight: FontWeight.bold),
              ),
      ),
    );
  }
}

class ElevateButton extends StatelessWidget {
  final VoidCallback onPress;
  final bool isLoading;
  final String text;
  const ElevateButton(
      {super.key,
      required this.onPress,
      required this.text,
      this.isLoading = false});

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
        onPressed: isLoading
            ? null
            : () {
                context.hideKeyboard();
                onPress.call();
              },
        child: isLoading
            ? const SpinKitDualRing(
                color: Colors.white,
                size: 40.0,
              )
            : AutoSizeText(
                text,
                style: context.dividerTextLarge?.copyWith(color: Colors.white),
              ));
  }
}
