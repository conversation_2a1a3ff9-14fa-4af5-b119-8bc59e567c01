import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:flutter_quill/quill_delta.dart';
import 'package:flutter_quill_delta_from_html/flutter_quill_delta_from_html.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:onekitty_admin/configs/country_specifics.dart';
import 'package:onekitty_admin/features/onekitty/controllers/contribute_controller.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/controllers/edit_event_controller.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/controllers/kitty_controller.dart';
import 'package:onekitty_admin/features/onekitty/controllers/user_controller.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/controllers/user_ktty_controller.dart';
import 'package:onekitty_admin/helpers/colors.dart';
import 'package:onekitty_admin/helpers/context_extension.dart';
import 'package:onekitty_admin/helpers/show_toast.dart';

import 'package:onekitty_admin/models/kitty_payload.dart';
import 'package:onekitty_admin/models/user_kitties_model.dart'; 
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/views/screens/kitty_settings.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/widgets/row_widget.dart';
import 'package:onekitty_admin/features/onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty_admin/utils/common_strings.dart';
import 'package:onekitty_admin/utils/formatted_currency.dart';
import 'package:onekitty_admin/utils/iswysiwyg.dart';
import 'package:onekitty_admin/utils/my_button.dart'; 
import '../../../../../../../../../utils/show_cached_network_image.dart';

class EditKittyDetails extends StatefulWidget {
  final UserKitty? kitty;

  const EditKittyDetails({
    this.kitty,
    super.key,
  });

  @override
  State<EditKittyDetails> createState() => _EditKittyDetailsState();
}

class _EditKittyDetailsState extends State<EditKittyDetails>
    with TickerProviderStateMixin {
  final formKey = GlobalKey<FormState>();
  //KittyController singleKitty = Get.put(KittyController());
  ContributeController singleKitty = Get.put(ContributeController());
  DataController dataController = Get.put(DataController());
  final nameController = TextEditingController();
  final targetController = TextEditingController();
  
  final beneficiaryController = TextEditingController();
  final descriptionController = TextEditingController();
  final accountController = TextEditingController();
  final accountrefController = TextEditingController();
  final endDateController = TextEditingController();
  // late TabController tabController;
  KittyController kittyController = Get.put(KittyController());
  String? selectedChannel = "M-Pesa";
  String? phoneNum;
  bool isLoading = false;
  int? kittyId;
  DateTime? endDate;
  var params = Get.parameters;
  List tillPaybil = ["Mpesa Paybill", "Buy Goods(Till)"];
  PhoneNumber num = CountryConfig.phoneNumber;
  String myPhone = "";
  String selectedtillPaybil = "Mpesa Paybill";
  final box = GetStorage();
  //UserController userController = Get.find();
  final user = UserController().getLocalUser();
  final ContributeController contributeController =
      Get.find<ContributeController>();
  final eventController = Get.find<EditEventController>();
  final media = Get.find<ContributeController>().kittyMedia;
   
  late QuillController kittyDesc;
   

  final showSettings = false.obs;

  setValues() async {
    String? title = dataController.kitty.value.kitty?.title ?? "";
    kittyId = dataController.kitty.value.kitty?.iD;
    DateTime? endDateGot = dataController.kitty.value.kitty?.endDate;

    endDateController.text =
        DateFormat('d MMM yyyy HH : mm a').format(endDateGot ?? DateTime.now());

    nameController.text = title;
    targetController.text = (dataController.kitty.value.kitty?.limit ?? "").toString();
    endDate = endDateGot;
    String description = dataController.kitty.value.kitty?.description ?? "";
    Document document;
    if (isWysiwygFormat(description)) {
      document = Document.fromJson(HtmlToDelta().convert(description).toJson());
    } else if (description.startsWith('[') && description.endsWith(']')) {
      // JSON format
      document = Document.fromJson(jsonDecode(description));
    } else {
      // Plain text
      document = Document.fromDelta(Delta()..insert(description)..insert('\n'));
    }
    var selection = const TextSelection.collapsed(offset: 0);
    kittyDesc = QuillController(
        document: document, selection: selection);
    String? desc =
        extractText(dataController.kitty.value.kitty?.description ?? "");
    descriptionController.text = desc;
    
    // Prefill categories
    await _prefillCategories();
    setState(() {});
  }

  Future<void> _prefillCategories() async {
    await kittyController.getKittyCategories();
    final kittyCategories = dataController.kitty.value.kitty?.categories;
    if (kittyCategories != null && kittyCategories.isNotEmpty) {
      // Extract category IDs from the kitty's categories
      final categoryIds = kittyCategories.map((cat) => cat.id).where((id) => id != null).cast<int>().toList();
      
      // Find matching categories from the available categories and add them to selected
      for (var categoryId in categoryIds) {
        final category = kittyController.kittyCategories.firstWhereOrNull(
          (cat) => cat.id == categoryId,
        );
        if (category != null && !kittyController.selectedKittyCategoryModels.any((c) => c.id == category.id)) {
          kittyController.selectedKittyCategoryModels.add(category);
          kittyController.selectedKittyCategories.add(category.id!);
        }
      }
    }
  }

  late TabController pagetabController;

  @override
  void initState() {
    pagetabController = TabController(length: 2, vsync: this, initialIndex: 0);
    kittyController.selectedKittyCategoryModels.clear();
    kittyController.selectedKittyCategories.clear();
    // tabController = TabController(length: 3, vsync: this, initialIndex: 0);
    // if (kDebugMode) {}
    pagetabController.addListener(() {
      showSettings.value = pagetabController.index == 1;
    });
    setValues();

    super.initState();
  }

  @override
  void dispose() {
    kittyController.selectedKittyCategoryModels.clear();
    kittyController.selectedKittyCategories.clear();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text("edit_kitty_details".tr),
          bottom: TabBar(
  controller: pagetabController,
  labelColor: Colors.white,
  unselectedLabelColor: Colors.white,
  indicatorColor: Colors.white,
  indicatorWeight: 2.0,
  dividerColor: Colors.transparent,
  tabs: [
    Tab(text: "details_tab".tr),
    Tab(text: "settings_tab".tr),
  ],
)
        ),
        body: TabBarView(
          controller: pagetabController,
          children: [
            Container(
              margin: const EdgeInsets.all(20),
              child: Form(
                key: formKey,
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Text(
                      //   "Edit Kitty Details",
                      //   style: context.titleText,
                      // ),
                      SingleLineRow(
                        text: "Kitty name",
                        popup: KtStrings.kittyName,
                      ),
                      CustomTextField(
                        isRequired: true,
                        labelText: "enter_kitty_name_label".tr,
                        hintText: "update_kitty_name_hint".tr,
                        controller: nameController,
                        validator: (value) {
                          if (value!.isEmpty) {
                            return "enter_kitty_title_validation".tr;
                          } else if (value.length < 5) {
                            return "kitty_name_length_validation_edit".tr;
                          } else {
                            return null;
                          }
                        },
                      ),
                      SingleLineRow(
                        text: "kitty_description".tr,
                        popup: KtStrings.kittyDescription,
                      ),
                   
            Padding(
              padding: const EdgeInsets.all(4.0),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8.0),
                  border: Border.all(color: Colors.grey.shade400),
                ),
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 8),
                    QuillSimpleToolbar(
                      config: const QuillSimpleToolbarConfig(
                        multiRowsDisplay: false,
                      
                        
                      ),controller: kittyDesc,
                    ),
                    const SizedBox(height: 15),
                   QuillEditor.basic(controller: kittyDesc,
                      config: QuillEditorConfig(
                        placeholder: "event_description_placeholder".tr,
                        
                        // readOnly: false,
                        autoFocus: false,
                        enableInteractiveSelection:
                            true, // Enable interactive selection to allow text editing

                      
                      ),
                    ),
                    const SizedBox(height: 8),
                  ],
                ),
              ),
            ), 
                SingleLineRow(
                        text: "target_amount_optional".tr,
                        popup: 'target_amount_tooltip'.tr,
                      ),
                       CustomTextField(
          controller: targetController,
          hintText: "amount_hint_example".tr,
          labelText: "amount_label".tr,
          showNoKeyboard: true,
          
          validator: (p0) {
            // if (p0!.isEmpty) {
            //   return "This field cannot be empty";
            // }
            return null;
          },
        ),
        SingleLineRow(
          text: "kitty_category".tr,
          popup: 'select_category_tooltip'.tr,
        ),
        SizedBox(height: 8.h),
        GetX<KittyController>(
            init: KittyController(),
            initState: (state) async {
              await state.controller?.getKittyCategories();
            },
            builder: (controller) {
              if (controller.isLoadingKittyCategories.isTrue) {
                return Container(
                  height: 55.h,
                  padding: const EdgeInsets.all(8),
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Colors.white70,
                    border: Border.all(color: Colors.grey, width: 0.5),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child:  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('select_category'.tr),
                      const CircularProgressIndicator()
                    ],
                  ),
                );
              } else if (controller.kittyCategories.isEmpty) {
                return Container(
                  height: 55.h,
                  padding: const EdgeInsets.all(8),
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Colors.white70,
                    border: Border.all(color: Colors.grey, width: 0.5),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Center(child: Text('no_categories_found'.tr)),
                );
              } else {
                return GestureDetector(
                  onTap: () => _showCategoryBottomSheet(context, controller),
                  child: Container(
                    height: 55.h,
                    padding: const EdgeInsets.all(12),
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: Colors.white70,
                      border: Border.all(color: Colors.grey, width: 0.5),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Obx(() => controller.selectedKittyCategoryModels.isEmpty
                              ? Text('select_categories_label'.tr)
                              : Text(
                                  controller.selectedKittyCategoryModels
                                      .map((c) => c.name)
                                      .join(', '),
                                  overflow: TextOverflow.ellipsis,
                                )),
                        ),
                        const Icon(Icons.arrow_drop_down),
                      ],
                    ),
                  ),
                );
              }
            }),
        SizedBox(height: 16.h),
                      SingleLineRow(
                        text: "kitty_media".tr,
                        popup:
                            'media_tooltip'.tr,
                      ),
                      Align(
                        alignment: Alignment.centerLeft,
                        child: SizedBox(
                            height: 80.h,
                            child: Obx(
                              () => ListView.builder(
                                  itemCount: media.length + 1,
                                  scrollDirection: Axis.horizontal,
                                  shrinkWrap: true,
                                  itemBuilder: (context, index) {
                                    if (index == media.length) {
                                      return InkWell(
                                        onTap: () async {
                                          if (kittyController
                                              .isUploadingImage.value) {
                                            return;
                                          }
                                          kittyController.pickImage(
                                              context: context,
                                              kittyId: dataController
                                                      .kitty.value.kitty?.iD ??
                                                  0,
                                              name: dataController.kitty.value
                                                      .kitty?.title ??
                                                  '');
                                        },
                                        child: Obx(
                                          () => Container(
                                              padding:
                                                  const EdgeInsets.all(8.0),
                                              height: 70.h,
                                              width: 70.w,
                                              decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(4.5),
                                                border: Border.all(
                                                    color: Colors.grey),
                                              ),
                                              child: kittyController
                                                      .isUploadingImage.value
                                                  ? const SizedBox(
                                                      height: 30,
                                                      width: 30,
                                                      child:
                                                          CircularProgressIndicator
                                                              .adaptive(),
                                                    )
                                                  : const Icon(Icons
                                                      .add_a_photo_outlined)),
                                        ),
                                      );
                                    }
                                    return Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: Stack(children: [
                                        ShowCachedNetworkImage(
                                            height: 70.h,
                                            width: 70.w,
                                            errorWidget:
                                                const Icon(Icons.warning),
                                            imageurl: media[index].url ?? ''),
                                        IconButton(
                                            onPressed: () {
                                              showDialog(
                                                context: context,
                                                builder:
                                                    (BuildContext context) {
                                                  return AlertDialog(
                                                    title: Text(
                                                        'confirm_deletion_title'.tr),
                                                    content: Text(
                                                        'delete_photo_confirmation'.tr),
                                                    actions: <Widget>[
                                                      TextButton(
                                                        onPressed: () {
                                                          Navigator.of(context)
                                                              .pop(); // Close the dialog
                                                        },
                                                        child: Text(
                                                            'cancel_button'.tr),
                                                      ),
                                                      TextButton(
                                                        onPressed: () {
                                                          kittyController
                                                              .deleteMedia(
                                                                  contributeController
                                                                          .kittyMedia[
                                                                              index]
                                                                          .id ??
                                                                      0,
                                                                  pos: index)
                                                              .whenComplete(() =>
                                                                  Navigator.of(
                                                                          context)
                                                                      .pop());
                                                        },
                                                        child: Text(
                                                            'delete_button'.tr,
                                                            style: const TextStyle(
                                                                color: Colors
                                                                    .red)),
                                                      ),
                                                    ],
                                                  );
                                                },
                                              );
                                            },
                                            icon: Icon(Icons.delete_outlined,
                                                color: Colors.grey.shade400))
                                      ]),
                                    );
                                  }),
                            )),
                      ),

                      // Align(

                      //   child: MyButton(label: ('Edit EndDate'), onClick: (){ 
                      //     Get.toNamed(NavRoutes.editEndDatekittyScreen);
                      //   }),
                      // )
                      
                      /*
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0),
                        child: MyTextFieldwValidator(
                          title: 'end_date'.tr,
                          controller: endDateController,
                          hint: 'click to select end Date',
                          readOnly: true,
                          onTap: () async {
                            DateTime? pickedDateTime = await showDatePicker(
                              context: context,
                              initialDate: DateTime.now(),
                              firstDate: DateTime.now(),
                              lastDate:
                                  DateTime.now().add(const Duration(days: 365)),
                            );

                            if (pickedDateTime != null) {
                              TimeOfDay? pickedTime = await showTimePicker(
                                context: context,
                                initialTime: TimeOfDay.now(),
                              );

                              if (pickedTime != null) {
                                DateTime finalDateTime = DateTime(
                                  pickedDateTime.year,
                                  pickedDateTime.month,
                                  pickedDateTime.day,
                                  pickedTime.hour,
                                  pickedTime.minute,
                                );

                                String formattedDateTime =
                                    DateFormat('d MMM yyyy HH : mm a')
                                        .format(finalDateTime);
                                endDateController.text = formattedDateTime;
                              }
                            }
                          },
                        ),
                      ),
                 */   ],
                  ),
                ),
              ),
            ),
            KittySettingsScreen(kittyId: kittyId ?? 0)
          ],
        ),
        floatingActionButton: Obx(
          () => showSettings.value == true
              ? const SizedBox()
              : FloatingActionButton.extended(
                  backgroundColor: AppColors.primary,
                  onPressed: () async {
                    setState(() {
                      isLoading = true;
                    });
                    await updateKitty();
                    if (mounted) {
                      setState(() {
                        isLoading = false;
                      });
                    }
                  },
                  label: isLoading
                      ? const SpinKitDualRing(
                          color: Colors.white,
                          size: 30.0,
                        )
                      : Text("update_details_button".tr)),
        ));
  }

  updateKitty() async {
    final kittyCreated = dataController.kitty.value.kitty;
    if (formKey.currentState!.validate()) {
      context.hideKeyboard();
      /*
      int? referCode;
      int settlementType = 0;
      String beneficiaryChannel = '63902';
      String account = '';
      String accountref = '';
      String beneficiaryPhoneNumber = myPhone;
      String? userID = kittyController.user.value.iD?.toString();

      if (tabController.index == 1) {
        //paybill
        settlementType = 2;
        if (accountController.text.trim().isEmpty ||
            accountrefController.text.trim().isEmpty) {
          ToastUtils.showErrorToast(
              context, "Enter all required fields for Paybill", "Error");
          return;
        } else {
          account = accountController.text.trim();
          accountref = accountrefController.text.trim();
          beneficiaryPhoneNumber = user!.phoneNumber ?? "";
          beneficiaryChannel = beneficiaryChannel;
        }
      } else if (tabController.index == 2) {
        //till
        settlementType = 1;
        if (accountController.text.trim().isEmpty) {
          ToastUtils.showErrorToast(context, "Enter Till number", "Error");
          return;
        } else {
          account = accountController.text.trim();
          beneficiaryPhoneNumber = user!.phoneNumber ?? "";
          beneficiaryChannel = beneficiaryChannel;
        }
      } else {
        settlementType = 0;
        beneficiaryPhoneNumber = myPhone.substring(1);
        account = accountController.text.trim();
        account = myPhone.substring(1);
      }*/
      CreateKitPayload payload = CreateKitPayload(
          id: kittyId,
          limit: int.tryParse(targetController.text),
          title: nameController.text.trim(),
          description: quilltoHtml(kittyDesc), //descriptionController.text.trim(),
          beneficiaryChannel: kittyController
              .getNetworkCode(networkTitle: selectedChannel ?? "")
              .toString(),
          // null, //"0",
          beneficiaryAccount:
              dataController.kitty.value.kitty?.beneficiaryAccount ??
                  "", //null, //'************',
          //  account, // null,
          beneficiaryAccountRef:
              kittyCreated?.beneficiaryAccountRef ?? "", // accountref, //null,
          refererMerchantCode: kittyCreated?.refererMerchantCode,
          beneficiaryPhoneNumber:
              //null, //'************', //
              singleKitty.userPhoneNumber, //beneficiaryPhoneNumber,
          settlementType: kittyCreated?.settlementType,
          phoneNumber: kittyCreated?.phoneNumber ?? "",
          categories: kittyController.selectedKittyCategories.isNotEmpty
              ? kittyController.selectedKittyCategories.toList()
              : null,
          endDate: DateFormat('d MMM yyyy HH : mm a')
              .parse(endDateController.text)
              .toUtc());

      bool resp = await kittyController.updateKitty(request: payload);
      if (resp) {
        kittyController.selectedKittyCategoryModels.clear();
        kittyController.selectedKittyCategories.clear();
        if (!mounted) {
          return;
        }
        // Refresh current kitty data
        var res = await contributeController.getKitty(id: kittyId);
        if (res) {
          var dataController = Get.put(DataController());
          dataController.kitty.value = singleKitty.singleKitty.value;
        }
        // Refresh user kitties list
        await Get.find<UserKittyController>().forceRefresh();
        Navigator.pop(context);
      } else {
        if (!mounted) {
          return;
        }
        ToastUtils.showToast(kittyController.apiMessage.string,
            toastType: ToastType.error);
      }
      // Get.to(() => const ViewKitty());
    } else {
      ToastUtils.showErrorToast(context, "Error", "Check the values");
    }
  }

  void _showCategoryBottomSheet(BuildContext context, KittyController controller) {
    final searchController = TextEditingController();
    controller.filterCategories('');
    
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        padding: EdgeInsets.all(16.w),
        child: Column(
          children: [
            Container(
              width: 40.w,
              height: 4.h,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),
            SizedBox(height: 16.h),
            Text(
              'select_categories'.tr,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            TextField(
              controller: searchController,
              decoration: InputDecoration(
                hintText: 'search_categories_placeholder'.tr,
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
              onChanged: (value) => controller.filterCategories(value),
            ),
            SizedBox(height: 16.h),
            Expanded(
              child: GetBuilder<KittyController>(
                builder: (controller) => ListView.builder(
                  itemCount: controller.filteredKittyCategories.length,
                  itemBuilder: (context, index) {
                    final category = controller.filteredKittyCategories[index];
                    final isSelected = controller.selectedKittyCategoryModels.any((c) => c.id == category.id);
                    final canSelect = controller.selectedKittyCategoryModels.length < 3;
                    
                    return CheckboxListTile(
                      title: Text(category.name?.toLowerCase().replaceAll(' ', '_').tr ?? ''),
                      value: isSelected,
                      onChanged: (canSelect || isSelected) ? (value) {
                        controller.toggleCategorySelection(category);
                      } : null,
                      controlAffinity: ListTileControlAffinity.leading,
                    );
                  },
                ),
              ),
            ),
            SizedBox(height: 16.h),
            SizedBox(
              width: double.infinity,
              child: MyButton(

                onClick: () => Navigator.pop(context),
                label: ('done'.tr),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
