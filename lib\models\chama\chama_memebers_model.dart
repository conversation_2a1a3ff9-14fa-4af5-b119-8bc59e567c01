// To parse this JSON data, do
//
//     final chamaMembers = chamaMembersFromJson(jsonString);

import 'dart:convert';

import 'package:onekitty_admin/models/chama/member_penalty_request.dart';

List<ChamaMembers> chamaMembersFromJson(String str) => List<ChamaMembers>.from(json.decode(str).map((x) => ChamaMembers.fromJson(x)));

String chamaMembersToJson(List<ChamaMembers> data) => json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class ChamaMembers {
    final int? id;
    final DateTime? createdAt;
    final DateTime? updatedAt;
    final String? deletedAt;
    final int? userId;
    final String? phoneNumber;
    final String? countryCode;
    final String? firstName;
    final String? secondName;
    final String? role;
    final String? beneficiary;
    final int? chamaId;
    final int? receivingOrder;
    final String? status;
    final bool? isSignatory;
    final String? notificationType;
    final String? email;
    final String? whatsApp;

    ChamaMembers( {
      this.notificationType, this.email, this.whatsApp,
        this.id,
        this.createdAt,
        this.updatedAt,
        this.deletedAt,
        this.userId,
        this.phoneNumber,
        this.countryCode,
        this.firstName,
        this.secondName,
        this.role,
        this.beneficiary,
        this.chamaId,
        this.receivingOrder,
        this.status,
        this.isSignatory,
    });

    factory ChamaMembers.fromJson(Map<String, dynamic> json) => ChamaMembers(
        id: json["ID"],
        createdAt: DateTime.parse(json["CreatedAt"]),
        updatedAt: DateTime.parse(json["UpdatedAt"]),
        deletedAt: json["DeletedAt"],
        userId: json["user_id"],
        phoneNumber: json["phone_number"] ?? "",
        countryCode: json["country_code"],
        firstName: json["first_name"] ?? "",
        secondName: json["second_name"] ?? "",
        role: json["role"],
        beneficiary: json["beneficiary"],
        chamaId: json["chama_id"],
        receivingOrder: json["receiving_order"],
        status: json["status"],
        isSignatory: json["is_signatory"] ?? false,
        email: json['email'],
        whatsApp: json['whatsApp'],
        notificationType: json['notificationType']
    );

    Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt!.toIso8601String(),
        "UpdatedAt": updatedAt!.toIso8601String(),
        "DeletedAt": deletedAt,
        "user_id": userId,
        "phone_number": phoneNumber,
        "country_code": countryCode,
        "first_name": firstName,
        "second_name": secondName,
        "role": role,
        "beneficiary": beneficiary,
        "chama_id": chamaId,
        "receiving_order": receivingOrder,
        "status": status,
        "is_signatory": isSignatory,
        "whatsApp" : whatsApp, 
        "email" : email,
        "notificationType" : notificationType
    };

    MemberBeingPenalized toMemberBeingPenalized({int? amount, String? reason}) {
    return MemberBeingPenalized(
      memberId: id,
      amount: amount,
      reason: reason,
    );
  }
}

// To parse this JSON data, do
//
//     final updtMemDto = updtMemDtoFromJson(jsonString);


UpdtMemDto updtMemDtoFromJson(String str) => UpdtMemDto.fromJson(json.decode(str));

String updtMemDtoToJson(UpdtMemDto data) => json.encode(data.toJson());

class UpdtMemDto {
    int chamaId;
    int memberId;
    String firstName;
    String secondName;
    String profileUlr;
    String role;
    String status;

    UpdtMemDto({
        required this.chamaId,
        required this.memberId,
        required this.firstName,
        required this.secondName,
        required this.profileUlr,
        required this.role,
        required this.status,
    });

    factory UpdtMemDto.fromJson(Map<String, dynamic> json) => UpdtMemDto(
        chamaId: json["chama_id"],
        memberId: json["member_id"],
        firstName: json["first_name"] ?? "",
        secondName: json["second_name"] ?? "",
        profileUlr: json["profile_ulr"],
        role: json["role"],
        status: json["status"],
    );

    Map<String, dynamic> toJson() => {
        "chama_id": chamaId,
        "member_id": memberId,
        "first_name": firstName,
        "second_name": secondName,
        "profile_ulr": profileUlr,
        "role":role,
        "status":status,
    };
}


// To parse this JSON data, do
//
//     final orderedMembers = orderedMembersFromJson(jsonString);


OrderedMembers orderedMembersFromJson(String str) => OrderedMembers.fromJson(json.decode(str));

String orderedMembersToJson(OrderedMembers data) => json.encode(data.toJson());

class OrderedMembers {
    bool? status;
    String? message;
    OrderData? data;

    OrderedMembers({
         this.status,
         this.message,
         this.data,
    });

    factory OrderedMembers.fromJson(Map<String, dynamic> json) => OrderedMembers(
        status: json["status"],
        message: json["message"],
        data: OrderData.fromJson(json["data"]),
    );

    Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data!.toJson(),
    };
}

class OrderData {
    List<ChamaMembers>? items;
    int? page;
    int? size;
    int? maxPage;
    int? totalPages;
    int? total;
    bool? last;
    bool? first;
    int? visible;

    OrderData({
         this.items,
         this.page,
         this.size,
         this.maxPage,
         this.totalPages,
         this.total,
         this.last,
         this.first,
         this.visible,
    });

    factory OrderData.fromJson(Map<String, dynamic> json) => OrderData(
        items: List<ChamaMembers>.from(json["items"].map((x) => ChamaMembers.fromJson(x))),
        page: json["page"],
        size: json["size"],
        maxPage: json["max_page"],
        totalPages: json["total_pages"],
        total: json["total"],
        last: json["last"],
        first: json["first"],
        visible: json["visible"],
    );

    Map<String, dynamic> toJson() => {
        "items": List<dynamic>.from(items!.map((x) => x.toJson())),
        "page": page,
        "size": size,
        "max_page": maxPage,
        "total_pages": totalPages,
        "total": total,
        "last": last,
        "first": first,
        "visible": visible,
    };
}

