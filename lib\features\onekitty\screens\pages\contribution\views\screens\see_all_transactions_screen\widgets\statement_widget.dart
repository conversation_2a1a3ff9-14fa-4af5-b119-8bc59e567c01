import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/events/controllers/view_single_event.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/controllers/user_ktty_controller.dart'; 
import 'package:onekitty_admin/models/kitty_model.dart';
import 'package:onekitty_admin/models/transaction_model.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/views/screens/see_all_transactions_screen/widgets/pdf_make.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/views/screens/see_all_transactions_screen/widgets/pdf_statement.dart';
import 'package:printing/printing.dart';

// ignore: must_be_immutable
class StatementPage extends StatelessWidget {
  StatementPage(
      {super.key,
      this.transactions,
      this.userTransactions,
      required this.isContributions,
      this.kitty,
      this.eventId});
  final int? eventId;
  List<TransactionModel>? transactions = [];
  List<TransactionModel>? userTransactions = [];
  bool isContributions;
  Kitty? kitty;
  final DataController dataController = Get.find<DataController>();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('PDF Statement'),
      ),
      body: PdfPreview(
        shareActionExtraSubject:
            "${dataController.kitty.value.kitty?.title ?? "onekitty"} transactions statement",
        pdfFileName: eventId != null
            ? Get.find<ViewSingleEventController>().event.value.id == eventId
                ? Get.find<ViewSingleEventController>().event.value.title
                : "event"
            : isContributions
                ? "${dataController.kitty.value.kitty?.title ?? ""}_transactions_statement.pdf"
                : "onekitty_transactions_statement.pdf",
        build: (context) => isContributions
            ? makeFullContributionsPdf(transactions ?? [], kitty,
                eventId: eventId)
            : makeFullPdf(transactions ?? []),
      ),
    );
  }
}

//USER TRANSACTIONS
// ignore: must_be_immutable
class UserStatementPage extends StatelessWidget {
  UserStatementPage(
      {super.key,
      required this.isContributions,
      required this.userTransactions,
      this.kitty});
  List<TransactionModel> userTransactions = [];
  bool isContributions;
  Kitty? kitty;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('PDF Statement'),
      ),
      body: PdfPreview(
        shareActionExtraSubject: "${"onekitty"} transactions statement",
        pdfFileName: isContributions
            ? "User_transactions_statement.pdf"
            : "onekitty_transactions_statement.pdf",
        build: (context) => isContributions
            ? makeFullUserContributionsPdf(userTransactions)
            : makeFullUserPdf(userTransactions),
      ),
    );
  }
}

// ignore: must_be_immutable
class SingleStatementPage extends StatelessWidget {
  const SingleStatementPage(
      {super.key,
      required this.isContributions,
      this.userTransactions,
      this.kittyTransaction,
      this.kitty,
      this.eventId});
  final TransactionModel? userTransactions;
  final bool isContributions;
  final Kitty? kitty;
  final TransactionModel? kittyTransaction;
  final int? eventId;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('PDF Statement'),
      ),
      body: PdfPreview(
        shareActionExtraSubject: "${"onekitty"} transactions statement",
        pdfFileName: isContributions
            ? "onekitty_transactions_statement.pdf"
            : "User_transactions_statement.pdf",
        build: (context) => isContributions
            ? makeKittySinglePdf(kittyTransaction ?? TransactionModel(),
                eventId: eventId)
            : makeUserSinglePdf(userTransactions ?? TransactionModel()),
      ),
    );
  }
}
