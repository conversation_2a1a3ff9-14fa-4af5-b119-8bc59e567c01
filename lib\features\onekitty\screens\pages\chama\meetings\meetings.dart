import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty_admin/features/onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty_admin/helpers/colors.dart';
import 'package:onekitty_admin/helpers/extensions/text_styles.dart';
import 'package:onekitty_admin/helpers/show_snack_bar.dart';
import 'package:onekitty_admin/helpers/show_toast.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/chama/meetings/add_meeting.dart';
import 'package:onekitty_admin/utils/asset_urls.dart';
import 'package:onekitty_admin/utils/custom_button.dart';
import 'package:onekitty_admin/utils/custom_image_view.dart';
import 'package:onekitty_admin/utils/delete_dialog.dart';
import 'package:onekitty_admin/utils/size_config.dart';
import 'package:onekitty_admin/utils/themes_colors.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter/services.dart';

class MeetingsPage extends StatefulWidget {
  const MeetingsPage({super.key});

  @override
  State<MeetingsPage> createState() => _MeetingsPageState();
}

class _MeetingsPageState extends State<MeetingsPage> {
  final ChamaDataController chamaDataController =
      Get.put(ChamaDataController());
  final ChamaController chamaController = Get.put(ChamaController());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
        child: Column(
          children: [
            Row(
              children: [
                IconButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    icon: const Icon(Icons.arrow_back)),
                Text('back'.tr)
              ],
            ),
            Text(
              'meetings'.tr,
              style: context.titleText
                  ?.copyWith(fontSize: 22, fontWeight: FontWeight.bold),
            ),
            const SizedBox(
              height: 7,
            ),
            Text(
              'meetings_description'.tr,
              textAlign: TextAlign.center,
              style: TextStyle(
                  color: appTheme.gray600, fontWeight: FontWeight.w600),
            ),
            Expanded(
                child: GetX(
                    init: ChamaController(),
                    initState: (state) {
                      Future.delayed(Duration.zero, () async {
                        try {
                          await state.controller?.getChamaMeetings(
                              chamaId:
                                  chamaDataController.chama.value.chama?.id ??
                                      0);
                        } catch (e) {
                          throw e;
                        }
                      });
                    },
                    builder: (ChamaController chamaController) {
                      if (chamaController.isGetMeetingLoading.isTrue) {
                        return SizedBox(
                          height: SizeConfig.screenHeight * .33,
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SpinKitDualRing(
                                  color: ColorUtil.blueColor,
                                  lineWidth: 4.sp,
                                  size: 40.0.sp,
                                ),
                                Text(
                                  'loading'.tr,
                                  style: const TextStyle(
                                    color: Colors.white,
                                  ),
                                )
                              ],
                            ),
                          ),
                        );
                      } else if (chamaController.meetings.isEmpty) {
                        return Center(
                            child: Text('no_meetings_added'.tr));
                      } else if (chamaController.meetings.isNotEmpty) {
                        return ListView.separated(
                            itemBuilder: (context, index) {
                              final meeting = chamaController.meetings[index];
                              return InkWell(
                                onTap: () {
                                  if (chamaDataController
                                          .chama.value.member?.role ==
                                      "CHAIRPERSON") {
                                    Get.off(() => AddMeetingPage(
                                          isUpdating: true,
                                          meeting: meeting,
                                        ));
                                  }
                                  // Navigator.pushAndRemoveUntil(
                                  //     context,
                                  //     MaterialPageRoute(
                                  //         builder: (context) => AddMeetingPage(
                                  //               isUpdating: true,
                                  //               meeting: meeting,
                                  //             )),
                                  //     (route) => route.isActive);
                                },
                                child: Row(
                                  children: [
                                    CustomImageView(
                                      onTap: () async {
                                        final Uri url = Uri.parse(
                                            "${meeting.calendarLink}");
                                        if (!await launchUrl(url)) {
                                          ToastUtils.showErrorToast(
                                              context,
                                              'could_not_launch_url'.tr,
                                              'error'.tr);
                                        }
                                      },
                                      imagePath: AssetUrl.meetingCalender,
                                    ),
                                    const SizedBox(
                                      width: 12,
                                    ),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            meeting.title ?? "",
                                            maxLines: 2,
                                            overflow: TextOverflow.ellipsis,
                                            style: context.titleText?.copyWith(
                                                fontWeight: FontWeight.w800,
                                                color: appTheme.gray600),
                                          ),
                                          Text(
                                              "${DateFormat("dd/MM/yyyy").format(meeting.startDate!.toUtc().toLocal())} - ${DateFormat("dd/MM/yyyy").format(meeting.endDate!.toUtc().toLocal())}"),
                                          Text(
                                            "${DateFormat("HH:mm").format(meeting.startDate!.toUtc().toLocal())} - ${DateFormat("HH:mm").format(meeting.endDate!.toUtc().toLocal())}",
                                            overflow: TextOverflow.ellipsis,
                                            style: const TextStyle(
                                                color: AppColors.neutralGrey,
                                                fontWeight: FontWeight.w500),
                                          ),
                                          Text(
                                            meeting.eventType ?? "",
                                            style: TextStyle(
                                                color: appTheme.gray600,
                                                fontWeight: FontWeight.w400),
                                          ),
                                          meeting.eventType?.toUpperCase() == "VIRTUAL" 
                                          ? Row(
                                              children: [
                                                Expanded(
                                                  child: Text(
                                                    meeting.venue ?? "",
                                                    overflow: TextOverflow.ellipsis,
                                                    style: TextStyle(
                                                        color: appTheme.gray600,
                                                        fontWeight: FontWeight.w400),
                                                  ),
                                                ),
                                                IconButton(
                                                  onPressed: () {
                                                    Clipboard.setData(ClipboardData(text: meeting.venue ?? ""));
                                                    ToastUtils.showSuccessToast(context, 'link_copied_to_clipboard'.tr, 'success'.tr);
                                                  },
                                                  icon: const Icon(Icons.copy, size: 18, color: ColorUtil.blueColor),
                                                  padding: EdgeInsets.zero,
                                                  constraints: const BoxConstraints(),
                                                ),
                                              ],
                                            )
                                          : Text(
                                              meeting.venue ?? "",
                                              style: TextStyle(
                                                  color: appTheme.gray600,
                                                  fontWeight: FontWeight.w400),
                                            ),
                                        ],
                                      ),
                                    ),
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.end,
                                      children: [
                                        if (chamaDataController
                                                .chama.value.member?.role ==
                                            "CHAIRPERSON")
                                          IconButton(
                                              onPressed: () {
                                                showDeleteDialog(
                                                    index,
                                                    context,
                                                    chamaController
                                                        .isDeleteMeetingLoading
                                                        .isTrue, () {
                                                  deleteMeeting(index);
                                                  Navigator.pop(context);
                                                }, 'meeting'.tr);
                                              },
                                              icon: const Icon(Icons.close)),
                                        TextButton(
                                            onPressed: () async {
                                              final Uri url = Uri.parse(
                                                  "${meeting.calendarLink}");
                                              if (!await launchUrl(url)) {
                                                ToastUtils.showErrorToast(
                                                    context,
                                                    'could_not_launch_url'.tr,
                                                    'error'.tr);
                                              }
                                            },
                                            child: Container(
                                                padding:
                                                    const EdgeInsets.all(7),
                                                decoration: BoxDecoration(
                                                    color: AppColors
                                                        .blueButtonColor
                                                        .withOpacity(0.2),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            25)),
                                                child: Text('calender'.tr)))
                                      ],
                                    ),
                                  ],
                                ),
                              );
                            },
                            separatorBuilder: (context, index) {
                              return const SizedBox(
                                height: 15,
                              );
                            },
                            itemCount: chamaController.meetings.length);
                      }
                      return Center(child: Text('no_meetings_added_yet'.tr));
                    })),
            SizedBox(
              height: 45,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  OutlinedButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      child:  Padding(
                        padding: const  EdgeInsets.symmetric(horizontal: 8.0),
                        child: Text('back'.tr),
                      )),
                  if (chamaDataController.chama.value.member?.role ==
                          "CHAIRPERSON" ||
                      chamaDataController.chama.value.member?.role ==
                          "SECRETARY")
                    CustomKtButton(
                      btnText: 'add_meeting'.tr,
                      width: 150.w,
                      onPress: () {
                        Get.off(() => const AddMeetingPage(isUpdating: false));
                      },
                    )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  deleteMeeting(index) async {
    final meeting = chamaController.meetings[index];
    bool res = await chamaController.deleteMeeting(
        meetingId: meeting.id ?? 0, chamaId: meeting.chamaId ?? 0);
    if (res) {
      Snack.show(res, chamaController.apiMessage.string);
      setState(() {
        chamaController.meetings.removeAt(index);
      });
    }
  }
}
