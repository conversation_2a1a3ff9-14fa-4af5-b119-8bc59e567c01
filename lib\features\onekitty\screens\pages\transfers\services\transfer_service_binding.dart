// Transfer Service Binding
// Ensures TransferService is properly registered with GetX

import 'package:get/get.dart';
import 'transfer_service.dart';

class TransferServiceBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<TransferService>(() => TransferService());
  }
}

// Extension to easily register the service
extension TransferServiceRegistration on GetInterface {
  void registerTransferService() {
    if (!Get.isRegistered<TransferService>()) {
      Get.put<TransferService>(TransferService());
    }
  }
}