// Comprehensive Transaction Filter Widget
// Provides all filter options: search, transaction code, date range

import 'dart:async';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty_admin/helpers/colors.dart';
import 'package:onekitty_admin/utils/themes_colors.dart';
import '../../models/transaction_type.dart';
import '../../controllers/transaction_controller.dart';

class TransactionFilterWidget extends StatefulWidget {
  final TransactionType transactionType;
  final String controllerTag;

  const TransactionFilterWidget({
    super.key,
    required this.transactionType,
    required this.controllerTag,
  });

  @override
  State<TransactionFilterWidget> createState() =>
      _TransactionFilterWidgetState();
}

class _TransactionFilterWidgetState extends State<TransactionFilterWidget> {
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _transactionCodeController =
      TextEditingController();
  final TextEditingController _accountNameController =
      TextEditingController();

  DateTime? _startDate;
  DateTime? _endDate;
  bool _isExpanded = false;
  String _selectedCategory = 'ALL';
  final List<String> _categoryOptions = ['ALL', 'PENALTY', 'CONTRIBUTION'];

  late final TransactionController _controller;
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    _controller = Get.find<TransactionController>(tag: widget.controllerTag);

    // Initialize controllers with current filter values
    _searchController.text = _controller.searchFilter;
    _transactionCodeController.text = _controller.transactionCodeFilter;
    _accountNameController.text = _controller.accountNameFilter;
    _selectedCategory = _controller.categoryFilter.isEmpty ? 'ALL' : _controller.categoryFilter;
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _searchController.dispose();
    _transactionCodeController.dispose();
    _accountNameController.dispose();
    super.dispose();
  }

  void _debounceSearch(String value) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(seconds: 2), () {
      _controller.setSearchFilter(value);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 4.w, horizontal: 8),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.slate),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Search field - always visible
          _buildSearchField(),

          // Expandable filters section
          if (_isExpanded) ...[
            const Divider(color: AppColors.neutralGrey, height: 1),
            _buildAdvancedFilters(),
          ],

          // Expand/collapse button and clear filters
        ],
      ),
    );
  }

  Widget _buildSearchField() {
    return Padding(
      padding: EdgeInsets.all(8.w),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: _showDateRangeBottomSheet,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('Date Range'),
                  Row(
                    children: [
                      AutoSizeText(
                        _getDateRangeText(),
                        minFontSize: 6, 
                        maxFontSize: 20,
                        maxLines: 1,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(width: 8),
                      const Icon(Icons.arrow_drop_down)
                    ],
                  ),
                ],
              ),
            ),
          ),
          _buildFilterActions(),
        ],
      ),
    );
  }

  Widget _buildAdvancedFilters() {
    return Padding(
      padding: EdgeInsets.all(16.w),
      child: Column(
        children: [
          // Transaction code filter
          _buildFilterField(
            controller: _transactionCodeController,
            label: 'transaction_code'.tr,
            hint: 'enter_transaction_code'.tr,
            icon: Icons.receipt,
            onChanged: (value) {
              _debounceTimer?.cancel();
              _debounceTimer = Timer(const Duration(seconds: 2), () {
                _controller.setTransactionCodeFilter(value);
              });
            },
          ),

          SizedBox(height: 12.h),

          // Account name filter
          _buildFilterField(
            controller: _accountNameController,
            label: 'account_number'.tr,
            hint: 'enter_account_number'.tr,
            icon: Icons.account_balance,
            onChanged: (value) {
              _debounceTimer?.cancel();
              _debounceTimer = Timer(const Duration(seconds: 2), () {
                _controller.setAccountNameFilter(value);
              });
            },
          ),

          // Category filter for chama transactions only
          if (widget.transactionType == TransactionType.chama) ...[
            SizedBox(height: 12.h),
            _buildCategoryFilter(),
          ],
        ],
      ),
    );
  }

  Widget _buildFilterField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    required Function(String) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        SizedBox(height: 4.h),
        TextField(
          controller: controller,
          onChanged: onChanged,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: const TextStyle(
              color: Colors.black54,
              fontSize: 14,
            ),
            prefixIcon: Icon(
              icon,
              color: AppColors.neutralGrey,
              size: 20,
            ),
            suffixIcon: controller.text.isNotEmpty
                ? IconButton(
                    icon: const Icon(
                      Icons.clear,
                      color: AppColors.neutralGrey,
                      size: 18,
                    ),
                    onPressed: () {
                      controller.clear();
                      onChanged('');
                    },
                  )
                : null,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: const BorderSide(color: AppColors.slate),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: const BorderSide(color: AppColors.slate),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: const BorderSide(color: AppColors.primary),
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: 12.w,
              vertical: 10.h,
            ),
          ),
          style: const TextStyle(
            color: Colors.black87,
            fontSize: 14,
          ),
        ),
      ],
    );
  }
  Widget _buildDateField({
    required String label,
    required DateTime? date,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 10.h),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.slate),
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Row(
          children: [
            const Icon(
              Icons.calendar_today,
              color: AppColors.neutralGrey,
              size: 20,
            ),
            SizedBox(width: 8.w),
            Expanded(
              child: Text(
                date != null ? DateFormat('dd/MM/yyyy').format(date) : label,
                style: TextStyle(
                  color: date != null ? Colors.black87 : Colors.black54,
                  fontSize: 14,
                ),
              ),
            ),
            if (date != null)
              GestureDetector(
                onTap: () {
                  setState(() {
                    if (label == 'start_date'.tr) {
                      _startDate = null;
                    } else {
                      _endDate = null;
                    }
                  });
                  _updateDateFilters();
                },
                child: const Icon(
                  Icons.clear,
                  color: AppColors.neutralGrey,
                  size: 18,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterActions() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 8.h),
      child: Row(
        children: [
          // Expand/collapse button
          IconButton(
            onPressed: () {
              setState(() {
                _isExpanded = !_isExpanded;
              });
            },
            icon: Icon(
              _isExpanded ? Icons.filter_alt_off : Icons.filter_alt_outlined,
              color: AppColors.primary,
            ),
            // label: Text(
            //   _isExpanded ? 'Less Filters' : 'More Filters',
            //   style: TextStyle(
            //     color: AppColors.primary,
            //     fontSize: 12.sp,
            //     fontWeight: FontWeight.w500,
            //   ),
            // ),
          ),

          const SizedBox(width: 8),

          // Clear all filters button
          if (_hasActiveFilters())
            IconButton(
              onPressed: _clearAllFilters,
              icon: const Icon(
                Icons.clear_all,
                color: ColorUtil.error,
                size: 16,
              ),
            ),
        ],
      ),
    );
  }

  String _getSearchHint() {
    switch (widget.transactionType) {
      case TransactionType.user:
        return 'search_your_transactions'.tr;
      case TransactionType.kitty:
        return 'search_kitty_transactions'.tr;
      case TransactionType.chama:
        return 'search_chama_transactions'.tr;
      case TransactionType.event:
        return 'search_event_transactions'.tr;
    }
  }

  bool _hasActiveFilters() {
    return _searchController.text.isNotEmpty ||
        _transactionCodeController.text.isNotEmpty ||
        _accountNameController.text.isNotEmpty ||
        _startDate != null ||
        _endDate != null ||
        (widget.transactionType == TransactionType.chama && _selectedCategory != 'ALL');
  }

  void _clearAllFilters() {
    setState(() {
      _searchController.clear();
      _transactionCodeController.clear();
      _accountNameController.clear();
      _startDate = null;
      _endDate = null;
      _selectedCategory = 'ALL';
    });
    _controller.clearAllFilters();
  }

  void _selectStartDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _startDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        _startDate = picked;
      });
      _updateDateFilters();
    }
  }

  void _selectEndDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _endDate ?? DateTime.now(),
      firstDate: _startDate ?? DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        _endDate = picked;
      });
      _updateDateFilters();
    }
  }

  void _updateDateFilters() {
    String? startDateStr;
    String? endDateStr;
    
    if (_startDate != null) {
      startDateStr = DateFormat('yyyy-MM-dd').format(_startDate!);
    }
    
    if (_endDate != null) {
      endDateStr = DateFormat('yyyy-MM-dd').format(_endDate!);
    }

    _controller.setDateFilters(startDateStr, endDateStr);
  }

  String _getDateRangeText() {
    if (_startDate != null && _endDate != null) {
      return '${DateFormat('dd/MM/yyyy').format(_startDate!)} - ${DateFormat('dd/MM/yyyy').format(_endDate!)}';
    } else if (_startDate != null) {
      return 'From ${DateFormat('dd/MM/yyyy').format(_startDate!)}';
    } else if (_endDate != null) {
      return 'Until ${DateFormat('dd/MM/yyyy').format(_endDate!)}';
    }
    return 'All Time';
  }

  void _showDateRangeBottomSheet() {
    DateTime? tempStartDate = _startDate;
    DateTime? tempEndDate = _endDate;
    int selectedTabIndex = 0;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => StatefulBuilder(
        builder: (context, setModalState) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          padding: EdgeInsets.fromLTRB(24.w, 24.w, 24.w, MediaQuery.of(context).viewInsets.bottom + 24.w),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: Container(
                  width: 40.w,
                  height: 4.h,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2.r),
                  ),
                ),
              ),
              SizedBox(height: 20.h),
              Text(
                'date_range'.tr,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              SizedBox(height: 24.h),
              // Tab Bar
              Container(
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: GestureDetector(
                        onTap: () => setModalState(() => selectedTabIndex = 0),
                        child: Container(
                          padding: EdgeInsets.symmetric(vertical: 12.h),
                          decoration: BoxDecoration(
                            color: selectedTabIndex == 0 ? AppColors.primary : Colors.transparent,
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                          child: Text(
                            'Quick',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: selectedTabIndex == 0 ? Colors.white : Colors.black54,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                    ),
                    Expanded(
                      child: GestureDetector(
                        onTap: () => setModalState(() => selectedTabIndex = 1),
                        child: Container(
                          padding: EdgeInsets.symmetric(vertical: 12.h),
                          decoration: BoxDecoration(
                            color: selectedTabIndex == 1 ? AppColors.primary : Colors.transparent,
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                          child: Text(
                            'Custom',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: selectedTabIndex == 1 ? Colors.white : Colors.black54,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 24.h),
              // Tab Content
              if (selectedTabIndex == 0) ..._buildQuickTab(
                setModalState, 
                () => tempStartDate, 
                () => tempEndDate, 
                (date) => tempStartDate = date, 
                (date) => tempEndDate = date
              )
              else ..._buildCustomTab(
                setModalState, 
                () => tempStartDate, 
                () => tempEndDate, 
                (date) => tempStartDate = date, 
                (date) => tempEndDate = date
              ),
              SizedBox(height: 32.h),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        setModalState(() {
                          tempStartDate = null;
                          tempEndDate = null;
                        });
                      },
                      style: OutlinedButton.styleFrom(
                        padding: EdgeInsets.symmetric(vertical: 12.h),
                        side: const BorderSide(color: AppColors.neutralGrey),
                      ),
                      child: const Text(
                        'Clear',
                        style: TextStyle(color: AppColors.neutralGrey),
                      ),
                    ),
                  ),
                  SizedBox(width: 16.w),
                  Expanded(
                    flex: 2,
                    child: ElevatedButton(
                      onPressed: () {
                        setState(() {
                          _startDate = tempStartDate;
                          _endDate = tempEndDate;
                        });
                        _updateDateFilters();
                        Navigator.pop(context);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        padding: EdgeInsets.symmetric(vertical: 12.h),
                      ),
                      child: const Text(
                        'Confirm',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 32.h),
            ],
          ),
        ),
      ),
    );
  }

  List<Widget> _buildQuickTab(StateSetter setModalState, DateTime? Function() getTempStartDate, DateTime? Function() getTempEndDate, Function(DateTime?) setTempStartDate, Function(DateTime?) setTempEndDate) {
    final quickOptions = [
      {'label': 'All', 'months': null},
      {'label': '1M', 'months': 1},
      {'label': '3M', 'months': 3},
      {'label': '6M', 'months': 6},
    ];

    return [
      Wrap(
        spacing: 12.w,
        runSpacing: 12.h,
        children: quickOptions.map((option) {
          final isSelected = _isQuickOptionSelected(option['months'] as int?, getTempStartDate(), getTempEndDate());
          return GestureDetector(
            onTap: () {
              setModalState(() {
                if (option['months'] == null) {
                  setTempStartDate(null);
                  setTempEndDate(null);
                } else {
                  final now = DateTime.now();
                  final endDate = DateTime(now.year, now.month, now.day, 23, 59, 59, 999);
                  final startDate = DateTime(now.year, now.month - (option['months'] as int), now.day, 0, 0, 0);
                  setTempEndDate(endDate);
                  setTempStartDate(startDate);
                }
              });
            },
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 12.h),
              decoration: BoxDecoration(
                color: isSelected ? AppColors.primary : Colors.transparent,
                border: Border.all(
                  color: isSelected ? AppColors.primary : AppColors.slate,
                ),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Text(
                option['label'] as String,
                style: TextStyle(
                  color: isSelected ? Colors.white : Colors.black87,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          );
        }).toList(),
      ),
    ];
  }

  List<Widget> _buildCustomTab(StateSetter setModalState, DateTime? Function() getTempStartDate, DateTime? Function() getTempEndDate, Function(DateTime?) setTempStartDate, Function(DateTime?) setTempEndDate) {
    return [
      Row(
        children: [
          Expanded(
            child: _buildBottomSheetDateField(
              label: 'start_date'.tr,
              date: getTempStartDate(),
              onTap: () async {
                final picked = await showDatePicker(
                  context: context,
                  initialDate: getTempStartDate() ?? DateTime.now(),
                  firstDate: DateTime(2020),
                  lastDate: DateTime.now(),
                );
                if (picked != null) {
                  setModalState(() {
                    setTempStartDate(picked);
                  });
                }
              },
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: _buildBottomSheetDateField(
              label: 'end_date'.tr,
              date: getTempEndDate(),
              onTap: () async {
                final picked = await showDatePicker(
                  context: context,
                  initialDate: getTempEndDate() ?? DateTime.now(),
                  firstDate: getTempStartDate() ?? DateTime(2020),
                  lastDate: DateTime.now(),
                );
                if (picked != null) {
                  setModalState(() {
                    setTempEndDate(picked);
                  });
                }
              },
            ),
          ),
        ],
      ),
    ];
  }

  bool _isQuickOptionSelected(int? months, DateTime? tempStartDate, DateTime? tempEndDate) {
    if (months == null) {
      return tempStartDate == null && tempEndDate == null;
    }
    
    if (tempStartDate == null || tempEndDate == null) return false;
    
    final now = DateTime.now();
    final expectedStart = DateTime(now.year, now.month - months, now.day);
    
    return tempStartDate.day == expectedStart.day &&
           tempStartDate.month == expectedStart.month &&
           tempStartDate.year == expectedStart.year &&
           tempEndDate.day == now.day &&
           tempEndDate.month == now.month &&
           tempEndDate.year == now.year;
  }

  Widget _buildBottomSheetDateField({
    required String label,
    required DateTime? date,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.slate),
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: const TextStyle(
                fontSize: 12,
                color: AppColors.neutralGrey,
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: 4.h),
            Row(
              children: [
                const Icon(
                  Icons.calendar_today,
                  color: AppColors.neutralGrey,
                  size: 18,
                ),
                SizedBox(width: 8.w),
                Expanded(
                  child: Text(
                    date != null ? DateFormat('dd/MM/yyyy').format(date) : 'Select date',
                    style: TextStyle(
                      color: date != null ? Colors.black87 : Colors.black54,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Category',
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        SizedBox(height: 8.h),
        SizedBox(
          height: 40.h,
          child: ListView.separated(
            scrollDirection: Axis.horizontal,
            itemCount: _categoryOptions.length,
            separatorBuilder: (context, index) => SizedBox(width: 8.w),
            itemBuilder: (context, index) {
              final category = _categoryOptions[index];
              final isSelected = _selectedCategory == category;
              
              return Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(20),
                  onTap: () {
                    setState(() {
                      _selectedCategory = category;
                    });
                    _controller.setCategoryFilter(category == 'ALL' ? '' : category);
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 16.w),
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: isSelected
                          ? AppColors.primary
                          : AppColors.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      category,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                        color: isSelected ? Colors.white : AppColors.primary,
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

}