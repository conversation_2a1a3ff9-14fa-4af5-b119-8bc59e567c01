import 'package:get/get_utils/get_utils.dart';

/// Enum to define the different types of WhatsApp group contexts
enum WhatsAppGroupType {
  event,
  chama,
  kitty,
}

/// Extension to provide display names for WhatsApp group types
extension WhatsAppGroupTypeExtension on WhatsAppGroupType {
  String get displayName {
    switch (this) {
      case WhatsAppGroupType.event:
        return 'event'.tr;
      case WhatsAppGroupType.chama:
        return 'chama'.tr;
      case WhatsAppGroupType.kitty:
        return 'kitty'.tr;
    }
  }

  String get contextDescription {
    switch (this) {
      case WhatsAppGroupType.event:
        return 'connect_whatsapp_group_event_notifications'.tr;
      case WhatsAppGroupType.chama:
        return 'connect_whatsapp_groups_chama_notifications'.tr;
      case WhatsAppGroupType.kitty:
        return 'connect_whatsapp_group_transaction_notifications'.tr;
    }
  }

  String get emptyStateMessage {
    switch (this) {
      case WhatsAppGroupType.event:
        return 'no_whatsapp_groups_connected_event'.tr;
      case WhatsAppGroupType.chama:
        return 'no_whatsapp_groups_connected'.tr;
      case WhatsAppGroupType.kitty:
        return 'no_whatsapp_groups'.tr;
    }
  }

  String get removeConfirmationMessage {
    switch (this) {
      case WhatsAppGroupType.event:
        return 'remove_whatsapp_group_event_confirmation'.tr;
      case WhatsAppGroupType.chama:
        return 'remove_whatsapp_group_confirmation'.tr;
      case WhatsAppGroupType.kitty:
        return 'remove_whatsapp_group_confirmation'.tr;
    }
  }
}