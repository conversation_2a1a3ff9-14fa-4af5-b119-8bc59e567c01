import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:grouped_list/grouped_list.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/controllers/user_ktty_controller.dart';
import 'package:onekitty_admin/helpers/show_toast.dart';
import 'package:onekitty_admin/models/auth/user_model.dart';
import 'package:onekitty_admin/models/kitty_model.dart';
import 'package:onekitty_admin/models/transaction_model.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/profile/merchant/transactions.dart';

import 'package:date_time_format/date_time_format.dart';
import 'package:intl/intl.dart';
import 'package:onekitty_admin/features/onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty_admin/main.dart' show isLight;
import '../../../../../../../../utils/utils_exports.dart';

class Referrals extends StatefulWidget {
  const Referrals({super.key});

  @override
  State<Referrals> createState() => _ReferralsScreenState();
}

class _ReferralsScreenState extends State<Referrals> {
  TextEditingController searchController = TextEditingController();
  TextEditingController mrtController = TextEditingController();
  TextEditingController codeController = TextEditingController();
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final GlobalKey _tooltipKey3 = GlobalKey();

  UserKittyController userController = Get.put(UserKittyController());

  String greeting = getGreeting();
  bool isShowform = false;
  DateTime tagetDate = DateTime.now().add(const Duration(days: 1));
  // ContributeController singleKitty = Get.put(ContributeController());
  List filteredKitties = [];

  @override
  void initState() {
    super.initState();
    filteredKitties = userController.referkitties;
  }

  @override
  void dispose() {
    searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return (userController.usermerchant.value.userId == null)
        ? Scaffold(
            appBar: AppBar(
              title: Text('affiliate'.tr),
              centerTitle: true,
            ),
            body: Container(
              padding: const EdgeInsets.all(15),
              alignment: Alignment.topCenter,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'no_referral_code_message'.tr,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      setState(() {
                        isShowform = true;
                      });
                    },
                    child: Text(
                      'register_now'.tr,
                      style: const TextStyle(
                          fontSize: 17, fontWeight: FontWeight.bold),
                    ),
                  ),
                  SizedBox(
                    height: 5.h,
                  ),
                  if (isShowform)
                    Form(
                      key: formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('enter_affiliate_name'.tr,
                              style: Theme.of(context)
                                  .textTheme
                                  .titleLarge
                                  ?.copyWith(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 15)),
                          CustomTextField(
                            controller: mrtController,
                            hintText: 'affiliate_name_hint'.tr,
                            labelText: "",
                            validator: (p0) {
                              if (p0!.isEmpty) {
                                return 'affiliate_name_cannot_be_empty'.tr;
                              }
                              return null;
                            },
                          ),
                          SizedBox(
                            height: 5.h,
                          ),
                          Row(
                            children: [
                              Text('preferred_referral_code_optional'.tr,
                                  style: Theme.of(context)
                                      .textTheme
                                      .titleLarge
                                      ?.copyWith(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 15)),
                              const Spacer(),
                              GestureDetector(
                                onTap: () {
                                  final dynamic tooltip =
                                      _tooltipKey3.currentState;
                                  tooltip?.ensureTooltipVisible();
                                },
                                child: Tooltip(
                                  key: _tooltipKey3,
                                  message: 'system_generate_unique_code'.tr,
                                  child: CustomImageView(
                                    imagePath: AssetUrl.imgInbox,
                                    height: 15.h,
                                    width: 15.w,
                                    margin: EdgeInsets.only(
                                        left: 3.w, top: 2.h, bottom: 3.h),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          CustomTextField(
                            controller: codeController,
                            maxLength: 6,
                            paddingHorizontal: 2.spMin,
                            showNoKeyboard: true,
                            inputFormatters: <TextInputFormatter>[
                              FilteringTextInputFormatter.allow(
                                  RegExp("[0-9]")),
                            ],
                            hintText: "122",
                            labelText: "",
                          ),
                          SizedBox(
                            height: 5.h,
                          ),
                          Obx(
                            () => CustomKtButton(
                              isLoading: userController.loading.isTrue,
                              onPress: () async {
                                if (formKey.currentState!.validate()) {
                                  await showConfirmation(context,
                                      onConfirm: () async {
                                    final res =
                                        await userController.setRefererCode(
                                      request: SetMrchtDto(
                                        userId:
                                            userController.usermodel.value.id ??
                                                0,
                                        merchantName: mrtController.text.trim(),
                                        merchantCode:
                                            int.tryParse(codeController.text),
                                        phoneNumber: userController
                                            .usermodel.value.phoneNumber,
                                      ),
                                    );
                                    if (res) {
                                      await userController.getUser();
                                      setState(() {});
                                    }
                                  },
                                      merchantName: mrtController.text.trim(),
                                      merchantCode: codeController.text,
                                      phoneNumber: userController
                                              .usermodel.value.phoneNumber ??
                                          "");
                                }
                              },
                              height: 30.h,
                              width: 80.w,
                              btnText: 'register'.tr,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          )
        : DefaultTabController(
            length: 2,
            child: Scaffold(
              appBar: AppBar(
                title: Text('my_referrals'.tr),
                centerTitle: true,
                bottom: TabBar(
                  indicatorColor: Colors.white,
                  labelColor: Colors.white,
                  unselectedLabelColor: Colors.white,
                  indicatorWeight: 2.0,
                  dividerColor: Colors.transparent,
                  tabs: [
                    Tab(text: 'kitty_referrals'.tr),
                    Tab(text: 'transactions'.tr),
                  ],
                ),
              ),
              body: TabBarView(
                children: [
                  ReferralsView(
                      userController: userController,
                      filteredKitties: filteredKitties),
                  const MerTransaction(),
                ],
              ),
            ),
          );
    //final screenSize = MediaQuery.of(context).size;
  }
}

Future<void> showConfirmation(BuildContext context,
    {required VoidCallback onConfirm,
    required String merchantName,
    required String merchantCode,
    required String phoneNumber}) {
  return showDialog<void>(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return AlertDialog(
        title: Text('confirm_affiliate_registration'.tr),
        content: SingleChildScrollView(
          child: ListBody(
            children: <Widget>[
              Text('${'affiliate_name'.tr}: $merchantName'),
              Text('${'affiliate_code'.tr}: $merchantCode'),
              Text('${'phone_number'.tr}: $phoneNumber'),
            ],
          ),
        ),
        actions: <Widget>[
          TextButton(
            child: Text('cancel'.tr),
            onPressed: () {
              Navigator.of(context).pop();
            },
          ),
          TextButton(
            child: Text('confirm'.tr),
            onPressed: () {
              Navigator.of(context).pop();
              onConfirm();
            },
          ),
        ],
      );
    },
  );
}

class ReferralsView extends StatefulWidget {
  const ReferralsView({
    super.key,
    required this.userController,
    required this.filteredKitties,
  });

  final UserKittyController userController;
  final List filteredKitties;

  @override
  State<ReferralsView> createState() => _ReferralsViewState();
}

class _ReferralsViewState extends State<ReferralsView> {
  bool _isExpanded = true;

  @override
  Widget build(BuildContext context) {
    final merchantPercent =
        (widget.userController.usermerchant.value.merchantPercent ?? 0) * 100;
    return Scaffold(
      body: Column(
        mainAxisSize: MainAxisSize.max,
        children: [
          SizedBox(height: 5.h),
          TweenAnimationBuilder<Color?>(
            curve: Curves.bounceOut,
            tween: ColorTween(
              begin: isLight.value ? Colors.grey[200] : Colors.grey[800],
              end: _isExpanded
                  ? (isLight.value ? Colors.white : Theme.of(context).cardColor)
                  : (isLight.value ? Colors.grey[400] : Colors.grey[700]),
            ),
            duration: const Duration(milliseconds: 500),
            builder: (context, color, child) {
              return Container(
                color: color,
                child: ExpansionTile(
                  title: Text(
                    'affiliate_profile'.tr,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).textTheme.titleLarge?.color,
                    ),
                  ),
                  textColor: Theme.of(context).textTheme.bodyLarge?.color,
                  collapsedTextColor:
                      Theme.of(context).textTheme.bodyLarge?.color,
                  initiallyExpanded: true,
                  iconColor: Theme.of(context).iconTheme.color,
                  collapsedIconColor: Theme.of(context).iconTheme.color,
                  onExpansionChanged: (bool expanded) {
                    setState(() => _isExpanded = expanded);
                  },
                  children: <Widget>[
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: RichText(
                            text: TextSpan(
                              children: [
                                TextSpan(
                                  text:
                                      "${widget.userController.usermerchant.value.merchantName}",
                                  style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: Theme.of(context)
                                          .textTheme
                                          .bodyLarge
                                          ?.color),
                                ),
                              ],
                            ),
                          ),
                        ),
                        Obx(() {
                          return widget.userController.referkitties.isEmpty
                              ? const SizedBox.shrink()
                              : Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: RichText(
                                    text: TextSpan(
                                      children: [
                                        TextSpan(
                                            text: 'referred_kitties'.tr,
                                            style: TextStyle(
                                                color: Theme.of(context)
                                                    .textTheme
                                                    .bodyLarge
                                                    ?.color)),
                                        TextSpan(
                                          text:
                                              "${widget.userController.totalRefers}",
                                          style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                              color: Theme.of(context)
                                                  .textTheme
                                                  .bodyLarge
                                                  ?.color),
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                        }),
                        InkWell(
                          onTap: () async {
                            String link =
                                "www.onekitty.co.ke/create/?code=${widget.userController.usermerchant.value.merchantCode.toString()}";
                            await Clipboard.setData(ClipboardData(text: link));
                            ToastUtils.showToast('${'copied'.tr} $link',
                                toastType: ToastType.success);
                          },
                          child: Chip(
                            label: Text(
                                '${'code'.tr}${widget.userController.usermerchant.value.merchantCode.toString()}'),
                            avatar: const Icon(Icons.copy),
                          ).animate().fadeIn(duration: 500.ms),
                        ),
                        const SizedBox(
                          width: 10,
                        )
                      ],
                    ),
                    const Divider(),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: RichText(
                            text: TextSpan(
                              children: [
                                TextSpan(
                                    text: 'revenue_percentage'.tr,
                                    style: TextStyle(
                                        color: Theme.of(context)
                                            .textTheme
                                            .bodyLarge
                                            ?.color)),
                                TextSpan(
                                  text:
                                      "${merchantPercent.toStringAsFixed(2)}%",
                                  style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: Theme.of(context)
                                          .textTheme
                                          .bodyLarge
                                          ?.color),
                                ),
                              ],
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: RichText(
                            text: TextSpan(
                              children: [
                                TextSpan(
                                    text: 'balance'.tr,
                                    style: TextStyle(
                                        color: Theme.of(context)
                                            .textTheme
                                            .bodyLarge
                                            ?.color)),
                                TextSpan(
                                  text: FormattedCurrency.getFormattedCurrency(
                                    widget.userController.usermerchant.value
                                            .balance ??
                                        0.0,
                                  ),
                                  style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: Theme.of(context)
                                          .textTheme
                                          .bodyLarge
                                          ?.color),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              );
            },
          ),
          Obx(() {
            return widget.userController.referkitties.isEmpty
                ? const SizedBox()
                : const Column(
                    children: [
                      // Align(
                      //   alignment: Alignment.center,
                      //   child: Text(
                      //     "Referrals",
                      //     style: theme.textTheme.titleLarge,
                      //     // ignore: deprecated_member_use
                      //     textScaleFactor: 0.8,
                      //   ),
                      // ),
                      // Padding(
                      //   padding: EdgeInsets.only(top: 5.h, right: 20.w),
                      //   child: Align(
                      //     alignment: Alignment.topRight,
                      //     child: Text(
                      //       "Total Referrals: ${widget.userController.totalRefers}",
                      //       style: TextStyle(fontWeight: FontWeight.bold),
                      //     ),
                      //   ),
                      // ),
                    ],
                  );
          }),
          Expanded(
            child: GetX(
              init: UserKittyController(),
              initState: (state) {
                Future.delayed(Duration.zero, () async {
                  try {
                    await state.controller?.getReferkitties();
                    // ignore: avoid_print
                    print("Kitties loaded successfully");
                  } catch (e) {
                    // ignore: avoid_print
                    print("Error loading kitties: $e");
                    throw e;
                  }
                });
              },
              builder: (UserKittyController usercontroller) {
                if (widget.userController.kittiesLoading.isTrue) {
                  return Center(
                      child: CircularProgressIndicator(
                    color: Theme.of(context).colorScheme.primary,
                  ));
                }
                if (widget.userController.referkitties.isEmpty) {
                  return Center(
                    child: Text(
                      'no_referrals_found'.tr,
                      style: TextStyle(
                          color: Theme.of(context).textTheme.bodyLarge?.color),
                    ),
                  );
                } else if (widget.filteredKitties.isEmpty) {
                  return Padding(
                    padding: EdgeInsets.only(top: 15.h),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(
                          'kitty_not_found'.tr,
                          style: TextStyle(
                              color:
                                  Theme.of(context).textTheme.bodyLarge?.color),
                        ),
                      ],
                    ),
                  );
                } else if (widget.filteredKitties.isNotEmpty) {
                  return Container(
                    margin: const EdgeInsets.symmetric(
                        horizontal: 20, vertical: 20),
                    child: ListView.separated(
                      controller: widget.userController.scrollController,
                      physics: const BouncingScrollPhysics(),
                      separatorBuilder: (context, index) {
                        return SizedBox(
                          height: 24.h,
                        );
                      },
                      itemCount: widget.filteredKitties.length,
                      itemBuilder: (context, index) {
                        final kitty = widget.filteredKitties[index];

                        return ContributionKittyWidget(
                          kitty: kitty,
                        );
                      },
                    ),
                  );
                }

                return Text(
                  'unknown_error_occurred'.tr,
                  style: TextStyle(color: Theme.of(context).colorScheme.error),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

// ignore: must_be_immutable
class ContributionKittyWidget extends StatefulWidget {
  final Kitty kitty;

  const ContributionKittyWidget({
    super.key,
    required this.kitty,
  });

  @override
  State<ContributionKittyWidget> createState() =>
      _ContributionKittyWidgetState();
}

class _ContributionKittyWidgetState extends State<ContributionKittyWidget> {
  UserKittyController userController = Get.put(UserKittyController());
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 7.w,
        vertical: 2.h,
      ),
      decoration: AppDecoration.outlineIndigo.copyWith(
        borderRadius: BorderRadiusStyle.roundedBorder6,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(right: 4.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.symmetric(vertical: 7.h),
                    child: Text(
                      widget.kitty.title ?? "",
                      overflow: TextOverflow.ellipsis,
                      style: CustomTextStyles.labelMediumff545963,
                    ),
                  ),
                ),
                if (widget.kitty.kittyType == 0)
                  Text(
                    'contribution'.tr,
                    style: TextStyle(
                      fontWeight: FontWeight.w900,
                      color: appTheme.blueGray400,
                    ),
                  ),
                if (widget.kitty.kittyType == 4 || widget.kitty.kittyType == 5)
                  Text(
                    'chama'.tr,
                    style: TextStyle(
                      fontWeight: FontWeight.w900,
                      color: appTheme.gray900,
                    ),
                  ),
                if (widget.kitty.kittyType == 3)
                  Text(
                    'chama'.tr,
                    style: TextStyle(
                      fontWeight: FontWeight.w900,
                      color: appTheme.gray900,
                    ),
                  ),
              ],
            ),
          ),
          RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: 'created_by'.tr,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontStyle: FontStyle.italic,
                    color: isLight.value
                        ? const Color.fromARGB(255, 27, 28, 29)
                        : appTheme.whiteA700,
                  ),
                ),
                TextSpan(
                  text:
                      userController.maskString(widget.kitty.phoneNumber ?? ""),
                  style: CustomTextStyles.bodySmallGray900,
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(right: 4.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    RichText(
                      text: TextSpan(
                        children: [
                          TextSpan(
                            text: 'created_on'.tr,
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontStyle: FontStyle.italic,
                              color: isLight.value
                                  ? const Color.fromARGB(255, 27, 28, 29)
                                  : appTheme.whiteA700,
                            ),
                          ),
                          TextSpan(
                            text: DateFormat('MMM dd, yyyy').format(
                                widget.kitty.createdAt?.toLocal() ??
                                    DateTime.now()),
                            style: CustomTextStyles.bodySmallGray900,
                          ),
                        ],
                      ),
                    ),
                    RichText(
                      text: TextSpan(
                        children: [
                          TextSpan(
                            text: 'end_date'.tr,
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontStyle: FontStyle.italic,
                              color: isLight.value
                                  ? const Color.fromARGB(255, 27, 28, 29)
                                  : appTheme.whiteA700,
                            ),
                          ),
                          TextSpan(
                            text: DateTimeFormat.relative(
                                widget.kitty.endDate ?? DateTime.now(),
                                levelOfPrecision: 1,
                                prependIfBefore: 'ends_in'.tr,
                                ifNow: "Now",
                                appendIfAfter: 'ago'),
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: isLight.value
                                  ? const Color.fromARGB(255, 27, 28, 29)
                                  : appTheme.whiteA700,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                InkWell(
                  onTap: () {
                    Get.to(
                      () => Transactions(
                        kitty: widget.kitty,
                      ),
                    );
                  },
                  child: Text(
                    'earnings'.tr,
                    style: TextStyle(
                        color: Theme.of(context).colorScheme.secondary,
                        fontStyle: FontStyle.normal),
                  ),
                )
              ],
            ),
          ),
          SizedBox(height: 3.h),
        ],
      ),
    );
  }
}

class MerTransaction extends StatefulWidget {
  const MerTransaction({
    super.key,
  });

  @override
  State<MerTransaction> createState() => _MerTransactionState();
}

class _MerTransactionState extends State<MerTransaction> {
  final dateformat = DateFormat('EE, dd MMMM');
  UserKittyController userController = Get.put(UserKittyController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      //appBar: buildAppBar(context),
      body: Container(
        width: double.maxFinite,
        padding: EdgeInsets.symmetric(vertical: 16.h),
        child: Column(
          children: [
            Expanded(
              child: Container(
                height: 869.h,
                margin: EdgeInsets.symmetric(horizontal: 32.w),
                child: Align(
                  alignment: Alignment.center,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [_buildTransactionsList(context)],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionsList(BuildContext context) {
    return GetX(
      init: UserKittyController(),
      initState: (state) {
        Future.delayed(Duration.zero, () async {
          // try {
          await state.controller?.getMerchantTransactions(
              code: userController.usermerchant.value.merchantCode ?? 0);
          // } catch (e) {
          //   print("Error loading kitties: $e");
          //   throw e;
          // }
        });
      },
      builder: (UserKittyController controller) {
        if (controller.loadingTransactions.isTrue) {
          return SizedBox(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SpinKitDualRing(
                    color: Theme.of(context).colorScheme.primary,
                    lineWidth: 4.sp,
                    size: 40.0.sp,
                  ),
                  Text(
                    'loading'.tr,
                    style: TextStyle(
                      color: Theme.of(context).textTheme.bodyLarge?.color,
                      fontStyle: FontStyle.italic,
                    ),
                  )
                ],
              ),
            ),
          );
        } else if (controller.merchtransactions.isEmpty) {
          return Center(
            child: Text('you_have_no_earnings_yet'.tr),
          );
        } else if (controller.merchtransactions.isNotEmpty) {
          return Expanded(
            child: GroupedListView<TransactionModel, DateTime>(
              elements: controller.merchtransactions,
              sort: false,
              useStickyGroupSeparators: true,
              groupBy: (TransactionModel element) {
                DateTime date = element.createdAt!.toLocal();
                return DateTime(date.year, date.month, date.day);
              },
              stickyHeaderBackgroundColor:
                  Theme.of(context).scaffoldBackgroundColor,
              groupHeaderBuilder: (value) {
                final date = dateformat.format(value.createdAt!.toLocal());
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Text(
                    date,
                    style: const TextStyle(
                      // color: Theme.of(context).textTheme.titleMedium?.color,
                      fontWeight: FontWeight.bold,
                      fontSize: 15.0,
                    ),
                  ),
                );
              },
              itemBuilder: (_, TransactionModel item) {
                return TransactionItem(item: item);
              },
              separator: const Padding(
                padding: EdgeInsets.symmetric(vertical: 8.0),
              ),
            ),
          );
        } else {
          return Container();
        }
      },
    );
  }
}

class TransactionItem extends StatelessWidget {
  final TransactionModel item;

  const TransactionItem({
    super.key,
    required this.item,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 15.h, vertical: 16.h),
      decoration: AppDecoration.outlineGray
          .copyWith(borderRadius: BorderRadiusStyle.circleBorder22),
      child: SizedBox(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              decoration:
                  BoxDecoration(borderRadius: BorderRadiusStyle.roundedBorder6),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Padding(
                    padding: EdgeInsets.only(left: 8.h),
                    child: Container(
                      margin: EdgeInsets.only(top: 3.h, bottom: 2.h),
                      padding: EdgeInsets.all(7.h),
                      decoration: AppDecoration.fillAGray
                          .copyWith(shape: BoxShape.circle),
                      child: Padding(
                        padding: const EdgeInsets.all(6.0),
                        child: Text(
                          item.merchant?.merchantName != null &&
                                  item.merchant!.merchantName!.isNotEmpty
                              ? item.merchant!.merchantName![0]
                              : ' ',
                          style: TextStyle(
                            color: isLight.value ? Colors.white : Colors.black,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(left: 14.h),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(item.transactionRef ?? "",
                            style: CustomTextStyles.titleSmallIndigo500),
                        SizedBox(height: 7.h),
                      ],
                    ),
                  ),
                  const Spacer(),
                  Column(
                    children: [
                      Align(
                        alignment: Alignment.centerRight,
                        child: Text(
                          ' ${item.amount?.toStringAsFixed(2)}',
                          style: TextStyle(
                            color: Theme.of(context).colorScheme.primary,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ),
                      SizedBox(height: 2.h),
                      Opacity(
                        opacity: 0.4,
                        child: Text(
                            DateFormat.jm().format(item.createdAt!.toLocal()),
                            style: theme.textTheme.bodySmall),
                      ),
                    ],
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
