// settings_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/controllers/kitty_settings.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/controllers/user_ktty_controller.dart';
import 'package:onekitty_admin/features/login/screens/passwd_req_screen.dart';
import 'package:onekitty_admin/features/onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty_admin/utils/my_button.dart';

class KittySettingsScreen extends StatefulWidget {
  final int kittyId;
  const KittySettingsScreen({super.key, required this.kittyId});

  @override
  State<KittySettingsScreen> createState() => _KittySettingsScreenState();
}

class _KittySettingsScreenState extends State<KittySettingsScreen> {
  final controller = Get.put(SettingsController());
  final isActivating = false.obs;
  final isRetrying = false.obs;
  final maxAmountController = TextEditingController();
  final TextEditingController minAmountController = TextEditingController();
  final TextEditingController messageController = TextEditingController();
  final TextEditingController historyLimitController = TextEditingController();
  final paymentRefLabel = TextEditingController();
  final RxBool hasMembership = false.obs;
  final RxString historyStartDate = ''.obs;
  final RxBool hideNames = false.obs;
  final RxBool hideAmount = false.obs;
  final RxString groupBy = ''.obs;
  final showAdvancedSettings = false.obs;

  @override
  void initState() {
    super.initState();
    _fetchSettings();
  }

  Future<void> _fetchSettings() async {
    await controller.fetchSettings(widget.kittyId);
    // Update controllers with fetched data
    maxAmountController.text =
        controller.settings.value.maximumAmount?.toString() ?? "";
    paymentRefLabel.text =
        controller.settings.value.paymentRefLabel?.toString() ?? "";
    minAmountController.text =
        controller.settings.value.minimumAmount?.toString() ?? "";
    messageController.text = controller.settings.value.customMessage ?? "";
    historyLimitController.text =
        controller.settings.value.historyLimit?.toString() ?? '';
    hasMembership.value = controller.settings.value.hasMembership ?? false;
    historyStartDate.value = controller.settings.value.startDate != null
        ? (DateFormat('dd/MM/yyyy HH : mm a')
            .format(controller.settings.value.startDate!))
        : '';
    hideNames.value = controller.settings.value.hideNames ?? false;
    hideAmount.value = controller.settings.value.hideAmount ?? false;
    groupBy.value = controller.settings.value.groupBy ?? '';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      persistentFooterButtons: [
        Obx(() => Padding(
              padding: const EdgeInsets.all(8.0),
              child: SizedBox(
                width: double.infinity,
                child: MyButton(
                  showLoading: controller.isSubmitting.value,
                  onClick: () async {
                    final res = await controller.submitSettings({
                      'ID': controller.settings.value.id ?? widget.kittyId,
                      'kitty_id':
                          controller.settings.value.kittyId ?? widget.kittyId,
                      'minimum_amount':
                          num.tryParse(minAmountController.text), // use null
                      'maximum_amount': num.tryParse(
                          maxAmountController.text), // should be null
                      'hide_amount': hideAmount.value,
                      'hide_names': hideNames.value,
                      'history_limit': num.tryParse(historyLimitController
                          .text), // should be null if not present
                      'group_by': groupBy.value,
                      // 'beneficiary_split_config': splitConfig.value.toUpperCase(),
                      'start_date': historyStartDate.value != ''
                          ? DateFormat('dd/MM/yyyy HH : mm a')
                              .parse(historyStartDate.value)
                              .toUtc()
                              .toIso8601String()
                          : null,
                      'custom_message': messageController.text,
                      'has_membership': hasMembership.value,
                      'signatory_threshold': null, 
                      'payment_ref_label': hasMembership.value ? paymentRefLabel.text : null,
                    });
                    if (res) {
                      Navigator.pop(context);
                    }
                  },
                  label: 'submit'.tr,
                ),
              ),
            ))
      ],
      body: Obx(() {
        if (controller.isFetchingSettings.value) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        // if (controller.settings.value.id == null) {
        //   return Center(
        //     child: Column(
        //       mainAxisAlignment: MainAxisAlignment.center,
        //       children: [
        //         const Icon(Icons.settings, size: 48, color: Colors.grey),
        //         const SizedBox(height: 16),
        //         Text(
        //           'no_settings_found'.tr,
        //           style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        //         ),
        //         const SizedBox(height: 8),
        //         Text(
        //           'settings_created_when_submit'.tr,
        //           style: TextStyle(color: Colors.grey),
        //         ),
        //         const SizedBox(height: 16),
        //         Obx(() => ElevatedButton(
        //           onPressed: isRetrying.value ? null : () async {
        //             isRetrying.value = true;
        //             await controller.fetchSettings(widget.kittyId);
        //             isRetrying.value = false;
        //           },
        //           child: isRetrying.value
        //             ? const SizedBox(
        //                 width: 20,
        //                 height: 20,
        //                 child: CircularProgressIndicator(
        //                   strokeWidth: 2,
        //                   valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
        //                 ),
        //               )
        //             : Text('retry'.tr),
        //         )),
        //       ],
        //     ),
        //   );
        // }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomTextField(
                controller: minAmountController,
               labelText: 'minimum_amount'.tr,
                     suffixIcon: Tooltip(
                      showDuration: const Duration(seconds: 3),
                      triggerMode: TooltipTriggerMode.tap,
                      message: "minimum_amount_tooltip".tr,
                      child: const Icon(Icons.help),
                    ), 
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 16),
              CustomTextFieldText(
                controller: maxAmountController,
                  labelText: 'maximum_amount'.tr, 
                    suffixIcon: Tooltip(
                      showDuration: const Duration(seconds: 3),
                      triggerMode: TooltipTriggerMode.tap,
                      message: "maximum_amount_tooltip".tr,
                      child: const Icon(Icons.help),
                    ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 16),
              CustomTextField(
                controller: messageController,
                labelText: 'custom_thank_you_message'.tr,
                maxLength: 3, 
                  suffixIcon: Tooltip(
                    showDuration: const Duration(seconds: 3),
                    triggerMode: TooltipTriggerMode.tap,
                    message:
                        "custom_message_tooltip".tr,
                    child: const Icon(Icons.help),
                    
                ),
              ),
              const SizedBox(height: 16),
              Obx(
                () => Row(children: [
                  Text('hide_amount'.tr),
                  const Spacer(),
                  Switch(
                    value: hideAmount.value,
                    onChanged: (value) => hideAmount.value = value,
                  ),
                  Tooltip(
                      showDuration: const Duration(seconds: 3),
                      triggerMode: TooltipTriggerMode.tap,
                      message:
                          "hide_amount_tooltip".tr,
                      child: const Icon(Icons.help))
                ]),
              ),
              Obx(
                () => Column(
                  children: [
                    Row(children: [
                      Text('has_membership'.tr),
                      const Spacer(),
                      Switch(
                        value: hasMembership.value,
                        onChanged: (value) => hasMembership.value = value,
                      ),
                      Tooltip(
                        showDuration: const Duration(seconds: 3),
                        triggerMode: TooltipTriggerMode.tap,
                        message:
                            "has_membership_tooltip".tr,
                        child: const Icon(Icons.help),
                      )
                    ]),
                    if (hasMembership.value) ...[
                      const SizedBox(height: 8),
                      CustomTextField(
                        controller: paymentRefLabel,
                        labelText: 'membership_id_label'.tr,
                         hintText:
                              'membership_id_example'.tr,
                          suffixIcon: Tooltip(
                            showDuration: const Duration(seconds: 3),
                            triggerMode: TooltipTriggerMode.tap,
                            message:
                                "membership_id_tooltip".tr,
                            child: const Icon(Icons.help),
                          ),
                      ),
                      const SizedBox(height: 8),
                    ]
                  ],
                ),
              ),
              Obx(
                () => Row(children: [
                  Tooltip(
                    showDuration: const Duration(seconds: 3),
                    triggerMode: TooltipTriggerMode.tap,
                    message: "hide_individual_names_contribution_list".tr,
                    child: Text('hide_names'.tr),
                  ),
                  const Spacer(),
                  Switch(
                    value: hideNames.value,
                    onChanged: (value) => hideNames.value = value,
                  ),
                  Tooltip(
                      showDuration: const Duration(seconds: 3),
                      triggerMode: TooltipTriggerMode.tap,
                      message: "hide_names_tooltip".tr,
                      child: const Icon(Icons.help))
                ]),
              ),
              Obx(
                () => Row(children: [
                  Tooltip(
                    showDuration: const Duration(seconds: 3),
                    triggerMode: TooltipTriggerMode.tap,
                    message:
                        "group_transactions_contribution_list_phone_numbers".tr,
                    child: Text('group_by_account'.tr),
                  ),
                  const Spacer(),
                  Switch(
                    value: groupBy.value == "account",
                    onChanged: (value) =>
                        groupBy.value = value ? "account" : "",
                  ),
                  Tooltip(
                    showDuration: const Duration(seconds: 3),
                    triggerMode: TooltipTriggerMode.tap,
                    message:
                        "group_transactions_contribution_list_phone_numbers".tr,
                    child: const Icon(Icons.help),
                  ),
                ]),
              ),
              const SizedBox(height: 16),
              InkWell(
                onTap: () async {
                  DateTime? pickedDateTime = await showDatePicker(
                    context: context,
                    initialDate: DateTime.now(),
                    firstDate: DateTime(2000),
                    lastDate: DateTime.now(),
                  );

                  if (pickedDateTime != null) {
                    TimeOfDay? pickedTime = await showTimePicker(
                      context: context,
                      initialTime: TimeOfDay.now(),
                    );

                    if (pickedTime != null) {
                      DateTime finalDateTime = DateTime(
                        pickedDateTime.year,
                        pickedDateTime.month,
                        pickedDateTime.day,
                        pickedTime.hour,
                        pickedTime.minute,
                      );

                      String formattedDateTime =
                          DateFormat('dd/MM/yyyy HH : mm a')
                              .format(finalDateTime);

                      historyStartDate.value = formattedDateTime;
                    }
                  }
                },
                child: Obx(
                  () => InputDecorator(
                    decoration:   InputDecoration(
                        labelText: 'history_start_date'.tr,
                        // border: ThemeHelper.inputBoxDecoration(),
                        suffixIcon: Tooltip(
                            showDuration: const Duration(seconds: 3),
                            triggerMode: TooltipTriggerMode.tap,
                            message:
                                "history_start_date_tooltip".tr,
                            child: const Icon(Icons.help)),
                        suffix: const Icon(Icons.calendar_month)),
                    child: Text(historyStartDate.value),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              CustomTextField(
                controller: historyLimitController,
                keyboardType: const TextInputType.numberWithOptions(),
                labelText: 'history_limit'.tr,
                   suffixIcon: Tooltip(
                    showDuration: const Duration(seconds: 3),
                    triggerMode: TooltipTriggerMode.tap,
                    message:
                        "history_limit_tooltip".tr,
                    child: const Icon(Icons.help),
                  ),
                  suffix: SizedBox(
                    width: 60,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        GestureDetector(
                          onTap: () {
                            int val =
                                int.tryParse(historyLimitController.text) ?? 0;
                            historyLimitController.text = (val + 1).toString();
                          },
                          child: const Icon(
                            Icons.add,
                            size: 18,
                          ),
                        ),
                        const Expanded(child: VerticalDivider()),
                        GestureDetector(
                          onTap: () {
                            int val =
                                int.tryParse(historyLimitController.text) ?? 0;
                            if (val > 1) {
                              historyLimitController.text =
                                  (val - 1).toString();
                            }
                          },
                          child: const Icon(
                            Icons.remove,
                            size: 18,
                          ),
                        ),
                      ],
                    ),
                  ),
              
              ),
              // const SizedBox(height: 16),
              // CustomTextField(
              //   controller: tarrifWithdrawPercentageController,
              //   decoration: const InputDecoration(
              //     labelText: 'Tarrif Withdraw Percentage',
              //     helperText: 'This only accepts decimals',
              //     border: OutlineInputBorder(),
              //   ),
              //   keyboardType: TextInputType.number,
              // ),
              /* const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: splitConfig.value,
                hint: const Text('Select split config'),
                decoration: const InputDecoration(
                  labelText: 'Beneficiary split config',
                  border: OutlineInputBorder(),
                ),
                items: <String>['Percentage', 'Amount']
                    .map<DropdownMenuItem<String>>((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(value),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  if (newValue != null) {
                    splitConfig.value = newValue;
                  }
                },
              ), */
              const SizedBox(height: 16),

              TextButton(
                onPressed: () {
                  showAdvancedSettings(!showAdvancedSettings.value);
                },
                child: Text('advanced_settings'.tr),
              ),
              const SizedBox(height: 16),
              Obx(() => showAdvancedSettings.value
                  ? Column(children: [
                      Get.find<DataController>()
                                  .kitty
                                  .value
                                  .kittyStatus
                                  ?.toLowerCase() !=
                              'active'
                          ? Container(
                              decoration: BoxDecoration(
                                border:
                                    Border.all(color: Colors.green, width: 0.6),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: ListTile(
                                  leading: Obx(
                                    () => Container(
                                        height: 60,
                                        width: 60,
                                        decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(8),
                                            color: Colors.green.shade50),
                                        child: isActivating.value
                                            ? const CircularProgressIndicator(
                                                color: Colors.green,
                                              )
                                            : const Icon(Icons.key,
                                                color: Colors.green)),
                                  ),
                                  title: Text('activate_kitty'.tr,
                                      style: const TextStyle(
                                          color: Colors.green,
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold)),
                                  subtitle: const Text('',
                                      style: TextStyle(
                                          color: Colors.green,
                                          fontSize: 12,
                                          fontStyle: FontStyle.italic)),
                                  onTap: () async {
                                    isActivating(true);
                                    await controller.deactivateKitty({
                                      'kitty_id': widget.kittyId,
                                      'status': 0
                                    }).whenComplete(() {
                                      isActivating.value = false;
                                    });
                                  }))
                          : Container(
                              decoration: BoxDecoration(
                                border:
                                    Border.all(color: Colors.green, width: 0.6),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: ListTile(
                                  leading: Container(
                                      height: 60,
                                      width: 60,
                                      decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(8),
                                          color: Colors.red.shade50),
                                      child: const Icon(Icons.block,
                                          color: Colors.red)),
                                  title: Text('deactivate_kitty'.tr,
                                      style: const TextStyle(
                                          color: Colors.red,
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold)),
                                  subtitle: Text(
                                      'reactivate_kitty_message'.tr,
                                      style: const TextStyle(
                                          color: Colors.red,
                                          fontSize: 12,
                                          fontStyle: FontStyle.italic)),
                                  onTap: () {
                                    final isDeleting = false.obs;
                                    Get.dialog(AlertDialog(
                                      title: Text('delete_kitty'.tr),
                                      content: Column(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Text(
                                              'are_you_sure_deactivate'.tr),
                                          const SizedBox(height: 30),
                                          Row(children: [
                                            MyButton(
                                              label: 'cancel'.tr,
                                              width: 80.w,
                                              outlined: true,
                                              onClick: () {
                                                Get.back();
                                              },
                                            ),
                                            SizedBox(width: 6.w),
                                            Obx(
                                              () => MyButton(
                                                width: 150.w,
                                                showLoading: isDeleting.value,
                                                color: Colors.red,
                                                onClick: () async {
                                                  var isAuthenticated =
                                                      await Get.to(
                                                          () =>
                                                              AuthPasswdScreen(),
                                                          arguments: [false]);
                                                  if (isAuthenticated != null &&
                                                      isAuthenticated == true) {
                                                    isDeleting.value = true;
                                                    await controller
                                                        .deactivateKitty({
                                                      'kitty_id':
                                                          widget.kittyId,
                                                      'status': 1
                                                    }).whenComplete(() {
                                                      isDeleting.value = false;
                                                      Navigator.pop(context);
                                                      Navigator.pop(context);
                                                    });
                                                  }
                                                },
                                                label: 'deactivate'.tr,
                                              ),
                                            )
                                          ])
                                        ],
                                      ),
                                    ));
                                  }),
                            ),
                      const SizedBox(height: 200),
                    ])
                  : const SizedBox(height: 200))
            ],
          ),
        );
      }),
    );
  }
}
