import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/controllers/kitty_controller.dart';
import 'package:onekitty_admin/features/onekitty/screens/pages/contribution/controllers/user_ktty_controller.dart';
import 'package:onekitty_admin/helpers/colors.dart';
import 'package:onekitty_admin/helpers/extensions/text_styles.dart';
import 'package:onekitty_admin/features/onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty_admin/utils/custom_button.dart';
import 'package:onekitty_admin/utils/whatsapp_validator.dart';

class WhatsAppEditLink extends StatefulWidget {
  final int? kittyId;
  const WhatsAppEditLink({super.key, this.kittyId});

  @override
  State<WhatsAppEditLink> createState() => _WhatsAppEditLinkState();
}

class _WhatsAppEditLinkState extends State<WhatsAppEditLink> {
  final linkController = TextEditingController();
  final formKey = GlobalKey<FormState>();
  final kittyController = Get.put(KittyController());
  final dataController = Get.put(DataController());
  final box = GetStorage();
  bool isLoading = false;
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: Container(
          margin: const EdgeInsets.all(20),
          child: Form(
            key: formKey,
            child: Column(
              children: [
                Text(
                  "connect_whatsapp_group".tr,
                  style:
                      context.titleText?.copyWith(fontWeight: FontWeight.bold),
                ),
                Text(dataController.kitty.value.kitty?.title ?? ""),
                const SizedBox(
                  height: 30,
                ),
                Align(
                  alignment: Alignment.topLeft,
                  child: Text(
                    "whatsapp_group_link".tr,
                    style: context.titleText,
                  ),
                ),
                SizedBox(
                  height: 10.h,
                ),
                CustomTextField(
                  labelText: box.read("whatsapplink") == null
                      ? "enter_whatsapp_link".tr
                      : "${box.read("whatsapplink")}",
                  hintText: "enter_whatsapp_link_hint".tr,
                  //hintText: "https://chat.whatsapp.com/kqkeodbbfjNLKlqwo",
                  controller: linkController,
                  validator: (p0) {
                    if (p0!.isEmpty) {
                      return "provide_link".tr;
                    } else if (!WhatsAppValidator.isValidWhatsAppLink(p0)) {
                      return WhatsAppValidator.getValidationErrorMessage();
                    }
                    return null;
                  },
                ),
                const SizedBox(
                  height: 20,
                ),
                SizedBox(
                  height: 50,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      OutlinedButton(
                          onPressed: () {
                            Navigator.pop(context);
                          },
                          child: Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 12.0),
                            child: Text(
                              "back_button".tr,
                              style: context.dividerTextLarge
                                  ?.copyWith(color: AppColors.primary),
                            ),
                          )),
                      Obx(() => CustomKtButton(
                            width: 120.w,
                            isLoading: kittyController.isLinkloading.isTrue,
                            onPress: () async {
                              if (formKey.currentState!.validate()) {
                                if (await kittyController.joinGroup(
                                  context: context,
                                  id: widget.kittyId ?? dataController.kitty.value.kitty?.iD ?? 0,
                                  link: linkController.text.trim(),
                                )) {
                                  // Refresh WhatsApp group data before closing
                                 
                                  // Pop the current screen to go back to viewing page
                                  Navigator.pop(context);
                                }
                              }
                            },
                            btnText: "submit_button".tr,
                          ))
                    ],
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
