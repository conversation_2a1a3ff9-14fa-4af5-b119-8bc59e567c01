import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:universal_html/html.dart' as html;

/// Web-compatible file handler service for downloads and file operations
class WebFileHandlerService {
  /// Download a file on web platform
  static void downloadFile(Uint8List bytes, String filename, {String? mimeType}) {
    if (kIsWeb) {
      final blob = html.Blob([bytes], mimeType ?? 'application/octet-stream');
      final url = html.Url.createObjectUrlFromBlob(blob);
      final anchor = html.document.createElement('a') as html.AnchorElement
        ..href = url
        ..style.display = 'none'
        ..download = filename;
      html.document.body?.children.add(anchor);
      anchor.click();
      html.document.body?.children.remove(anchor);
      html.Url.revokeObjectUrl(url);
    }
  }

  /// Download a text file on web platform
  static void downloadTextFile(String content, String filename) {
    if (kIsWeb) {
      final bytes = utf8.encode(content);
      downloadFile(Uint8List.fromList(bytes), filename, mimeType: 'text/plain');
    }
  }

  /// Download a JSON file on web platform
  static void downloadJsonFile(Map<String, dynamic> data, String filename) {
    if (kIsWeb) {
      final content = jsonEncode(data);
      final bytes = utf8.encode(content);
      downloadFile(Uint8List.fromList(bytes), filename, mimeType: 'application/json');
    }
  }

  /// Download an Excel file on web platform
  static void downloadExcelFile(Uint8List bytes, String filename) {
    if (kIsWeb) {
      downloadFile(bytes, filename, mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    }
  }

  /// Download a PDF file on web platform
  static void downloadPdfFile(Uint8List bytes, String filename) {
    if (kIsWeb) {
      downloadFile(bytes, filename, mimeType: 'application/pdf');
    }
  }

  /// Download an image file on web platform
  static void downloadImageFile(Uint8List bytes, String filename, {String imageType = 'png'}) {
    if (kIsWeb) {
      downloadFile(bytes, filename, mimeType: 'image/$imageType');
    }
  }

  /// Check if running on web
  static bool get isWeb => kIsWeb;
}
